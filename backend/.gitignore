# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Go workspace file
go.work

# 环境变量文件
.env
.env.local
.env.production

# 日志文件
*.log
logs/

# IDE相关文件
.vscode/
.idea/
*.swp
*.swo

# 操作系统相关文件
.DS_Store
Thumbs.db

# 临时文件
tmp/
temp/

# 构建输出
dist/
build/

# Docker相关
.dockerignore

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存目录
.cache/

# 热重载配置
.air.toml 