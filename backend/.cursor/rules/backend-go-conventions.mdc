---
alwaysApply: false
---
# 后端 Go 语言开发规范与项目结构

本文档为后端 Go 项目提供了一套全面的开发规范和最佳实践，涵盖了从目录结构、代码风格到框架使用、再到数据库优化的各个方面。

## 1. 核心目录结构

```
bdb-backend/
├── cmd/                          # 应用程序入口 (main.go)
├── internal/                     # 私有应用代码
│   ├── api/                     # 应用程序层 (Gin)
│   │   ├── controller/          # HTTP处理层 (Handler)
│   │   ├── router/              # 路由配置
│   │   ├── middleware/          # 中间件
│   │   └── server.go            # 服务器配置
│   ├── service/                 # 业务逻辑层
│   ├── repository/              # 数据访问层 (GORM)
│   ├── model/                   # 数据模型 (Structs)
│   ├── types/                   # API请求/响应结构体
│   └── utils/                   # 通用工具
├── pkg/                          # 公共库代码 (可被外部项目引用)
│   ├── auth/                    # 认证 (JWT, password)
│   ├── cache/                   # 缓存 (Redis)
│   ├── database/                # 数据库
│   ├── logger/                  # 日志
│   ├── validator/               # 验证
│   └── response/                # 统一响应格式
├── configs/                      # 配置文件 (config.yaml)
├── migrations/                   # 数据库迁移脚本
├── deployments/                  # 部署配置 (Dockerfile, docker-compose.yml)
├── scripts/                      # 脚本
├── tests/                        # 测试
├── docs/                         # 文档
├── go.mod                        # Go模块
└── README.md                     # 项目说明
```

## 2. Go 语言通用编码规范

### 2.1 命名规范

- **包名 (Package Names)**: 使用简短、清晰、全小写且不含下划线的名称。例如：`controller`, `service`, `repository`。
- **变量名 (Variable Names)**: 使用小驼峰命名法 (camelCase)。例如：`userID`, `requestBody`。
- **导出标识符 (Exported Identifiers)**: 所有需要被外部包访问的变量、函数、结构体、接口等，必须使用大驼峰命名法 (PascalCase)。例如：`UserService`, `GetProfile`。
- **接口名 (Interface Names)**: 单一方法的接口建议以 "-er" 作为后缀。例如：`Reader`, `Writer`。对于包含多个方法的复杂接口（如服务层接口），可以直接命名为 `UserService`。
- **私有标识符 (Private Identifiers)**: 在包内私有的标识符使用小驼峰命名法。
- **文件名 (File Names)**: 控制器和服务层文件应使用下划线命名法，并以层标识符结尾。例如：`user_ctl.go`, `user_svc.go`。

### 2.2 代码格式化

- **强制使用 `goimports`**: 提交代码前，必须使用 `goimports` (或 `gofmt`) 进行格式化，以保证代码风格统一并自动管理 imports。

### 2.3 错误处理

- **错误是值 (Errors are values)**: 函数如果可能出错，应将 `error` 作为最后一个返回值。
- **立即处理错误**: 不要使用 `_` 忽略错误。收到错误后应立即检查和处理。
- **错误包装 (Error Wrapping)**: 在调用链路中向上传递错误时，应添加上下文信息，方便调试。使用 `fmt.Errorf("...: %w", err)` 来包装错误。
- **特定错误判断**: 使用 `errors.Is()` 来判断是否为特定错误（例如 `gorm.ErrRecordNotFound`），使用 `errors.As()` 来转换到特定的错误类型。

### 2.4 并发

- **使用 `context.Context`**: 在处理请求的整个生命周期中，应传递 `context.Context`，用于控制超时、取消操作和传递请求域的值（如 `request_id`）。`ctx` 应作为函数的第一个参数。
- **Goroutine 通信**: 优先使用 Channel 进行 Goroutine 之间的通信，而不是通过共享内存加锁的方式。

## 3. Gin 框架与分层架构

### 3.1 分层架构

- **严格分层**: 严格遵守 `controller` -> `service` -> `repository` 的分层架构。
  - `controller`: 负责解析 HTTP 请求，参数校验和调用 `service`。**禁止包含任何业务逻辑**。
  - `service`: 负责核心业务逻辑。
  - `repository`: 负责数据持久化操作。
  - `model`: 数据库表对应的Go结构体。
  - `types`: API的请求(Request)和响应(Response)数据结构体，统一存放于 `internal/types/` 目录。
- **依赖注入**: 项目使用 `google/wire` 管理各层之间的依赖关系。在修改或添加构造函数后，必须在后端项目根目录运行 `go generate ./...` 以更新依赖关系图。

### 3.2 路由

- **路由分组**: 使用 `router.Group()` 按业务模块对 API 进行分组。例如: `v1 := router.Group("/api/v1")`。
- **RESTful API**: 遵循 RESTful 风格设计 API 接口。

### 3.3 中间件 (Middleware)

- **核心中间件**: 必须包含用于日志记录、Panic 恢复 (`gin.Recovery`)、跨域 (CORS)、认证的中间件。
- **编写原则**: 中间件应职责单一，易于组合。

### 3.4 请求与响应

- **请求绑定与校验**:
  - 使用 `ctx.ShouldBindJSON()` 等方法将请求体绑定到专用的 `Request` 结构体。
  - 结构体验证应使用 `go-playground/validator` 库，并定义清晰的 `validate` tag。
- **统一响应格式**: 所有 API 必须返回统一的 JSON 结构，方便前端和客户端处理。
  ```json
  {
      "code": 0,
      "message": "success",
      "data": {}
  }
  ```
  - `code`: 业务状态码（0表示成功，非0表示各种错误）。
  - `message`: 对当前状态码的简要描述。
  - `data`: 实际返回的数据。

## 4. GORM 使用与高性能 SQL

### 4.1 GORM 规范

- **避免 `SELECT *`**: 严禁使用 `db.First(&user)` 而不指定字段。必须使用 `db.Select("id", "name", ...).First(&user)` 来明确指定所需字段，减少不必要的数据传输和内存占用。
- **N+1 问题**: 警惕并解决 N+1 查询问题。使用 `Preload` 进行预加载。
  ```go
  // Bad: N+1 query
  db.Find(&users)
  for _, user := range users {
    db.Model(&user).Association("Profile").Find(&user.Profile)
  }

  // Good: Eager Loading
  db.Preload("Profile").Find(&users)
  ```
- **事务 (Transaction)**: 多个写操作必须放在一个事务中执行，使用 `db.Transaction` 来确保数据一致性。
- **错误检查**: GORM 的每个操作（Create, Query, Update, Delete）都必须检查返回的 `error`。特别是查询操作，需要判断 `gorm.ErrRecordNotFound`。

### 4.2 高性能 SQL 实践

- **索引 (Indexing)**:
  - **查询驱动**: 为 `WHERE` 子句中频繁用作查询条件的列建立索引。
  - **外键索引**: 所有外键列必须建立索引。
  - **组合索引**: 如果查询条件经常涉及多个列，应考虑建立组合索引。注意索引列的顺序。
  - **`EXPLAIN` 分析**: 对于复杂或慢查询，必须使用 `EXPLAIN` 分析其执行计划，找出性能瓶颈并进行优化。
- **避免大事务**: 事务应尽可能简短，只包含必要的操作，以减少锁定的时间和范围，提高并发性能。
- **批量操作**: 对于大量数据的插入或更新，使用 `CreateInBatches` 等批量操作方法。

## 5. 配置与日志

### 5.1 配置管理

- **配置分离**: 使用 `Viper` 等库管理配置。将配置与代码分离，并通过配置文件（如 `config.yaml`）或环境变量加载。
- **环境隔离**: 为开发 (`dev`)、测试 (`test`)、生产 (`prod`) 环境提供不同的配置文件。
- **禁止硬编码**: 严禁在代码中硬编码任何敏感信息（如数据库密码、API Key）。

### 5.2 日志记录

- **结构化日志**: 使用 `Zerolog` 等库进行结构化日志记录 (JSON 格式)。
- **日志级别**: 正确使用日志级别 (`Debug`, `Info`, `Warn`, `Error`, `Fatal`)。生产环境一般设置为 `Info` 级别。
- **包含上下文**: 日志中应包含 `request_id`, `user_id` 等上下文信息，便于追踪和调试。
- **日志记录原则**:
  - 只在关键业务流程、错误捕获、异步任务等重要位置记录日志。
  - 依赖统一的日志中间件来处理常规请求日志。
  - 避免在 `controller` 层记录过多常规请求日志。错误应在 `service` 或 `repository` 层捕获并向上传递，或在中间件中统一处理。

## 6. 核心业务约定 (Core Business Conventions)

### 6.1 用户ID (UserID) 规范

- **统一类型 (Unified Type)**: 为了确保整个应用的一致性和避免类型转换错误，所有与用户ID (`UserID`) 相关的定义、参数和变量都**必须**使用 `uint` 类型。
- **应用范围 (Scope)**: 此规范适用于所有代码层级：
  - **`pkg/auth`**: JWT Claims (`UserID`) 必须是 `uint`。
  - **`internal/api/controller`**: 从 Gin Context 中获取和解析 `UserID` 时，必须转换为 `uint`。
  - **`internal/service`**: 所有服务层方法接收和处理的 `UserID` 必须是 `uint`。
  - **`internal/repository`**: 所有数据访问层方法以及 `model` 中定义的 `ID` 字段，应为 `uint`。
# 后端 Go 语言开发规范与项目结构

本文档为后端 Go 项目提供了一套全面的开发规范和最佳实践，涵盖了从目录结构、代码风格到框架使用、再到数据库优化的各个方面。

## 1. 核心目录结构

```
bdb-backend/
├── cmd/                          # 应用程序入口 (main.go)
├── internal/                     # 私有应用代码
│   ├── api/                     # 应用程序层 (Gin)
│   │   ├── controller/          # HTTP处理层 (Handler)
│   │   ├── router/              # 路由配置
│   │   ├── middleware/          # 中间件
│   │   └── server.go            # 服务器配置
│   ├── service/                 # 业务逻辑层
│   ├── repository/              # 数据访问层 (GORM)
│   ├── model/                   # 数据模型 (Structs)
│   ├── types/                   # API请求/响应结构体
│   └── utils/                   # 通用工具
├── pkg/                          # 公共库代码 (可被外部项目引用)
│   ├── auth/                    # 认证 (JWT, password)
│   ├── cache/                   # 缓存 (Redis)
│   ├── database/                # 数据库
│   ├── logger/                  # 日志
│   ├── validator/               # 验证
│   └── response/                # 统一响应格式
├── configs/                      # 配置文件 (config.yaml)
├── migrations/                   # 数据库迁移脚本
├── deployments/                  # 部署配置 (Dockerfile, docker-compose.yml)
├── scripts/                      # 脚本
├── tests/                        # 测试
├── docs/                         # 文档
├── go.mod                        # Go模块
└── README.md                     # 项目说明
```

## 2. Go 语言通用编码规范

### 2.1 命名规范

- **包名 (Package Names)**: 使用简短、清晰、全小写且不含下划线的名称。例如：`controller`, `service`, `repository`。
- **变量名 (Variable Names)**: 使用小驼峰命名法 (camelCase)。例如：`userID`, `requestBody`。
- **导出标识符 (Exported Identifiers)**: 所有需要被外部包访问的变量、函数、结构体、接口等，必须使用大驼峰命名法 (PascalCase)。例如：`UserService`, `GetProfile`。
- **接口名 (Interface Names)**: 单一方法的接口建议以 "-er" 作为后缀。例如：`Reader`, `Writer`。对于包含多个方法的复杂接口（如服务层接口），可以直接命名为 `UserService`。
- **私有标识符 (Private Identifiers)**: 在包内私有的标识符使用小驼峰命名法。
- **文件名 (File Names)**: 控制器和服务层文件应使用下划线命名法，并以层标识符结尾。例如：`user_ctl.go`, `user_svc.go`。

### 2.2 代码格式化

- **强制使用 `goimports`**: 提交代码前，必须使用 `goimports` (或 `gofmt`) 进行格式化，以保证代码风格统一并自动管理 imports。

### 2.3 错误处理

- **错误是值 (Errors are values)**: 函数如果可能出错，应将 `error` 作为最后一个返回值。
- **立即处理错误**: 不要使用 `_` 忽略错误。收到错误后应立即检查和处理。
- **错误包装 (Error Wrapping)**: 在调用链路中向上传递错误时，应添加上下文信息，方便调试。使用 `fmt.Errorf("...: %w", err)` 来包装错误。
- **特定错误判断**: 使用 `errors.Is()` 来判断是否为特定错误（例如 `gorm.ErrRecordNotFound`），使用 `errors.As()` 来转换到特定的错误类型。

### 2.4 并发

- **使用 `context.Context`**: 在处理请求的整个生命周期中，应传递 `context.Context`，用于控制超时、取消操作和传递请求域的值（如 `request_id`）。`ctx` 应作为函数的第一个参数。
- **Goroutine 通信**: 优先使用 Channel 进行 Goroutine 之间的通信，而不是通过共享内存加锁的方式。

## 3. Gin 框架与分层架构

### 3.1 分层架构

- **严格分层**: 严格遵守 `controller` -> `service` -> `repository` 的分层架构。
  - `controller`: 负责解析 HTTP 请求，参数校验和调用 `service`。**禁止包含任何业务逻辑**。
  - `service`: 负责核心业务逻辑。
  - `repository`: 负责数据持久化操作。
  - `model`: 数据库表对应的Go结构体。
  - `types`: API的请求(Request)和响应(Response)数据结构体，统一存放于 `internal/types/` 目录。
- **依赖注入**: 项目使用 `google/wire` 管理各层之间的依赖关系。在修改或添加构造函数后，必须在后端项目根目录运行 `go generate ./...` 以更新依赖关系图。

### 3.2 路由

- **路由分组**: 使用 `router.Group()` 按业务模块对 API 进行分组。例如: `v1 := router.Group("/api/v1")`。
- **RESTful API**: 遵循 RESTful 风格设计 API 接口。

### 3.3 中间件 (Middleware)

- **核心中间件**: 必须包含用于日志记录、Panic 恢复 (`gin.Recovery`)、跨域 (CORS)、认证的中间件。
- **编写原则**: 中间件应职责单一，易于组合。

### 3.4 请求与响应

- **请求绑定与校验**:
  - 使用 `ctx.ShouldBindJSON()` 等方法将请求体绑定到专用的 `Request` 结构体。
  - 结构体验证应使用 `go-playground/validator` 库，并定义清晰的 `validate` tag。
- **统一响应格式**: 所有 API 必须返回统一的 JSON 结构，方便前端和客户端处理。
  ```json
  {
      "code": 0,
      "message": "success",
      "data": {}
  }
  ```
  - `code`: 业务状态码（0表示成功，非0表示各种错误）。
  - `message`: 对当前状态码的简要描述。
  - `data`: 实际返回的数据。

## 4. GORM 使用与高性能 SQL

### 4.1 GORM 规范

- **避免 `SELECT *`**: 严禁使用 `db.First(&user)` 而不指定字段。必须使用 `db.Select("id", "name", ...).First(&user)` 来明确指定所需字段，减少不必要的数据传输和内存占用。
- **N+1 问题**: 警惕并解决 N+1 查询问题。使用 `Preload` 进行预加载。
  ```go
  // Bad: N+1 query
  db.Find(&users)
  for _, user := range users {
    db.Model(&user).Association("Profile").Find(&user.Profile)
  }

  // Good: Eager Loading
  db.Preload("Profile").Find(&users)
  ```
- **事务 (Transaction)**: 多个写操作必须放在一个事务中执行，使用 `db.Transaction` 来确保数据一致性。
- **错误检查**: GORM 的每个操作（Create, Query, Update, Delete）都必须检查返回的 `error`。特别是查询操作，需要判断 `gorm.ErrRecordNotFound`。

### 4.2 高性能 SQL 实践

- **索引 (Indexing)**:
  - **查询驱动**: 为 `WHERE` 子句中频繁用作查询条件的列建立索引。
  - **外键索引**: 所有外键列必须建立索引。
  - **组合索引**: 如果查询条件经常涉及多个列，应考虑建立组合索引。注意索引列的顺序。
  - **`EXPLAIN` 分析**: 对于复杂或慢查询，必须使用 `EXPLAIN` 分析其执行计划，找出性能瓶颈并进行优化。
- **避免大事务**: 事务应尽可能简短，只包含必要的操作，以减少锁定的时间和范围，提高并发性能。
- **批量操作**: 对于大量数据的插入或更新，使用 `CreateInBatches` 等批量操作方法。

## 5. 配置与日志

### 5.1 配置管理

- **配置分离**: 使用 `Viper` 等库管理配置。将配置与代码分离，并通过配置文件（如 `config.yaml`）或环境变量加载。
- **环境隔离**: 为开发 (`dev`)、测试 (`test`)、生产 (`prod`) 环境提供不同的配置文件。
- **禁止硬编码**: 严禁在代码中硬编码任何敏感信息（如数据库密码、API Key）。

### 5.2 日志记录

- **结构化日志**: 使用 `Zerolog` 等库进行结构化日志记录 (JSON 格式)。
- **日志级别**: 正确使用日志级别 (`Debug`, `Info`, `Warn`, `Error`, `Fatal`)。生产环境一般设置为 `Info` 级别。
- **包含上下文**: 日志中应包含 `request_id`, `user_id` 等上下文信息，便于追踪和调试。
- **日志记录原则**:
  - 只在关键业务流程、错误捕获、异步任务等重要位置记录日志。
  - 依赖统一的日志中间件来处理常规请求日志。
  - 避免在 `controller` 层记录过多常规请求日志。错误应在 `service` 或 `repository` 层捕获并向上传递，或在中间件中统一处理。

## 6. 核心业务约定 (Core Business Conventions)

### 6.1 用户ID (UserID) 规范

- **统一类型 (Unified Type)**: 为了确保整个应用的一致性和避免类型转换错误，所有与用户ID (`UserID`) 相关的定义、参数和变量都**必须**使用 `uint` 类型。
- **应用范围 (Scope)**: 此规范适用于所有代码层级：
  - **`pkg/auth`**: JWT Claims (`UserID`) 必须是 `uint`。
  - **`internal/api/controller`**: 从 Gin Context 中获取和解析 `UserID` 时，必须转换为 `uint`。
  - **`internal/service`**: 所有服务层方法接收和处理的 `UserID` 必须是 `uint`。
  - **`internal/repository`**: 所有数据访问层方法以及 `model` 中定义的 `ID` 字段，应为 `uint`。
