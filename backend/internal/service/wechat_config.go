package service

import (
	"bdb-backend/pkg/config"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
)

// NewWechatMiniProgram 创建微信小程序实例
func NewWechatMiniProgram(cfg *config.Config) (*miniProgram.MiniProgram, error) {
	miniConfig := &miniProgram.UserConfig{
		AppID:  cfg.WeChat.AppID,
		Secret: cfg.WeChat.AppSecret,

		HttpDebug: cfg.Server.Mode == "debug",
		Debug:     cfg.Server.Mode == "debug",
	}

	return miniProgram.NewMiniProgram(miniConfig)
}
