package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/pkg/centrifugo"
	"bdb-backend/pkg/config"

	"gorm.io/gorm"
)

// NotificationService 通知服务接口
type NotificationService interface {
	// 系统通知
	SendSystemNotification(ctx context.Context, req *SendSystemNotificationRequest) error
	GetUserNotifications(ctx context.Context, userID uint64, req *GetNotificationsRequest) ([]*model.Notification, int64, error)
	MarkNotificationAsRead(ctx context.Context, notificationID, userID uint64) error
	MarkAllNotificationsAsRead(ctx context.Context, userID uint64) error
	DeleteNotification(ctx context.Context, notificationID, userID uint64) error
	GetUnreadNotificationCount(ctx context.Context, userID uint64) (int64, error)

	// 离线消息推送
	PushOfflineMessage(ctx context.Context, req *PushOfflineMessageRequest) error

	// 在线状态管理
	UpdateUserOnlineStatus(ctx context.Context, userID uint64, isOnline bool) error
	GetUserOnlineStatus(ctx context.Context, userID uint64) (bool, error)

	// 群组通知
	SendRoomNotification(ctx context.Context, req *SendRoomNotificationRequest) error

	// 私聊通知
	SendPrivateNotification(ctx context.Context, req *SendPrivateNotificationRequest) error
}

// 通知类型
type NotificationType string

const (
	NotificationTypeSystem       NotificationType = "system"       // 系统通知
	NotificationTypeMessage      NotificationType = "message"      // 消息通知
	NotificationTypeFriend       NotificationType = "friend"       // 好友通知
	NotificationTypeRoom         NotificationType = "room"         // 群组通知
	NotificationTypeAnnouncement NotificationType = "announcement" // 公告通知
	NotificationTypeMention      NotificationType = "mention"      // @提醒
	NotificationTypeReply        NotificationType = "reply"        // 回复通知
)

// DTO结构体定义
type SendSystemNotificationRequest struct {
	UserID   uint64                 `json:"user_id"`
	UserIDs  []uint64               `json:"user_ids"`
	Title    string                 `json:"title" validate:"required,max=100"`
	Content  string                 `json:"content" validate:"required,max=1000"`
	Type     NotificationType       `json:"type" validate:"required"`
	Data     map[string]interface{} `json:"data"`
	URL      string                 `json:"url"`
	Level    string                 `json:"level" validate:"oneof=info warning error success"`
	ExpireAt *time.Time             `json:"expire_at"`
}

type GetNotificationsRequest struct {
	Page     int              `json:"page" validate:"min=1"`
	PageSize int              `json:"page_size" validate:"min=1,max=100"`
	Type     NotificationType `json:"type"`
	IsRead   *bool            `json:"is_read"`
}

type PushOfflineMessageRequest struct {
	UserID     uint64             `json:"user_id" validate:"required"`
	RoomID     uint64             `json:"room_id" validate:"required"`
	SenderID   uint64             `json:"sender_id" validate:"required"`
	Message    *model.ChatMessage `json:"message" validate:"required"`
	SenderInfo *model.User        `json:"sender_info"`
	RoomInfo   *model.ChatRoom    `json:"room_info"`
}

type SendRoomNotificationRequest struct {
	RoomID         uint64                 `json:"room_id" validate:"required"`
	Type           NotificationType       `json:"type" validate:"required"`
	Title          string                 `json:"title" validate:"required,max=100"`
	Content        string                 `json:"content" validate:"required,max=1000"`
	Data           map[string]interface{} `json:"data"`
	ExcludeUserIDs []uint64               `json:"exclude_user_ids"`
}

type SendPrivateNotificationRequest struct {
	FromUserID uint64                 `json:"from_user_id" validate:"required"`
	ToUserID   uint64                 `json:"to_user_id" validate:"required"`
	Type       NotificationType       `json:"type" validate:"required"`
	Title      string                 `json:"title" validate:"required,max=100"`
	Content    string                 `json:"content" validate:"required,max=1000"`
	Data       map[string]interface{} `json:"data"`
	URL        string                 `json:"url"`
}

// notificationService 通知服务实现
type notificationService struct {
	config           *config.Config
	notificationRepo repository.NotificationRepository
	userRepo         repository.UserRepository
	chatRepo         repository.ChatRepository
	centrifugo       *centrifugo.Client
	db               *gorm.DB
}

// NewNotificationService 创建通知服务
func NewNotificationService(
	cfg *config.Config,
	notificationRepo repository.NotificationRepository,
	userRepo repository.UserRepository,
	chatRepo repository.ChatRepository,
	centrifugo *centrifugo.Client,
	db *gorm.DB,
) NotificationService {
	return &notificationService{
		config:           cfg,
		notificationRepo: notificationRepo,
		userRepo:         userRepo,
		chatRepo:         chatRepo,
		centrifugo:       centrifugo,
		db:               db,
	}
}

// SendSystemNotification 发送系统通知
func (s *notificationService) SendSystemNotification(ctx context.Context, req *SendSystemNotificationRequest) error {
	var userIDs []uint64

	// 确定目标用户
	if req.UserID > 0 {
		userIDs = []uint64{req.UserID}
	} else if len(req.UserIDs) > 0 {
		userIDs = req.UserIDs
	} else {
		return fmt.Errorf("no target users specified")
	}

	// 序列化额外数据
	var dataJSON string
	if req.Data != nil {
		dataBytes, err := json.Marshal(req.Data)
		if err != nil {
			return fmt.Errorf("failed to marshal data: %w", err)
		}
		dataJSON = string(dataBytes)
	}

	// 为每个用户创建通知记录
	for _, userID := range userIDs {
		notification := &model.Notification{
			UserID:   userID,
			Type:     string(req.Type),
			Title:    req.Title,
			Content:  req.Content,
			Data:     dataJSON,
			URL:      req.URL,
			Level:    req.Level,
			IsRead:   false,
			ExpireAt: req.ExpireAt,
		}

		if err := s.notificationRepo.CreateNotification(ctx, notification); err != nil {
			log.Printf("Failed to create notification for user %d: %v", userID, err)
			continue
		}

		// 发送实时通知
		if err := s.sendRealtimeNotification(ctx, userID, notification); err != nil {
			log.Printf("Failed to send realtime notification to user %d: %v", userID, err)
		}
	}

	return nil
}

// GetUserNotifications 获取用户通知列表
func (s *notificationService) GetUserNotifications(ctx context.Context, userID uint64, req *GetNotificationsRequest) ([]*model.Notification, int64, error) {
	offset := (req.Page - 1) * req.PageSize
	return s.notificationRepo.ListNotifications(ctx, userID, repository.ListNotificationsFilter{
		Type:   string(req.Type),
		IsRead: req.IsRead,
		Offset: offset,
		Limit:  req.PageSize,
	})
}

// MarkNotificationAsRead 标记通知为已读
func (s *notificationService) MarkNotificationAsRead(ctx context.Context, notificationID, userID uint64) error {
	return s.notificationRepo.MarkNotificationAsRead(ctx, notificationID, userID)
}

// MarkAllNotificationsAsRead 标记所有通知为已读
func (s *notificationService) MarkAllNotificationsAsRead(ctx context.Context, userID uint64) error {
	return s.notificationRepo.MarkAllNotificationsAsRead(ctx, userID)
}

// DeleteNotification 删除通知
func (s *notificationService) DeleteNotification(ctx context.Context, notificationID, userID uint64) error {
	return s.notificationRepo.DeleteNotification(ctx, notificationID, userID)
}

// GetUnreadNotificationCount 获取未读通知数量
func (s *notificationService) GetUnreadNotificationCount(ctx context.Context, userID uint64) (int64, error) {
	return s.notificationRepo.GetUnreadNotificationCount(ctx, userID)
}

// PushOfflineMessage 推送离线消息
func (s *notificationService) PushOfflineMessage(ctx context.Context, req *PushOfflineMessageRequest) error {
	// 检查用户是否在线
	isOnline, err := s.GetUserOnlineStatus(ctx, req.UserID)
	if err != nil {
		log.Printf("Failed to check user online status: %v", err)
	}

	// 如果用户在线，不需要推送离线消息
	if isOnline {
		return nil
	}

	// 构建推送内容
	var title, content string

	if req.RoomInfo != nil && req.RoomInfo.Type == model.ChatRoomTypeGroup {
		// 群聊消息
		title = fmt.Sprintf("%s", req.RoomInfo.Name)
		if req.SenderInfo != nil {
			content = fmt.Sprintf("%s: %s", req.SenderInfo.Nickname, s.formatMessageContent(req.Message))
		} else {
			content = s.formatMessageContent(req.Message)
		}
	} else {
		// 私聊消息
		if req.SenderInfo != nil {
			title = req.SenderInfo.Nickname
			content = s.formatMessageContent(req.Message)
		} else {
			title = "新消息"
			content = s.formatMessageContent(req.Message)
		}
	}

	// 创建通知记录
	notificationData := map[string]interface{}{
		"room_id":    req.RoomID,
		"message_id": req.Message.ID,
		"sender_id":  req.SenderID,
		"type":       "offline_message",
	}

	notification := &SendSystemNotificationRequest{
		UserID:  req.UserID,
		Type:    NotificationTypeMessage,
		Title:   title,
		Content: content,
		Data:    notificationData,
		Level:   "info",
	}

	return s.SendSystemNotification(ctx, notification)
}

// UpdateUserOnlineStatus 更新用户在线状态
func (s *notificationService) UpdateUserOnlineStatus(ctx context.Context, userID uint64, isOnline bool) error {
	channel := centrifugo.GetUserStatusChannel(userID)

	statusData := map[string]interface{}{
		"user_id":   userID,
		"is_online": isOnline,
		"timestamp": time.Now().Unix(),
	}

	publishMsg := &centrifugo.PublishMessage{
		Channel: channel,
		Data:    statusData,
	}

	_, err := s.centrifugo.Publish(ctx, publishMsg)
	if err != nil {
		return fmt.Errorf("failed to publish user status: %w", err)
	}

	// 可以考虑将在线状态保存到Redis或数据库中
	// 这里只是通过Centrifugo发布状态更新

	return nil
}

// GetUserOnlineStatus 获取用户在线状态
func (s *notificationService) GetUserOnlineStatus(ctx context.Context, userID uint64) (bool, error) {
	channel := centrifugo.GetUserStatusChannel(userID)
	presence, err := s.centrifugo.GetPresence(ctx, channel)
	if err != nil {
		return false, fmt.Errorf("failed to get presence: %w", err)
	}

	return len(presence) > 0, nil
}

// SendRoomNotification 发送群组通知
func (s *notificationService) SendRoomNotification(ctx context.Context, req *SendRoomNotificationRequest) error {
	// 获取群组成员
	members, _, err := s.chatRepo.ListChatMembers(ctx, req.RoomID, repository.ListChatMembersFilter{
		Status: model.MemberStatusActive,
		Limit:  1000, // 假设群组不超过1000人
	})
	if err != nil {
		return fmt.Errorf("failed to get room members: %w", err)
	}

	// 构建目标用户列表
	var userIDs []uint64
	excludeMap := make(map[uint64]bool)
	for _, excludeID := range req.ExcludeUserIDs {
		excludeMap[excludeID] = true
	}

	for _, member := range members {
		if !excludeMap[member.UserID] {
			userIDs = append(userIDs, member.UserID)
		}
	}

	// 发送系统通知
	systemReq := &SendSystemNotificationRequest{
		UserIDs: userIDs,
		Type:    req.Type,
		Title:   req.Title,
		Content: req.Content,
		Data:    req.Data,
		Level:   "info",
	}

	if err := s.SendSystemNotification(ctx, systemReq); err != nil {
		return fmt.Errorf("failed to send system notification: %w", err)
	}

	// 发送实时群组通知
	channel := centrifugo.GetChatRoomChannel(req.RoomID)
	notificationData := map[string]interface{}{
		"type":      "room_notification",
		"title":     req.Title,
		"content":   req.Content,
		"data":      req.Data,
		"timestamp": time.Now().Unix(),
	}

	publishMsg := &centrifugo.PublishMessage{
		Channel: channel,
		Data:    notificationData,
	}

	if _, err := s.centrifugo.Publish(ctx, publishMsg); err != nil {
		log.Printf("Failed to publish room notification: %v", err)
	}

	return nil
}

// SendPrivateNotification 发送私人通知
func (s *notificationService) SendPrivateNotification(ctx context.Context, req *SendPrivateNotificationRequest) error {
	// 发送系统通知
	systemReq := &SendSystemNotificationRequest{
		UserID:  req.ToUserID,
		Type:    req.Type,
		Title:   req.Title,
		Content: req.Content,
		Data:    req.Data,
		URL:     req.URL,
		Level:   "info",
	}

	if err := s.SendSystemNotification(ctx, systemReq); err != nil {
		return fmt.Errorf("failed to send system notification: %w", err)
	}

	// 发送实时私人通知
	channel := centrifugo.GetNotificationChannel(req.ToUserID)
	notificationData := map[string]interface{}{
		"type":         "private_notification",
		"from_user_id": req.FromUserID,
		"title":        req.Title,
		"content":      req.Content,
		"data":         req.Data,
		"url":          req.URL,
		"timestamp":    time.Now().Unix(),
	}

	publishMsg := &centrifugo.PublishMessage{
		Channel: channel,
		Data:    notificationData,
	}

	if _, err := s.centrifugo.Publish(ctx, publishMsg); err != nil {
		log.Printf("Failed to publish private notification: %v", err)
	}

	return nil
}

// sendRealtimeNotification 发送实时通知
func (s *notificationService) sendRealtimeNotification(ctx context.Context, userID uint64, notification *model.Notification) error {
	channel := centrifugo.GetNotificationChannel(userID)

	var data interface{}
	if notification.Data != "" {
		if err := json.Unmarshal([]byte(notification.Data), &data); err != nil {
			log.Printf("Failed to unmarshal notification data: %v", err)
		}
	}

	notificationData := map[string]interface{}{
		"type":              "notification",
		"id":                notification.ID,
		"title":             notification.Title,
		"content":           notification.Content,
		"notification_type": notification.Type,
		"data":              data,
		"url":               notification.URL,
		"level":             notification.Level,
		"is_read":           notification.IsRead,
		"created_at":        notification.CreatedAt.Unix(),
		"expire_at":         nil,
	}

	if notification.ExpireAt != nil {
		notificationData["expire_at"] = notification.ExpireAt.Unix()
	}

	publishMsg := &centrifugo.PublishMessage{
		Channel: channel,
		Data:    notificationData,
	}

	_, err := s.centrifugo.Publish(ctx, publishMsg)
	return err
}

// formatMessageContent 格式化消息内容
func (s *notificationService) formatMessageContent(message *model.ChatMessage) string {
	switch message.Type {
	case model.MessageTypeText:
		if len(message.Content) > 50 {
			return message.Content[:50] + "..."
		}
		return message.Content
	case model.MessageTypeImage:
		return "[图片]"
	case model.MessageTypeVideo:
		return "[视频]"
	case model.MessageTypeAudio:
		return "[语音]"
	case model.MessageTypeFile:
		return "[文件]"
	case model.MessageTypeLocation:
		return "[位置]"
	case model.MessageTypeCard:
		return "[名片]"
	case model.MessageTypeEmoji:
		return "[表情]"
	case model.MessageTypeRecall:
		return "[消息已撤回]"
	default:
		return "[消息]"
	}
}

// CheckAndSendOfflineNotifications 检查并发送离线通知
func (s *notificationService) CheckAndSendOfflineNotifications(ctx context.Context) error {
	// 这个方法可以由定时任务调用，处理需要延迟发送的离线推送
	// 例如，用户离线超过一定时间后才发送推送通知

	// TODO: 实现具体的离线通知检查逻辑
	// 1. 查询最近一段时间内的未读消息
	// 2. 检查用户在线状态
	// 3. 对离线用户发送汇总通知

	return nil
}

// BatchSendNotifications 批量发送通知
func (s *notificationService) BatchSendNotifications(ctx context.Context, notifications []*SendSystemNotificationRequest) error {
	for _, req := range notifications {
		if err := s.SendSystemNotification(ctx, req); err != nil {
			log.Printf("Failed to send batch notification: %v", err)
		}
	}
	return nil
}

// GetNotificationStatistics 获取通知统计信息
func (s *notificationService) GetNotificationStatistics(ctx context.Context, userID uint64) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 未读通知数量
	unreadCount, err := s.GetUnreadNotificationCount(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get unread count: %w", err)
	}
	stats["unread_count"] = unreadCount

	// 各类型通知数量
	// TODO: 实现各类型通知的统计

	return stats, nil
}
