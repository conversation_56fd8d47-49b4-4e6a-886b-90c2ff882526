package service

import (
	"context"
	"errors"

	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
)

type UserService interface {
	GetProfile(ctx context.Context, userID uint) (*types.UserProfile, error)
	UpdateProfile(ctx context.Context, userID uint, req *types.UpdateProfileRequest) error
	UploadAvatar(ctx context.Context, userID uint, avatarURL string) error
	ChangePassword(ctx context.Context, userID uint, req *types.ChangePasswordRequest) error
}

type userService struct {
	userRepo repository.UserRepository
}

func NewUserService(userRepo repository.UserRepository) UserService {
	return &userService{
		userRepo: userRepo,
	}
}

// GetProfile 获取用户资料
func (s *userService) GetProfile(ctx context.Context, userID uint) (*types.UserProfile, error) {
	if userID == 0 {
		return nil, errors.New("invalid user ID")
	}

	user, err := s.userRepo.GetByID(uint(userID))
	if err != nil {
		return nil, err
	}

	if user == nil {
		return nil, errors.New("user not found")
	}

	return &types.UserProfile{
		UID:      user.UID,
		Phone:    user.Phone,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		Gender:   user.Gender,
		Source:   user.Source,
	}, nil
}

// UpdateProfile 更新用户资料
func (s *userService) UpdateProfile(ctx context.Context, userID uint, req *types.UpdateProfileRequest) error {
	if userID == 0 {
		return errors.New("invalid user ID")
	}

	user, err := s.userRepo.GetByID(uint(userID))
	if err != nil {
		return err
	}

	if user == nil {
		return errors.New("user not found")
	}

	// Dynamic update based on non-nil fields in the request
	if req.Nickname != nil {
		user.Nickname = *req.Nickname
	}
	if req.Avatar != nil {
		user.Avatar = *req.Avatar
	}
	if req.Gender != nil {
		user.Gender = *req.Gender
	}

	if err := s.userRepo.Update(user); err != nil {
		return err
	}

	return nil
}

// UploadAvatar 上传头像
func (s *userService) UploadAvatar(ctx context.Context, userID uint, avatarURL string) error {
	if userID == 0 {
		return errors.New("invalid user ID")
	}
	if avatarURL == "" {
		return errors.New("invalid avatar URL")
	}

	user, err := s.userRepo.GetByID(uint(userID))
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	user.Avatar = avatarURL

	if err := s.userRepo.Update(user); err != nil {
		return err
	}

	return nil
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, userID uint, req *types.ChangePasswordRequest) error {
	if userID == 0 {
		return errors.New("invalid user ID")
	}

	user, err := s.userRepo.GetByID(uint(userID))
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	// TODO: 验证旧密码
	// if !auth.CheckPasswordHash(req.OldPassword, user.Password) {
	// 	return errors.New("旧密码不正确")
	// }

	// hashedPassword, err := auth.HashPassword(req.NewPassword)
	// if err != nil {
	// 	logger.ErrorCtx(ctx, "Failed to hash new password", err, "user_id", userID)
	// 	return errors.New("密码加密失败")
	// }
	// user.Password = hashedPassword

	if err := s.userRepo.Update(user); err != nil {
		return err
	}

	return nil
}
