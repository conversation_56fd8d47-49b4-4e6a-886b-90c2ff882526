package service

import (
	"context"
	"errors"

	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/internal/utils"
	auth "bdb-backend/pkg/jwt"
	"bdb-backend/pkg/wechat"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// AuthService 认证服务接口
type AuthService interface {
	// Login 用户登录
	Login(ctx context.Context, req *types.LoginRequest) (*types.LoginResponse, error)
	// WechatLogin 微信快捷登录
	WechatLogin(ctx context.Context, req *types.WechatLoginRequest) (*types.LoginResponse, error)
	// SendSmsCode 发送短信验证码
	SendSmsCode(ctx context.Context, req *types.SendSmsCodeRequest) error
}

type authService struct {
	userRepo      repository.UserRepository
	jwtService    auth.JWTService
	wechatService wechat.AuthService
}

// NewAuthService 创建认证服务
func NewAuthService(userRepo repository.UserRepository, jwtService auth.JWTService, wechatService wechat.AuthService) AuthService {
	return &authService{
		userRepo:      userRepo,
		jwtService:    jwtService,
		wechatService: wechatService,
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, req *types.LoginRequest) (*types.LoginResponse, error) {
	// TODO: 验证短信验证码
	// 这里应该调用短信服务验证验证码

	// 查找或创建用户
	user, err := s.userRepo.GetByPhone(req.Phone)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，创建新用户
			user = &model.User{
				UID:      utils.GenerateUID(),
				Phone:    req.Phone,
				Nickname: "用户" + req.Phone[7:], // 使用手机号后4位作为默认昵称
				Source:   "sms",
			}
			if err := s.userRepo.Create(user); err != nil {
				log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to create user")
				return nil, errors.New("创建用户失败")
			}
		} else {
			log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to get user by phone")
			return nil, errors.New("获取用户信息失败")
		}
	}

	// 更新最后登录时间
	if err := s.userRepo.UpdateLastLoginAt(user.ID); err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update user last login time")
	}

	// 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(user.ID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	return &types.LoginResponse{
		Token: token,
		User: &types.UserInfo{
			UID:      user.UID,
			Phone:    user.Phone,
			Nickname: user.Nickname,
			Avatar:   user.Avatar,
			Source:   user.Source,
		},
	}, nil
}

// WechatLogin 微信快捷登录
func (s *authService) WechatLogin(ctx context.Context, req *types.WechatLoginRequest) (*types.LoginResponse, error) {
	// 1. 获取微信会话信息
	sessionInfo, err := s.wechatService.GetSessionInfo(ctx, req.LoginCode)
	if err != nil {
		log.Error().Err(err).Str("login_code", req.LoginCode).Msg("Failed to get wechat session info")
		return nil, errors.New("获取微信会话信息失败")
	}

	// 2. 获取用户手机号
	phoneInfo, err := s.wechatService.GetPhoneNumber(ctx, req.PhoneCode)
	if err != nil {
		log.Error().Err(err).Str("phone_code", req.PhoneCode).Msg("Failed to get wechat phone number")
		return nil, errors.New("获取手机号失败")
	}

	// 3. 查找或创建用户
	user, err := s.userRepo.GetByPhone(phoneInfo.PurePhoneNumber)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，创建新用户
			user = &model.User{
				UID:      utils.GenerateUID(),
				OpenID:   sessionInfo.OpenID,
				UnionID:  &sessionInfo.UnionID,
				Phone:    phoneInfo.PurePhoneNumber,
				Nickname: "微信用户" + phoneInfo.PurePhoneNumber[7:], // 使用手机号后4位作为默认昵称
				Source:   "mp-weixin",
			}
			if err := s.userRepo.Create(user); err != nil {
				log.Error().Err(err).Str("phone", phoneInfo.PurePhoneNumber).Str("openid", sessionInfo.OpenID).Msg("Failed to create wechat user")
				return nil, errors.New("创建用户失败")
			}
		} else {
			log.Error().Err(err).Str("phone", phoneInfo.PurePhoneNumber).Msg("Failed to get user by phone")
			return nil, errors.New("获取用户信息失败")
		}
	} else {
		// 用户已存在，更新微信信息
		user.OpenID = sessionInfo.OpenID
		if sessionInfo.UnionID != "" {
			user.UnionID = &sessionInfo.UnionID
		}
		// 如果来源不是微信，更新为微信
		if user.Source != "mp-weixin" {
			user.Source = "mp-weixin"
		}

		if err := s.userRepo.Update(user); err != nil {
			log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update user wechat info")
		}
	}

	// 4. 更新最后登录时间
	if err := s.userRepo.UpdateLastLoginAt(user.ID); err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update user last login time")
	}

	// 5. 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(user.ID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	return &types.LoginResponse{
		Token: token,
		User: &types.UserInfo{
			UID:      user.UID,
			Phone:    user.Phone,
			Nickname: user.Nickname,
			Avatar:   user.Avatar,
			Source:   user.Source,
		},
	}, nil
}

// SendSmsCode 发送短信验证码
func (s *authService) SendSmsCode(ctx context.Context, req *types.SendSmsCodeRequest) error {
	// TODO: 实现短信验证码发送逻辑
	// 这里应该调用短信服务提供商的API发送验证码
	// 并将验证码存储到Redis或数据库中，设置过期时间

	log.Info().Str("phone", req.Phone).Msg("SMS code send request")

	// 暂时返回成功，实际项目中需要集成短信服务
	return nil
}
