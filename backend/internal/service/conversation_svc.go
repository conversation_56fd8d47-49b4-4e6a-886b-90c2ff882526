package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

// ConversationService 会话服务接口
type ConversationService interface {
	// 会话管理
	GetOrCreateSingleConversation(ctx context.Context, userID1, userID2 uint) (*model.Conversation, error)
	GetOrCreateSystemConversation(ctx context.Context, userID uint) (*model.Conversation, error)
	GetConversationByID(ctx context.Context, conversationID uint) (*model.Conversation, error)

	// 会话列表
	GetUserConversations(ctx context.Context, req *types.GetConversationListRequest) (*types.GetConversationListResponse, error)

	// 会话操作
	DeleteConversation(ctx context.Context, req *types.DeleteConversationRequest) (*types.DeleteConversationResponse, error)
	PinConversation(ctx context.Context, req *types.PinConversationRequest) (*types.PinConversationResponse, error)
	MuteConversation(ctx context.Context, req *types.MuteConversationRequest) (*types.MuteConversationResponse, error)
}

// conversationService 会话服务实现
type conversationService struct {
	conversationRepo     repository.ConversationRepository
	userConversationRepo repository.UserConversationRepository
	messageRepo          repository.MessageRepository
	db                   *gorm.DB
}

// NewConversationService 创建会话服务
func NewConversationService(
	conversationRepo repository.ConversationRepository,
	userConversationRepo repository.UserConversationRepository,
	messageRepo repository.MessageRepository,
	db *gorm.DB,
) ConversationService {
	return &conversationService{
		conversationRepo:     conversationRepo,
		userConversationRepo: userConversationRepo,
		messageRepo:          messageRepo,
		db:                   db,
	}
}

// GetOrCreateSingleConversation 获取或创建单聊会话
func (s *conversationService) GetOrCreateSingleConversation(ctx context.Context, userID1, userID2 uint) (*model.Conversation, error) {
	if userID1 == userID2 {
		return nil, errors.New("不能和自己创建会话")
	}

	// 尝试获取现有会话
	conversation, err := s.conversationRepo.GetByUsers(ctx, userID1, userID2, model.ConversationTypeSingle)
	if err == nil {
		return conversation, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询会话失败: %w", err)
	}

	// 创建新会话
	conversation = model.CreateSingleConversation(userID1, userID2, userID1)

	// 开启事务
	tx := s.db.Begin()
	defer tx.Rollback()

	// 创建会话
	if err := s.conversationRepo.Create(ctx, conversation); err != nil {
		return nil, fmt.Errorf("创建会话失败: %w", err)
	}

	// 为两个用户创建会话状态
	now := time.Now()
	userConversations := []*model.UserConversation{
		{
			UserID:         userID1,
			ConversationID: conversation.ID,
			IsVisible:      true,
			LastActiveTime: now,
		},
		{
			UserID:         userID2,
			ConversationID: conversation.ID,
			IsVisible:      true,
			LastActiveTime: now,
		},
	}

	if err := s.userConversationRepo.BatchCreate(ctx, userConversations); err != nil {
		return nil, fmt.Errorf("创建用户会话状态失败: %w", err)
	}

	tx.Commit()
	return conversation, nil
}

// GetOrCreateSystemConversation 获取或创建系统通知会话
func (s *conversationService) GetOrCreateSystemConversation(ctx context.Context, userID uint) (*model.Conversation, error) {
	// 尝试获取现有系统会话
	conversation, err := s.conversationRepo.GetSystemConversation(ctx, userID)
	if err == nil {
		return conversation, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询系统会话失败: %w", err)
	}

	// 创建新的系统会话
	conversation = model.CreateSystemConversation(userID)

	// 开启事务
	tx := s.db.Begin()
	defer tx.Rollback()

	// 创建会话
	if err := s.conversationRepo.Create(ctx, conversation); err != nil {
		return nil, fmt.Errorf("创建系统会话失败: %w", err)
	}

	// 为用户创建会话状态
	userConversation := &model.UserConversation{
		UserID:         userID,
		ConversationID: conversation.ID,
		IsVisible:      true,
		LastActiveTime: time.Now(),
	}

	if err := s.userConversationRepo.Create(ctx, userConversation); err != nil {
		return nil, fmt.Errorf("创建用户会话状态失败: %w", err)
	}

	tx.Commit()
	return conversation, nil
}

// GetConversationByID 根据ID获取会话
func (s *conversationService) GetConversationByID(ctx context.Context, conversationID uint) (*model.Conversation, error) {
	return s.conversationRepo.GetByID(ctx, conversationID)
}

// GetUserConversations 获取用户会话列表
func (s *conversationService) GetUserConversations(ctx context.Context, req *types.GetConversationListRequest) (*types.GetConversationListResponse, error) {
	// 构建查询过滤器
	filter := repository.UserConversationFilter{
		IsVisible: true,
		Offset:    (req.Page - 1) * req.Size,
		Limit:     req.Size,
		OrderBy:   "last_active_time",
		Order:     "DESC",
	}

	// 获取用户会话状态列表
	userConversations, total, err := s.userConversationRepo.ListByUser(ctx, req.UserID, filter)
	if err != nil {
		return nil, fmt.Errorf("获取用户会话列表失败: %w", err)
	}

	if len(userConversations) == 0 {
		return &types.GetConversationListResponse{
			Conversations: []*types.ConversationInfo{},
			Total:         0,
			Page:          req.Page,
			PageSize:      req.Size,
		}, nil
	}

	// 提取会话ID列表
	conversationIDs := make([]uint, len(userConversations))
	for i, uc := range userConversations {
		conversationIDs[i] = uc.ConversationID
	}

	// 批量获取会话信息
	conversations := make(map[uint]*model.Conversation)
	for _, id := range conversationIDs {
		conv, err := s.conversationRepo.GetByID(ctx, id)
		if err != nil {
			continue // 跳过获取失败的会话
		}
		conversations[id] = conv
	}

	// 批量获取最后消息
	lastMessages, err := s.messageRepo.GetLastMessages(ctx, conversationIDs)
	if err != nil {
		// 记录错误但不中断流程
		lastMessages = make(map[uint]*model.Message)
	}

	// 构建响应数据
	conversationInfos := make([]*types.ConversationInfo, 0, len(userConversations))
	for _, uc := range userConversations {
		conv, exists := conversations[uc.ConversationID]
		if !exists {
			continue
		}

		// 计算未读数
		unreadCount, _ := s.messageRepo.CountUnreadMessages(ctx, req.UserID, uc.ConversationID, uc.LastReadMessageID)

		// 构建会话信息
		conversationInfo := &types.ConversationInfo{
			ID:             conv.ID,
			Type:           types.ConversationType(conv.Type),
			CreatedAt:      conv.CreatedAt,
			IsPinned:       uc.IsPinned,
			IsMuted:        uc.IsMuted,
			CustomName:     uc.CustomName,
			LastActiveTime: uc.LastActiveTime,
			UnreadCount:    int(unreadCount),
		}

		// 添加最后消息信息
		if lastMsg, exists := lastMessages[uc.ConversationID]; exists {
			conversationInfo.LastMessage = &types.MessageInfo{
				ID:          lastMsg.ID,
				MsgID:       lastMsg.MsgID,
				SenderID:    lastMsg.SenderID,
				Content:     lastMsg.Content,
				MessageType: types.ChatMessageType(lastMsg.MessageType),
				Status:      types.ChatMessageStatus(lastMsg.Status),
				IsRevoked:   lastMsg.IsRevoked,
				CreatedAt:   lastMsg.CreatedAt,
			}
		}

		conversationInfos = append(conversationInfos, conversationInfo)
	}

	return &types.GetConversationListResponse{
		Conversations: conversationInfos,
		Total:         total,
		Page:          req.Page,
		PageSize:      req.Size,
	}, nil
}

// DeleteConversation 删除会话
func (s *conversationService) DeleteConversation(ctx context.Context, req *types.DeleteConversationRequest) (*types.DeleteConversationResponse, error) {
	// 将用户的会话状态标记为不可见
	updates := map[string]interface{}{
		"is_visible": false,
		"is_deleted": true,
		"deleted_at": time.Now(),
	}

	err := s.userConversationRepo.Update(ctx, req.UserID, req.ConversationID, updates)
	if err != nil {
		return nil, fmt.Errorf("删除会话失败: %w", err)
	}

	return &types.DeleteConversationResponse{
		Success:   true,
		DeletedAt: time.Now(),
	}, nil
}

// PinConversation 置顶/取消置顶会话
func (s *conversationService) PinConversation(ctx context.Context, req *types.PinConversationRequest) (*types.PinConversationResponse, error) {
	err := s.userConversationRepo.SetPinned(ctx, req.UserID, req.ConversationID, req.IsPinned)
	if err != nil {
		return nil, fmt.Errorf("设置会话置顶状态失败: %w", err)
	}

	return &types.PinConversationResponse{
		Success:  true,
		IsPinned: req.IsPinned,
	}, nil
}

// MuteConversation 静音/取消静音会话
func (s *conversationService) MuteConversation(ctx context.Context, req *types.MuteConversationRequest) (*types.MuteConversationResponse, error) {
	err := s.userConversationRepo.SetMuted(ctx, req.UserID, req.ConversationID, req.IsMuted)
	if err != nil {
		return nil, fmt.Errorf("设置会话静音状态失败: %w", err)
	}

	return &types.MuteConversationResponse{
		Success: true,
		IsMuted: req.IsMuted,
	}, nil
}
