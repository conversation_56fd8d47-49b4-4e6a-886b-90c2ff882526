package router

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/api/middleware"
	"bdb-backend/pkg/config"

	"github.com/gin-gonic/gin"
)

func SetupRouter(
	cfg *config.Config,
	authCtl *controller.AuthController,
	userCtl *controller.UserController,
	jobCtl *controller.JobController,
	houseCtl *controller.HouseController,
	gigCtl *controller.GigController,
	datingCtl *controller.DatingController,
	messageCtl *controller.MessageController,
	paymentCtl *controller.PaymentController,
	fileCtl *controller.FileController,
	chatCtl *controller.ChatController,
	notificationCtl *controller.NotificationController,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	r := gin.New()

	// 全局中间件
	r.Use(middleware.Logger())
	r.Use(middleware.CORS())
	r.Use(gin.Recovery()) // 使用gin内置的recovery中间件

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.J<PERSON>(200, gin.H{"status": "ok"})
	})

	// API路由组
	v1 := r.Group("/api/v1")
	{
		// 认证路由（无需token验证）
		auth := v1.Group("/auth")
		{
			auth.POST("/wechat-login", authCtl.WechatLogin)
			auth.POST("/sms", authCtl.SendSmsCode)
			auth.POST("/refresh", authCtl.RefreshToken)
			auth.POST("/logout", authCtl.Logout)
		}

		// 需要认证的路由
		authorized := v1.Group("")
		// TODO: 添加认证中间件
		// authorized.Use(middleware.Auth())
		{
			// 用户相关
			users := authorized.Group("/users")
			{
				users.GET("/profile", userCtl.GetProfile)
				users.PUT("/profile", userCtl.UpdateProfile)
				users.POST("/avatar", userCtl.UploadAvatar)
				users.GET("/:user_id", userCtl.GetUserInfo)
				users.PUT("/password", userCtl.ChangePassword)
			}

			// 招聘相关
			jobs := authorized.Group("/jobs")
			{
				jobs.GET("", jobCtl.GetJob) // 统一使用GetJob作为获取职位的方法
				jobs.GET("/:id", jobCtl.GetJob)
				jobs.POST("", jobCtl.CreateJob)
			}

			// 房产相关
			houses := authorized.Group("/houses")
			{
				houses.GET("", houseCtl.GetHouse) // 统一使用GetHouse作为获取房源的方法
				houses.GET("/:id", houseCtl.GetHouse)
				houses.POST("", houseCtl.CreateHouse)
			}

			// 零工相关
			gigs := authorized.Group("/gigs")
			{
				gigs.GET("", gigCtl.GetGig) // 统一使用GetGig作为获取零工的方法
				gigs.GET("/:id", gigCtl.GetGig)
				gigs.POST("", gigCtl.CreateGig)
			}

			// 聊天相关（仅私聊功能）
			chat := authorized.Group("/chat")
			{
				// 会话管理
				chat.GET("/conversations", chatCtl.GetConversations)
				chat.POST("/conversations", chatCtl.CreateSingleConversation)
				chat.GET("/conversations/:id", chatCtl.GetConversationByID)
				chat.DELETE("/conversations/:id", chatCtl.DeleteConversation)
				chat.PUT("/conversations/:id/pin", chatCtl.PinConversation)
				chat.PUT("/conversations/:id/mute", chatCtl.MuteConversation)

				// 会话消息管理
				chat.GET("/conversations/:id/messages", messageCtl.GetMessages)

				// 消息操作
				messages := chat.Group("/messages")
				{
					messages.POST("", messageCtl.SendMessage)
					messages.PUT("/read", messageCtl.MarkMessagesAsRead)
					messages.DELETE("/:id/revoke", messageCtl.RevokeMessage)
				}
			}

			// 通知相关
			notifications := authorized.Group("/notifications")
			{
				notifications.POST("", notificationCtl.SendNotification)
				notifications.GET("", notificationCtl.GetNotifications)
				notifications.PUT("/:id/read", notificationCtl.MarkNotificationAsRead)
				notifications.PUT("/read-all", notificationCtl.MarkAllAsRead)
			}
		}
	}

	return r
}
