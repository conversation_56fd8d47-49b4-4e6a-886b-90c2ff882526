package router

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/api/middleware"
	"bdb-backend/pkg/config"

	"github.com/gin-gonic/gin"
)

func SetupRouter(
	cfg *config.Config,
	authCtl *controller.AuthController,
	userCtl *controller.UserController,
	jobCtl *controller.JobController,
	houseCtl *controller.HouseController,
	gigCtl *controller.GigController,
	datingCtl *controller.DatingController,
	messageCtl *controller.MessageController,
	paymentCtl *controller.PaymentController,
	fileCtl *controller.FileController,
	// chatCtl *controller.ChatController,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	r := gin.New()

	// 全局中间件
	r.Use(middleware.Logger())
	r.Use(middleware.CORS())
	r.Use(gin.Recovery()) // 使用gin内置的recovery中间件

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API路由组
	v1 := r.Group("/api/v1")
	{
		// 认证路由（无需token验证）
		auth := v1.Group("/auth")
		{
			auth.POST("/wechat-login", authCtl.WechatLogin)
			auth.POST("/sms", authCtl.SendSmsCode)
			auth.POST("/refresh", authCtl.RefreshToken)
			auth.POST("/logout", authCtl.Logout)
		}

		// 需要认证的路由
		authorized := v1.Group("")
		// TODO: 添加认证中间件
		// authorized.Use(middleware.Auth())
		{
			// 用户相关
			users := authorized.Group("/users")
			{
				users.GET("/profile", userCtl.GetProfile)
				users.PUT("/profile", userCtl.UpdateProfile)
				users.POST("/avatar", userCtl.UploadAvatar)
				users.GET("/:user_id", userCtl.GetUserInfo)
				users.PUT("/password", userCtl.ChangePassword)
			}

			// 招聘相关
			jobs := authorized.Group("/jobs")
			{
				jobs.GET("", jobCtl.GetJob) // 统一使用GetJob作为获取职位的方法
				jobs.GET("/:id", jobCtl.GetJob)
				jobs.POST("", jobCtl.CreateJob)
			}

			// 房产相关
			houses := authorized.Group("/houses")
			{
				houses.GET("", houseCtl.GetHouse) // 统一使用GetHouse作为获取房源的方法
				houses.GET("/:id", houseCtl.GetHouse)
				houses.POST("", houseCtl.CreateHouse)
			}

			// 零工相关
			gigs := authorized.Group("/gigs")
			{
				gigs.GET("", gigCtl.GetGig) // 统一使用GetGig作为获取零工的方法
				gigs.GET("/:id", gigCtl.GetGig)
				gigs.POST("", gigCtl.CreateGig)
			}

			/*
				// 聊天相关（仅私聊功能）
				chat := authorized.Group("/chat")
				{
					// 私聊管理
					private := chat.Group("/private")
					{
						private.POST("/:target_user_id", chatCtl.CreatePrivateChat)
						private.GET("/:target_user_id", chatCtl.GetPrivateChat)
					}

					// 消息管理
					messages := chat.Group("/messages")
					{
						messages.POST("", chatCtl.SendMessage)
						messages.POST("/:message_id/recall", chatCtl.RecallMessage)
						messages.DELETE("/:message_id", chatCtl.DeleteMessage)
						messages.POST("/:message_id/read", chatCtl.MarkMessageAsRead)
						messages.GET("/unread/count", chatCtl.GetUnreadCount)
					}

					// 消息历史
					chat.GET("/rooms/:room_id/messages", chatCtl.GetMessageHistory)

					// WebSocket连接
					ws := chat.Group("/ws")
					{
						ws.GET("/token", chatCtl.GetConnectionToken)
					}

					// 用户在线状态
					users := chat.Group("/users")
					{
						users.GET("/:user_id/status", chatCtl.GetUserOnlineStatus)
					}
				}
			*/
		}
	}

	return r
}
