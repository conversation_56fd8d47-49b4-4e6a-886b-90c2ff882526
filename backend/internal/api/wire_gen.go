// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/service"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/validator"
)

// Injectors from wire.go:

// BuildServer creates a new server with all its dependencies.
func BuildServer(cfg *config.Config) (*Server, error) {
	db := NewDB(cfg)
	cache := NewCache(cfg)
	userRepository := repository.NewUserRepository(db)
	jwtService := jwt.NewJWTService(cfg)
	wechatService, err := service.NewWechatService(cfg)
	if err != nil {
		return nil, err
	}
	authService := service.NewAuthService(userRepository, jwtService, wechatService)
	validatorValidator := validator.NewValidator()
	authController := controller.NewAuthController(authService, validatorValidator)
	userService := service.NewUserService(userRepository)
	userController := controller.NewUserController(userService, validatorValidator)
	jobController := controller.NewJobController(validatorValidator)
	houseController := controller.NewHouseController(validatorValidator)
	gigController := controller.NewGigController(validatorValidator)
	server := NewServer(cfg, db, cache, authController, userController, jobController, houseController, gigController)
	return server, nil
}

// wire.go:

// Aliases for external constructors
var (
	// NewCentrifugoClient = centrifugo.NewClient
	NewJWTService = jwt.NewJWTService
	NewValidator  = validator.NewValidator
)
