package middleware

import (
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/response"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates a new JWT authentication middleware.
func AuthMiddleware(jwtService jwt.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			response.Unauthorized(c, "Authorization header is missing")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			response.Unauthorized(c, "Invalid token format")
			c.Abort()
			return
		}

		tokenString := parts[1]
		claims, err := jwtService.ValidateUserToken(tokenString)
		if err != nil {
			response.Unauthorized(c, "Invalid or expired token")
			c.Abort()
			return
		}

		// Set user ID in context for downstream handlers
		c.<PERSON>("user_id", claims.UserID)

		c.Next()
	}
}
