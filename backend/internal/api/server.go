package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/api/router"
	"bdb-backend/pkg/cache"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/database"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server struct {
	router *gin.Engine
	config *config.Config
	db     *gorm.DB
	cache  cache.Cache
}

// NewDB creates a new database connection.
func NewDB(cfg *config.Config) *gorm.DB {
	return database.NewPostgresClient(cfg.Database)
}

// NewCache creates a new cache instance.
func NewCache(cfg *config.Config) cache.Cache {
	return cache.NewRedisCache(cfg.Redis)
}

// NewServer creates a new server instance.
func NewServer(
	cfg *config.Config,
	db *gorm.DB,
	cache cache.Cache,
	authCtl *controller.AuthController,
	userCtl *controller.UserController,
	chatCtl *controller.ChatController,
	messageCtl *controller.MessageController,
	notificationCtl *controller.NotificationController,
	jobCtl *controller.JobController,
	houseCtl *controller.HouseController,
	gigCtl *controller.GigController,
) *Server {
	// Controllers for features not yet fully implemented
	datingCtl := controller.NewDatingController()
	paymentCtl := controller.NewPaymentController()
	fileCtl := controller.NewFileController()

	r := router.SetupRouter(cfg, authCtl, userCtl, jobCtl, houseCtl, gigCtl, datingCtl, messageCtl, paymentCtl, fileCtl, chatCtl, notificationCtl)
	return &Server{
		router: r,
		config: cfg,
		db:     db,
		cache:  cache,
	}
}

func (s *Server) Run() error {
	return s.router.Run(":" + s.config.Server.Port)
}

func (s *Server) Close() error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
