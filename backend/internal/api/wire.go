//go:build wireinject
// +build wireinject

package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/service"

	// "bdb-backend/pkg/centrifugo"

	"bdb-backend/pkg/config"
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/validator"

	"github.com/google/wire"
)

// Aliases for external constructors
var (
	// NewCentrifugoClient = centrifugo.NewClient
	NewJWTService = jwt.NewJWTService
	NewValidator  = validator.NewValidator
)

// BuildServer creates a new server with all its dependencies.
func BuildServer(cfg *config.Config) (*Server, error) {
	wire.Build(
		// Providers for config, database, and cache
		NewDB,
		NewCache,

		// External services
		// NewCentrifugoClient,
		NewJWTService,
		NewValidator,

		// Provider sets for repository, service, and controller layers
		repository.ProviderSet,
		service.ProviderSet,
		controller.ProviderSet,

		// The final server provider
		NewServer,
	)
	return &Server{}, nil
}
