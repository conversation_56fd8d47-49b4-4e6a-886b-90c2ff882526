package controller

import (
	"bdb-backend/internal/types"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

type JobController struct {
	validator validator.Validator
	// jobService service.JobService // Uncomment when service is implemented
}

func NewJobController(validator validator.Validator /*, jobService service.JobService */) *JobController {
	return &JobController{
		validator: validator,
		// jobService: jobService,
	}
}

// CreateJob 创建职位
func (c *JobController) CreateJob(ctx *gin.Context) {
	var req types.CreateJobRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.jobService.CreateJob(ctx, userID, &req)

	response.OK(ctx, gin.H{
		"message": "创建职位成功 (占位符)",
	})
}

// GetJob 获取职位详情
func (c *JobController) GetJob(ctx *gin.Context) {
	jobID := ctx.Param("id")

	// TODO: c.jobService.GetJob(ctx, jobID)

	response.OK(ctx, gin.H{
		"job_id":  jobID,
		"message": "获取职位详情 (占位符)",
	})
}

// ListJobs 获取职位列表
func (c *JobController) ListJobs(ctx *gin.Context) {
	// TODO: var req types.ListJobsRequest
	// TODO: c.validator.ValidateQuery(ctx, &req)
	// TODO: c.jobService.ListJobs(ctx, &req)

	response.OK(ctx, gin.H{
		"message": "获取职位列表 (占位符)",
	})
}

// UpdateJob 更新职位
func (c *JobController) UpdateJob(ctx *gin.Context) {
	jobID := ctx.Param("id")
	_ = jobID // In case of no service call yet

	var req types.UpdateJobRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.jobService.UpdateJob(ctx, userID, jobID, &req)

	response.OK(ctx, gin.H{
		"job_id":  jobID,
		"message": "更新职位成功 (占位符)",
	})
}

// DeleteJob 删除职位
func (c *JobController) DeleteJob(ctx *gin.Context) {
	jobID := ctx.Param("id")

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.jobService.DeleteJob(ctx, userID, jobID)

	response.OK(ctx, gin.H{
		"job_id":  jobID,
		"message": "删除职位成功 (占位符)",
	})
}
