package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// UserController 用户控制器
type UserController struct {
	userService service.UserService
	validator   validator.Validator
}

// NewUserController 创建用户控制器
func NewUserController(userService service.UserService, validator validator.Validator) *UserController {
	return &UserController{
		userService: userService,
		validator:   validator,
	}
}

// GetProfile 获取用户资料
func (c *UserController) GetProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)

	profile, err := c.userService.GetProfile(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取用户资料失败", err, "user_id", userID)
		response.ServerErrorJSON(ctx, "获取用户资料失败")
		return
	}

	response.OK(ctx, profile)
}

// UpdateProfile 更新用户资料
func (c *UserController) UpdateProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.UpdateProfileRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	if err := c.userService.UpdateProfile(ctx, userID, &req); err != nil {
		logger.ErrorCtx(ctx, "更新用户资料失败", err, "user_id", userID)
		response.ServerErrorJSON(ctx, "更新用户资料失败")
		return
	}

	response.OK(ctx, gin.H{"message": "用户资料更新成功"})
}

// UploadAvatar 上传头像
func (c *UserController) UploadAvatar(ctx *gin.Context) {
	userID := GetUserID(ctx)

	file, err := ctx.FormFile("avatar")
	if err != nil {
		response.BadRequest(ctx, "未提供头像文件")
		return
	}

	err = c.userService.UploadAvatar(ctx, userID, file.Filename)
	if err != nil {
		logger.ErrorCtx(ctx, "上传头像失败", err, "user_id", userID, "filename", file.Filename)
		response.ServerErrorJSON(ctx, "上传头像失败")
		return
	}
	response.OK(ctx, nil)
}

// GetUserInfo 获取指定用户信息
func (c *UserController) GetUserInfo(ctx *gin.Context) {
	userID := GetUserID(ctx)
	userInfo, err := c.userService.GetProfile(ctx, userID)
	if err != nil {
		response.ServerErrorJSON(ctx, "获取用户信息失败")
		return
	}

	response.OK(ctx, userInfo)
}

// ChangePassword 修改密码
func (c *UserController) ChangePassword(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.ChangePasswordRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	if err := c.userService.ChangePassword(ctx, userID, &req); err != nil {
		logger.ErrorCtx(ctx, "修改密码失败", err, "user_id", userID)
		response.ServerErrorJSON(ctx, "修改密码失败")
		return
	}
	response.OK(ctx, gin.H{"message": "密码修改成功"})
}

// GetMyProfile 获取当前登录用户的个人资料
func (c *UserController) GetMyProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)
	if userID == 0 {
		return // GetUserID 内部已处理响应
	}

	profile, err := c.userService.GetProfile(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取用户资料失败", err, "user_id", userID)
		response.ServerErrorJSON(ctx, "获取用户资料失败")
		return
	}
	response.OK(ctx, profile)
}

// UpdateMyProfile 更新当前登录用户的个人资料
func (c *UserController) UpdateMyProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.UpdateProfileRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	if err := c.userService.UpdateProfile(ctx, userID, &req); err != nil {
		logger.ErrorCtx(ctx, "更新用户资料失败", err, "user_id", userID)
		response.ServerErrorJSON(ctx, "更新用户资料失败")
		return
	}
	response.OK(ctx, gin.H{"message": "用户资料更新成功"})
}

// GetMyFullProfile 获取当前登录用户的完整资料
// func (c *UserController) GetMyFullProfile(ctx *gin.Context) {
// 	userID := GetUserID(ctx)

// 	user, err := c.userService.GetFullProfile(ctx, userID)
// 	if err != nil {
// 		logger.ErrorCtx(ctx, "获取用户完整资料失败", err, "user_id", userID)
// 		response.ServerErrorJSON(ctx, "获取用户完整资料失败")
// 		return
// 	}

// 	response.OK(ctx, user)
// }

// // FollowUser 关注用户
// func (c *UserController) FollowUser(ctx *gin.Context) {
// 	userID := GetUserID(ctx)
// 	if userID == 0 {
// 		return
// 	}

// 	var req types.FollowRequest
// 	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
// 		response.BadRequest(ctx, err.Error())
// 		return
// 	}

// 	if err := c.userService.Follow(ctx, userID, req.TargetUserID); err != nil {
// 		logger.ErrorCtx(ctx, "关注用户失败", err, "user_id", userID, "target_user_id", req.TargetUserID)
// 		response.ServerErrorJSON(ctx, err.Error())
// 		return
// 	}
// 	response.OK(ctx, gin.H{"message": "操作成功"})
// }

// // GetUserByID 获取指定用户的公开信息
// func (c *UserController) GetUserByID(ctx *gin.Context) {
// 	targetUserIDStr := ctx.Param("user_id")
// 	targetUserID, err := strconv.ParseUint(targetUserIDStr, 10, 32)
// 	if err != nil {
// 		response.BadRequest(ctx, "无效的用户ID")
// 		return
// 	}

// 	userInfo, err := c.userService.GetProfile(ctx, uint(targetUserID))
// 	if err != nil {
// 		logger.ErrorCtx(ctx, "获取用户信息失败", err, "target_user_id", targetUserID)
// 		response.ServerErrorJSON(ctx, "获取用户信息失败")
// 		return
// 	}
// 	response.OK(ctx, userInfo)
// }

// // UpdateLastLoginAt 更新用户最后登录时间
// func (c *UserController) UpdateLastLoginAt(ctx *gin.Context) {
// 	userID := GetUserID(ctx)
// 	if userID == 0 {
// 		return
// 	}

// 	if err := c.userService.UpdateLastLoginAt(ctx, userID); err != nil {
// 		response.ServerErrorJSON(ctx, "Failed to update last login time")
// 		return
// 	}
// 	response.OK(ctx, nil)
// }
