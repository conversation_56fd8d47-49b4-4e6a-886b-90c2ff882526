package controller

import (
	"strconv"

	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

func GetUserID(ctx *gin.Context) uint {
	val, ok := ctx.Get("user_id")
	if !ok {
		logger.ErrorCtx(ctx, "user_id not found in context", nil)
		response.Unauthorized(ctx, "user_id not found in context")
		return 0
	}

	// 支持多种类型的转换以保持兼容性
	switch v := val.(type) {
	case uint:
		return v
	case uint64:
		return uint(v)
	case int64:
		if v < 0 {
			response.Unauthorized(ctx, "无效的用户ID")
			return 0
		}
		return uint(v)
	case int:
		if v < 0 {
			response.Unauthorized(ctx, "无效的用户ID")
			return 0
		}
		return uint(v)
	case float64:
		if v < 0 {
			response.Unauthorized(ctx, "无效的用户ID")
			return 0
		}
		return uint(v)
	default:
		logger.ErrorCtx(ctx, "user_id type conversion failed", nil, "type", v)
		response.Unauthorized(ctx, "用户ID类型错误")
		return 0
	}
}

// ParseUintParam 解析URL参数为uint类型
func ParseUintParam(ctx *gin.Context, paramName string) uint {
	paramStr := ctx.Param(paramName)
	paramVal, err := strconv.ParseUint(paramStr, 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的参数: "+paramName)
		return 0
	}
	return uint(paramVal)
}
