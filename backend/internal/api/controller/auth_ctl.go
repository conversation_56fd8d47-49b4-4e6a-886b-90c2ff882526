package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	authService service.AuthService
	validator   validator.Validator
}

// NewAuthController 创建新的认证控制器
func NewAuthController(authService service.AuthService, validator validator.Validator) *AuthController {
	return &AuthController{
		authService: authService,
		validator:   validator,
	}
}

// WechatLogin 微信登录
func (c *AuthController) WechatLogin(ctx *gin.Context) {
	var req types.WechatLoginRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		logger.ErrorCtx(ctx, "微信登录参数验证失败", err)
		response.BadRequest(ctx, err.Error())
		return
	}

	resp, err := c.authService.WechatLogin(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "微信登录失败", err)
		response.Unauthorized(ctx, err.Error())
		return
	}
	response.OK(ctx, resp)
}

// Login 短信登录
func (c *AuthController) Login(ctx *gin.Context) {
	var req types.LoginRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		logger.ErrorCtx(ctx, "短信登录参数验证失败", err)
		response.BadRequest(ctx, err.Error())
		return
	}

	resp, err := c.authService.Login(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "短信登录失败", err)
		response.Unauthorized(ctx, err.Error())
		return
	}
	response.OK(ctx, resp)
}

// SendSmsCode 发送短信验证码
func (c *AuthController) SendSmsCode(ctx *gin.Context) {
	var req types.SendSmsCodeRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		logger.ErrorCtx(ctx, "发送短信验证码参数验证失败", err)
		response.BadRequest(ctx, err.Error())
		return
	}

	if err := c.authService.SendSmsCode(ctx, &req); err != nil {
		logger.ErrorCtx(ctx, "发送短信验证码失败", err)
		response.ServerErrorJSON(ctx, err.Error())
		return
	}
	response.OK(ctx, nil)
}

// Logout 用户登出
func (c *AuthController) Logout(ctx *gin.Context) {
	// TODO: 在AuthService中实现Logout方法
	/*
		userID, ok := ctx.Get("user_id")
		if !ok {
			response.Unauthorized(ctx, "用户未登录")
			return
		}

		// Logout 方法需要一个 string 类型的 userID
		if err := c.authService.Logout(ctx, userID.(string)); err != nil {
			logger.ErrorCtx(ctx, "用户登出失败", err, "user_id", userID)
			response.ServerErrorJSON(ctx, err.Error())
			return
		}
	*/
	response.OK(ctx, gin.H{"message": "登出功能待实现"})
}

// RefreshToken 刷新token
func (c *AuthController) RefreshToken(ctx *gin.Context) {
	// TODO: 在AuthService中实现RefreshToken方法
	/*
		userID, ok := ctx.Get("user_id")
		if !ok {
			response.Unauthorized(ctx, "用户未登录")
			return
		}

		resp, err := c.authService.RefreshToken(ctx, userID.(string))
		if err != nil {
			logger.ErrorCtx(ctx, "token刷新失败", err, "user_id", userID)
			response.Unauthorized(ctx, err.Error())
			return
		}
		response.OK(ctx, resp)
	*/
	response.OK(ctx, gin.H{"message": "刷新token功能待实现"})
}
