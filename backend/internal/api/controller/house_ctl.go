package controller

import (
	"bdb-backend/internal/types"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

type HouseController struct {
	validator validator.Validator
	// houseService service.HouseService // Uncomment when service is implemented
}

func NewHouseController(validator validator.Validator /*, houseService service.HouseService */) *HouseController {
	return &HouseController{
		validator: validator,
		// houseService: houseService,
	}
}

// CreateHouse 创建房源
func (c *HouseController) CreateHouse(ctx *gin.Context) {
	var req types.CreateHouseRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.houseService.CreateHouse(ctx, userID, &req)

	response.OK(ctx, gin.H{
		"message": "创建房源成功 (占位符)",
	})
}

// GetHouse 获取房源详情
func (c *HouseController) GetHouse(ctx *gin.Context) {
	houseID := ctx.Param("id")

	// TODO: c.houseService.GetHouse(ctx, houseID)

	response.OK(ctx, gin.H{
		"house_id": houseID,
		"message":  "获取房源详情 (占位符)",
	})
}

// ListHouses 获取房源列表
func (c *HouseController) ListHouses(ctx *gin.Context) {
	// TODO: var req types.ListHousesRequest
	// TODO: c.validator.ValidateQuery(ctx, &req)
	// TODO: c.houseService.ListHouses(ctx, &req)

	response.OK(ctx, gin.H{
		"message": "获取房源列表 (占位符)",
	})
}

// UpdateHouse 更新房源
func (c *HouseController) UpdateHouse(ctx *gin.Context) {
	houseID := ctx.Param("id")
	_ = houseID // In case of no service call yet

	var req types.UpdateHouseRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.houseService.UpdateHouse(ctx, userID, houseID, &req)

	response.OK(ctx, gin.H{
		"house_id": houseID,
		"message":  "更新房源成功 (占位符)",
	})
}

// DeleteHouse 删除房源
func (c *HouseController) DeleteHouse(ctx *gin.Context) {
	houseID := ctx.Param("id")

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.houseService.DeleteHouse(ctx, userID, houseID)

	response.OK(ctx, gin.H{
		"house_id": houseID,
		"message":  "删除房源成功 (占位符)",
	})
}
