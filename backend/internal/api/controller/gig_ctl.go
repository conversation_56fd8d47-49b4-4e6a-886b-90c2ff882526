package controller

import (
	"bdb-backend/internal/types"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

type GigController struct {
	validator validator.Validator
	// gigService service.GigService // Uncomment when service is implemented
}

func NewGigController(validator validator.Validator /*, gigService service.GigService */) *GigController {
	return &GigController{
		validator: validator,
		// gigService: gigService,
	}
}

// CreateGig 创建零工
func (c *GigController) CreateGig(ctx *gin.Context) {
	var req types.CreateGigRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.gigService.CreateGig(ctx, userID, &req)

	response.OK(ctx, gin.H{
		"message": "创建零工成功 (占位符)",
	})
}

// GetGig 获取零工详情
func (c *GigController) GetGig(ctx *gin.Context) {
	gigID := ctx.Param("id")

	// TODO: c.gigService.GetGig(ctx, gigID)

	response.OK(ctx, gin.H{
		"gig_id":  gigID,
		"message": "获取零工详情 (占位符)",
	})
}

// ListGigs 获取零工列表
func (c *GigController) ListGigs(ctx *gin.Context) {
	// TODO: var req types.ListGigsRequest
	// TODO: c.validator.ValidateQuery(ctx, &req)
	// TODO: c.gigService.ListGigs(ctx, &req)

	response.OK(ctx, gin.H{
		"message": "获取零工列表 (占位符)",
	})
}

// UpdateGig 更新零工
func (c *GigController) UpdateGig(ctx *gin.Context) {
	gigID := ctx.Param("id")
	_ = gigID // In case of no service call yet

	var req types.UpdateGigRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.gigService.UpdateGig(ctx, userID, gigID, &req)

	response.OK(ctx, gin.H{
		"gig_id":  gigID,
		"message": "更新零工成功 (占位符)",
	})
}

// DeleteGig 删除零工
func (c *GigController) DeleteGig(ctx *gin.Context) {
	gigID := ctx.Param("id")

	// TODO: userID, _ := getUserIDFromContext(ctx)
	// TODO: c.gigService.DeleteGig(ctx, userID, gigID)

	response.OK(ctx, gin.H{
		"gig_id":  gigID,
		"message": "删除零工成功 (占位符)",
	})
}
