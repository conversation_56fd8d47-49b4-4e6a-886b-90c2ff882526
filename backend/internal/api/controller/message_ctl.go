package controller

import (
	"bdb-backend/pkg/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

type MessageController struct {
	// messageService service.MessageService
}

func NewMessageController() *MessageController {
	return &MessageController{}
}

// GetConversations 获取会话列表
func (c *MessageController) GetConversations(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "获取会话列表",
	}))
}

// GetMessages 获取消息列表
func (c *MessageController) GetMessages(ctx *gin.Context) {
	conversationID := ctx.Param("id")
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"conversation_id": conversationID,
		"message":         "获取消息列表",
	}))
}

// SendMessage 发送消息
func (c *MessageController) SendMessage(ctx *gin.Context) {
	conversationID := ctx.Param("id")
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"conversation_id": conversationID,
		"message":         "发送消息",
	}))
}
