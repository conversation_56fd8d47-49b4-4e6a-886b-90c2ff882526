package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// MessageController 消息控制器
type MessageController struct {
	messageService service.MessageService
	validator      validator.Validator
}

// NewMessageController 创建消息控制器
func NewMessageController(messageService service.MessageService, validator validator.Validator) *MessageController {
	return &MessageController{
		messageService: messageService,
		validator:      validator,
	}
}

// SendMessage 发送消息
func (c *MessageController) SendMessage(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.SendMessageRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	req.SenderID = userID

	resp, err := c.messageService.SendMessage(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "发送消息失败", err, "user_id", userID, "receiver_id", req.ReceiverID)
		response.ServerErrorJSON(ctx, "发送消息失败")
		return
	}

	response.OK(ctx, resp)
}

// GetMessages 获取消息列表
func (c *MessageController) GetMessages(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	var req types.GetMessageListRequest
	if err := c.validator.ValidateQuery(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}
	if req.Size > 100 {
		req.Size = 100 // 限制最大页面大小
	}

	req.ConversationID = conversationID
	req.UserID = userID

	resp, err := c.messageService.GetMessageList(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "获取消息列表失败", err, "user_id", userID, "conversation_id", conversationID)
		response.ServerErrorJSON(ctx, "获取消息列表失败")
		return
	}

	response.OK(ctx, resp)
}

// MarkMessagesAsRead 标记消息为已读
func (c *MessageController) MarkMessagesAsRead(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.MarkMessagesAsReadRequest
	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	req.UserID = userID

	resp, err := c.messageService.MarkMessagesAsRead(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "标记消息已读失败", err, "user_id", userID, "conversation_id", req.ConversationID)
		response.ServerErrorJSON(ctx, "标记消息已读失败")
		return
	}

	response.OK(ctx, resp)
}

// RevokeMessage 撤回消息
func (c *MessageController) RevokeMessage(ctx *gin.Context) {
	userID := GetUserID(ctx)

	messageID := ParseUintParam(ctx, "id")
	if messageID == 0 {
		return
	}

	req := &types.RevokeMessageRequest{
		UserID:    userID,
		MessageID: messageID,
	}

	resp, err := c.messageService.RevokeMessage(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "撤回消息失败", err, "user_id", userID, "message_id", messageID)
		response.ServerErrorJSON(ctx, "撤回消息失败")
		return
	}

	response.OK(ctx, resp)
}
