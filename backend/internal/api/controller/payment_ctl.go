package controller

import (
	"bdb-backend/pkg/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

type PaymentController struct {
	// paymentService service.PaymentService
}

func NewPaymentController() *PaymentController {
	return &PaymentController{}
}

// WechatPay 微信支付
func (c *PaymentController) WechatPay(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "微信支付",
	}))
}

// AlipayPay 支付宝支付
func (c *PaymentController) AlipayPay(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "支付宝支付",
	}))
}

// PaymentNotify 支付回调
func (c *PaymentController) PaymentNotify(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "支付回调",
	}))
}
