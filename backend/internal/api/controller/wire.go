package controller

import (
	"github.com/google/wire"
)

// ProviderSet is a provider set for all controllers.
var ProviderSet = wire.NewSet(
	NewAuthController,
	NewUserController,
	// NewChatController,
	// 暂时注释掉未完成的Controller，等实现对应的Service后再添加
	NewJobController,
	NewHouseController,
	NewGigController,
	// NewDatingController,
	// NewMessageController,
	// NewPaymentController,
	// NewFileController,
)
