package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ChatController 聊天控制器
type ChatController struct {
	conversationService service.ConversationService
	validator           validator.Validator
}

// NewChatController 创建聊天控制器
func NewChatController(conversationService service.ConversationService, validator validator.Validator) *ChatController {
	return &ChatController{
		conversationService: conversationService,
		validator:           validator,
	}
}

// GetConversations 获取会话列表
func (c *ChatController) GetConversations(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.GetConversationListRequest
	if err := c.validator.ValidateQuery(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}
	if req.Size > 100 {
		req.Size = 100 // 限制最大页面大小
	}

	req.UserID = userID

	resp, err := c.conversationService.GetUserConversations(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "获取会话列表失败", err, "user_id", userID)
		response.ServerErrorJSON(ctx, "获取会话列表失败")
		return
	}

	response.OK(ctx, resp)
}

// GetConversationByID 获取指定会话详情
func (c *ChatController) GetConversationByID(ctx *gin.Context) {
	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	conversation, err := c.conversationService.GetConversationByID(ctx, conversationID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取会话详情失败", err, "conversation_id", conversationID)
		response.ServerErrorJSON(ctx, "获取会话详情失败")
		return
	}

	response.OK(ctx, conversation)
}

// CreateSingleConversation 创建单聊会话
func (c *ChatController) CreateSingleConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req struct {
		UserID uint `json:"user_id" binding:"required" validate:"required,min=1"`
	}

	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	if userID == req.UserID {
		response.BadRequest(ctx, "不能和自己创建会话")
		return
	}

	conversation, err := c.conversationService.GetOrCreateSingleConversation(ctx, userID, req.UserID)
	if err != nil {
		logger.ErrorCtx(ctx, "创建单聊会话失败", err, "user_id", userID, "target_user_id", req.UserID)
		response.ServerErrorJSON(ctx, "创建会话失败")
		return
	}

	response.OK(ctx, conversation)
}

// DeleteConversation 删除会话
func (c *ChatController) DeleteConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	req := &types.DeleteConversationRequest{
		UserID:         userID,
		ConversationID: conversationID,
	}

	resp, err := c.conversationService.DeleteConversation(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "删除会话失败", err, "user_id", userID, "conversation_id", conversationID)
		response.ServerErrorJSON(ctx, "删除会话失败")
		return
	}

	response.OK(ctx, resp)
}

// PinConversation 置顶/取消置顶会话
func (c *ChatController) PinConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	var body struct {
		IsPinned bool `json:"is_pinned"`
	}

	if err := c.validator.ValidateJSON(ctx, &body); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	req := &types.PinConversationRequest{
		UserID:         userID,
		ConversationID: conversationID,
		IsPinned:       body.IsPinned,
	}

	resp, err := c.conversationService.PinConversation(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "置顶会话失败", err, "user_id", userID, "conversation_id", conversationID, "is_pinned", body.IsPinned)
		response.ServerErrorJSON(ctx, "置顶会话失败")
		return
	}

	response.OK(ctx, resp)
}

// MuteConversation 静音/取消静音会话
func (c *ChatController) MuteConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	var body struct {
		IsMuted bool `json:"is_muted"`
	}

	if err := c.validator.ValidateJSON(ctx, &body); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	req := &types.MuteConversationRequest{
		UserID:         userID,
		ConversationID: conversationID,
		IsMuted:        body.IsMuted,
	}

	resp, err := c.conversationService.MuteConversation(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "静音会话失败", err, "user_id", userID, "conversation_id", conversationID, "is_muted", body.IsMuted)
		response.ServerErrorJSON(ctx, "静音会话失败")
		return
	}

	response.OK(ctx, resp)
}
