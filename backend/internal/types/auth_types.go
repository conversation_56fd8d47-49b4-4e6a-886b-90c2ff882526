package types

// LoginRequest 登录请求
type LoginRequest struct {
	Phone   string `json:"phone" validate:"required,min=11,max=11"`
	SmsCode string `json:"sms_code" validate:"required,min=4,max=6"`
}

// WechatLoginRequest 微信登录请求
type WechatLoginRequest struct {
	LoginCode string `json:"loginCode" validate:"required"`
	PhoneCode string `json:"phoneCode" validate:"required"`
}

// SendSmsCodeRequest 发送短信验证码请求
type SendSmsCodeRequest struct {
	Phone string `json:"phone" validate:"required,min=11,max=11"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token string    `json:"token"`
	User  *UserInfo `json:"user"`
}

// UserInfo 用户信息
type UserInfo struct {
	UID      string `json:"uid"`
	Phone    string `json:"phone"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Source   string `json:"source"`
}
