package types

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	Nickname *string `json:"nickname" validate:"omitempty,min=2,max=20"`
	Avatar   *string `json:"avatar" validate:"omitempty,url"`
	Gender   *int    `json:"gender" validate:"omitempty,oneof=0 1 2"` // 0:未知, 1:男, 2:女
}

// UploadAvatarRequest 上传头像请求
type UploadAvatarRequest struct {
	Avatar string `json:"avatar" validate:"required,url"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6"`
}

// UserProfile 用户资料响应
type UserProfile struct {
	UID      string `json:"uid"`
	Phone    string `json:"phone"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Gender   int    `json:"gender"`
	Source   string `json:"source"`
}
