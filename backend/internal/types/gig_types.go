package types

// CreateGigRequest 创建零工请求
type CreateGigRequest struct {
	Title       string `json:"title" validate:"required,min=5,max=50"`
	Description string `json:"description" validate:"required,min=20"`
	Location    string `json:"location" validate:"required"`
	Pay         int    `json:"pay" validate:"required,gt=0"`
	Category    string `json:"category" validate:"required"`
}

// UpdateGigRequest 更新零工请求
type UpdateGigRequest struct {
	Title       *string `json:"title" validate:"omitempty,min=5,max=50"`
	Description *string `json:"description" validate:"omitempty,min=20"`
	Location    *string `json:"location" validate:"omitempty"`
	Pay         *int    `json:"pay" validate:"omitempty,gt=0"`
	Category    *string `json:"category" validate:"omitempty"`
}
