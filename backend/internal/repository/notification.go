package repository

import (
	"context"

	"bdb-backend/internal/model"
)

// NotificationRepository 通知Repository接口
type NotificationRepository interface {
	// 通知管理
	CreateNotification(ctx context.Context, notification *model.Notification) error
	GetNotificationByID(ctx context.Context, id uint64) (*model.Notification, error)
	UpdateNotification(ctx context.Context, id uint64, updates map[string]interface{}) error
	DeleteNotification(ctx context.Context, id, userID uint64) error
	ListNotifications(ctx context.Context, userID uint64, filter ListNotificationsFilter) ([]*model.Notification, int64, error)

	// 已读状态管理
	MarkNotificationAsRead(ctx context.Context, id, userID uint64) error
	MarkAllNotificationsAsRead(ctx context.Context, userID uint64) error
	GetUnreadNotificationCount(ctx context.Context, userID uint64) (int64, error)

	// 批量操作
	BatchCreateNotifications(ctx context.Context, notifications []*model.Notification) error
	BatchDeleteExpiredNotifications(ctx context.Context) error
}

// ListNotificationsFilter 通知列表过滤器
type ListNotificationsFilter struct {
	Type   string
	IsRead *bool
	Offset int
	Limit  int
}
