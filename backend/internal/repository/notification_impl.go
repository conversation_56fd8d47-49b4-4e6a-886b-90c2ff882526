package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// notificationRepository 通知Repository实现
type notificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository 创建通知Repository
func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{db: db}
}

// CreateNotification 创建通知
func (r *notificationRepository) CreateNotification(ctx context.Context, notification *model.Notification) error {
	return r.db.WithContext(ctx).Create(notification).Error
}

// GetNotificationByID 根据ID获取通知
func (r *notificationRepository) GetNotificationByID(ctx context.Context, id uint64) (*model.Notification, error) {
	var notification model.Notification
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// UpdateNotification 更新通知
func (r *notificationRepository) UpdateNotification(ctx context.Context, id uint64, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.Notification{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteNotification 删除通知
func (r *notificationRepository) DeleteNotification(ctx context.Context, id, userID uint64) error {
	return r.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).Delete(&model.Notification{}).Error
}

// ListNotifications 获取通知列表
func (r *notificationRepository) ListNotifications(ctx context.Context, userID uint64, filter ListNotificationsFilter) ([]*model.Notification, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.Notification{}).Where("user_id = ?", userID)

	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}

	if filter.IsRead != nil {
		query = query.Where("is_read = ?", *filter.IsRead)
	}

	// 过滤未过期的通知
	query = query.Where("expire_at IS NULL OR expire_at > ?", time.Now())

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var notifications []*model.Notification
	err := query.Order("created_at DESC").Offset(filter.Offset).Limit(filter.Limit).Find(&notifications).Error
	return notifications, total, err
}

// MarkNotificationAsRead 标记通知为已读
func (r *notificationRepository) MarkNotificationAsRead(ctx context.Context, id, userID uint64) error {
	now := time.Now()
	updates := map[string]interface{}{
		"is_read": true,
		"read_at": &now,
	}
	return r.db.WithContext(ctx).Model(&model.Notification{}).
		Where("id = ? AND user_id = ?", id, userID).
		Updates(updates).Error
}

// MarkAllNotificationsAsRead 标记所有通知为已读
func (r *notificationRepository) MarkAllNotificationsAsRead(ctx context.Context, userID uint64) error {
	now := time.Now()
	updates := map[string]interface{}{
		"is_read": true,
		"read_at": &now,
	}
	return r.db.WithContext(ctx).Model(&model.Notification{}).
		Where("user_id = ? AND is_read = false", userID).
		Updates(updates).Error
}

// GetUnreadNotificationCount 获取未读通知数量
func (r *notificationRepository) GetUnreadNotificationCount(ctx context.Context, userID uint64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Notification{}).
		Where("user_id = ? AND is_read = false", userID).
		Where("expire_at IS NULL OR expire_at > ?", time.Now()).
		Count(&count).Error
	return count, err
}

// BatchCreateNotifications 批量创建通知
func (r *notificationRepository) BatchCreateNotifications(ctx context.Context, notifications []*model.Notification) error {
	if len(notifications) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).CreateInBatches(notifications, 100).Error
}

// BatchDeleteExpiredNotifications 批量删除过期通知
func (r *notificationRepository) BatchDeleteExpiredNotifications(ctx context.Context) error {
	return r.db.WithContext(ctx).Where("expire_at IS NOT NULL AND expire_at <= ?", time.Now()).
		Delete(&model.Notification{}).Error
}
