package repository

import (
	"context"

	"bdb-backend/internal/model"
)

// ChatRepository 聊天Repository接口
type ChatRepository interface {
	// 聊天室管理
	CreateChatRoom(ctx context.Context, room *model.ChatRoom) error
	GetChatRoomByID(ctx context.Context, id uint64) (*model.ChatRoom, error)
	UpdateChatRoom(ctx context.Context, id uint64, updates map[string]interface{}) error
	DeleteChatRoom(ctx context.Context, id uint64) error
	ListChatRooms(ctx context.Context, userID uint64, filter ListChatRoomsFilter) ([]*model.ChatRoom, int64, error)
	GetPrivateChatRoom(ctx context.Context, userID1, userID2 uint64) (*model.ChatRoom, error)

	// 聊天室成员管理
	CreateChatMember(ctx context.Context, member *model.ChatMember) error
	GetChatMember(ctx context.Context, roomID, userID uint64) (*model.ChatMember, error)
	UpdateChatMember(ctx context.Context, roomID, userID uint64, updates map[string]interface{}) error
	DeleteChatMember(ctx context.Context, roomID, userID uint64) error
	ListChatMembers(ctx context.Context, roomID uint64, filter ListChatMembersFilter) ([]*model.ChatMember, int64, error)
	GetChatRoomMemberCount(ctx context.Context, roomID uint64) (int64, error)

	// 消息管理
	CreateChatMessage(ctx context.Context, message *model.ChatMessage) error
	GetChatMessageByID(ctx context.Context, id uint64) (*model.ChatMessage, error)
	UpdateChatMessage(ctx context.Context, id uint64, updates map[string]interface{}) error
	DeleteChatMessage(ctx context.Context, id uint64) error
	ListChatMessages(ctx context.Context, roomID uint64, filter ListChatMessagesFilter) ([]*model.ChatMessage, error)
	MarkMessageAsRead(ctx context.Context, messageID, userID uint64) error
	GetUnreadMessageCount(ctx context.Context, userID uint64) (int64, error)
}

// Filter结构体定义
type ListChatRoomsFilter struct {
	Type    model.ChatRoomType
	Keyword string
	Offset  int
	Limit   int
}

type ListChatMembersFilter struct {
	Status model.MemberStatus
	Role   model.MemberRole
	Offset int
	Limit  int
}

type ListChatMessagesFilter struct {
	BeforeID    uint64
	AfterID     uint64
	MessageType model.MessageType
	SenderID    uint64
	Offset      int
	Limit       int
}
