package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// UserConversationRepository 用户会话Repository接口
type UserConversationRepository interface {
	// 基础CRUD
	Create(ctx context.Context, userConv *model.UserConversation) error
	GetByUserAndConversation(ctx context.Context, userID, conversationID uint) (*model.UserConversation, error)
	Update(ctx context.Context, userID, conversationID uint, updates map[string]interface{}) error
	Delete(ctx context.Context, userID, conversationID uint) error

	// 查询方法
	ListByUser(ctx context.Context, userID uint, filter UserConversationFilter) ([]*model.UserConversation, int64, error)
	GetUnreadCount(ctx context.Context, userID uint) (int64, error)
	GetUnreadCountByConversation(ctx context.Context, userID, conversationID uint) (int64, error)

	// 状态管理
	MarkAsRead(ctx context.Context, userID, conversationID, messageID uint) error
	UpdateLastActiveTime(ctx context.Context, userID, conversationID uint) error
	SetPinned(ctx context.Context, userID, conversationID uint, isPinned bool) error
	SetMuted(ctx context.Context, userID, conversationID uint, isMuted bool) error
	SetVisible(ctx context.Context, userID, conversationID uint, isVisible bool) error

	// 批量操作
	BatchCreate(ctx context.Context, userConvs []*model.UserConversation) error
	BatchUpdateLastActiveTime(ctx context.Context, conversationID uint) error
}

// UserConversationFilter 用户会话查询过滤器
type UserConversationFilter struct {
	IsVisible bool
	IsPinned  *bool // nil表示不过滤，true/false表示过滤条件
	IsMuted   *bool
	Offset    int
	Limit     int
	OrderBy   string // last_active_time, created_at
	Order     string // ASC, DESC
}

// userConversationRepository 用户会话Repository实现
type userConversationRepository struct {
	db *gorm.DB
}

// NewUserConversationRepository 创建用户会话Repository
func NewUserConversationRepository(db *gorm.DB) UserConversationRepository {
	return &userConversationRepository{db: db}
}

// Create 创建用户会话
func (r *userConversationRepository) Create(ctx context.Context, userConv *model.UserConversation) error {
	return r.db.WithContext(ctx).Create(userConv).Error
}

// GetByUserAndConversation 根据用户和会话ID获取用户会话
func (r *userConversationRepository) GetByUserAndConversation(ctx context.Context, userID, conversationID uint) (*model.UserConversation, error) {
	var userConv model.UserConversation
	err := r.db.WithContext(ctx).Where("user_id = ? AND conversation_id = ?", userID, conversationID).First(&userConv).Error
	if err != nil {
		return nil, err
	}
	return &userConv, nil
}

// Update 更新用户会话
func (r *userConversationRepository) Update(ctx context.Context, userID, conversationID uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.UserConversation{}).
		Where("user_id = ? AND conversation_id = ?", userID, conversationID).
		Updates(updates).Error
}

// Delete 删除用户会话
func (r *userConversationRepository) Delete(ctx context.Context, userID, conversationID uint) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND conversation_id = ?", userID, conversationID).
		Delete(&model.UserConversation{}).Error
}

// ListByUser 获取用户的会话列表
func (r *userConversationRepository) ListByUser(ctx context.Context, userID uint, filter UserConversationFilter) ([]*model.UserConversation, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.UserConversation{}).Where("user_id = ?", userID)

	if filter.IsVisible {
		query = query.Where("is_visible = ?", filter.IsVisible)
	}
	if filter.IsPinned != nil {
		query = query.Where("is_pinned = ?", *filter.IsPinned)
	}
	if filter.IsMuted != nil {
		query = query.Where("is_muted = ?", *filter.IsMuted)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 排序
	orderBy := "last_active_time"
	if filter.OrderBy != "" {
		orderBy = filter.OrderBy
	}
	order := "DESC"
	if filter.Order != "" {
		order = filter.Order
	}

	var userConvs []*model.UserConversation
	err := query.Order(orderBy + " " + order).
		Offset(filter.Offset).
		Limit(filter.Limit).
		Find(&userConvs).Error

	return userConvs, total, err
}

// GetUnreadCount 获取用户总未读数
// 注意：根据新设计，UnreadCount字段已移除，这里返回0
// 实际计算由service层通过消息表动态计算完成
func (r *userConversationRepository) GetUnreadCount(ctx context.Context, userID uint) (int64, error) {
	// 检查用户是否有会话
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserConversation{}).
		Where("user_id = ? AND is_visible = true", userID).
		Count(&count).Error
	if err != nil {
		return 0, err
	}

	// 返回0，实际未读数计算由service层完成
	return 0, nil
}

// GetUnreadCountByConversation 获取用户在特定会话的未读数
// 注意：根据新设计，UnreadCount字段已移除，这里应该通过消息表动态计算
// 这个方法主要用于兼容，实际计算应该在service层完成
func (r *userConversationRepository) GetUnreadCountByConversation(ctx context.Context, userID, conversationID uint) (int64, error) {
	// 检查用户会话是否存在
	var userConv model.UserConversation
	err := r.db.WithContext(ctx).Where("user_id = ? AND conversation_id = ?", userID, conversationID).
		First(&userConv).Error
	if err != nil {
		return 0, err
	}

	// 返回0，实际计算由service层通过消息表完成
	// 这样设计是为了避免数据不一致问题
	return 0, nil
}

// MarkAsRead 标记消息已读
func (r *userConversationRepository) MarkAsRead(ctx context.Context, userID, conversationID, messageID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"last_read_message_id": messageID,
		"last_read_time":       now,
		"updated_at":           now,
	}
	return r.Update(ctx, userID, conversationID, updates)
}

// UpdateLastActiveTime 更新最后活跃时间
func (r *userConversationRepository) UpdateLastActiveTime(ctx context.Context, userID, conversationID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"last_active_time": now,
		"updated_at":       now,
	}
	return r.Update(ctx, userID, conversationID, updates)
}

// SetPinned 设置置顶状态
func (r *userConversationRepository) SetPinned(ctx context.Context, userID, conversationID uint, isPinned bool) error {
	updates := map[string]interface{}{
		"is_pinned":  isPinned,
		"updated_at": time.Now(),
	}
	return r.Update(ctx, userID, conversationID, updates)
}

// SetMuted 设置静音状态
func (r *userConversationRepository) SetMuted(ctx context.Context, userID, conversationID uint, isMuted bool) error {
	updates := map[string]interface{}{
		"is_muted":   isMuted,
		"updated_at": time.Now(),
	}
	return r.Update(ctx, userID, conversationID, updates)
}

// SetVisible 设置可见性
func (r *userConversationRepository) SetVisible(ctx context.Context, userID, conversationID uint, isVisible bool) error {
	updates := map[string]interface{}{
		"is_visible": isVisible,
		"updated_at": time.Now(),
	}
	if !isVisible {
		updates["deleted_at"] = time.Now()
		updates["is_deleted"] = true
	}
	return r.Update(ctx, userID, conversationID, updates)
}

// BatchCreate 批量创建用户会话
func (r *userConversationRepository) BatchCreate(ctx context.Context, userConvs []*model.UserConversation) error {
	if len(userConvs) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).CreateInBatches(userConvs, 100).Error
}

// BatchUpdateLastActiveTime 批量更新会话的最后活跃时间
func (r *userConversationRepository) BatchUpdateLastActiveTime(ctx context.Context, conversationID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"last_active_time": now,
		"updated_at":       now,
	}
	return r.db.WithContext(ctx).Model(&model.UserConversation{}).
		Where("conversation_id = ?", conversationID).
		Updates(updates).Error
}
