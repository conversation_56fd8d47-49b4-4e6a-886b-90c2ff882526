package repository

import (
	"context"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// chatRepository 聊天Repository实现
type chatRepository struct {
	db *gorm.DB
}

// NewChatRepository 创建聊天Repository
func NewChatRepository(db *gorm.DB) ChatRepository {
	return &chatRepository{db: db}
}

// CreateChatRoom 创建聊天室
func (r *chatRepository) CreateChatRoom(ctx context.Context, room *model.ChatRoom) error {
	return r.db.WithContext(ctx).Create(room).Error
}

// GetChatRoomByID 根据ID获取聊天室
func (r *chatRepository) GetChatRoomByID(ctx context.Context, id uint64) (*model.ChatRoom, error) {
	var room model.ChatRoom
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&room).Error
	if err != nil {
		return nil, err
	}
	return &room, nil
}

// UpdateChatRoom 更新聊天室
func (r *chatRepository) UpdateChatRoom(ctx context.Context, id uint64, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.ChatRoom{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteChatRoom 删除聊天室
func (r *chatRepository) DeleteChatRoom(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.ChatRoom{}).Error
}

// ListChatRooms 获取聊天室列表
func (r *chatRepository) ListChatRooms(ctx context.Context, userID uint64, filter ListChatRoomsFilter) ([]*model.ChatRoom, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.ChatRoom{})

	// 连接成员表来获取用户可访问的聊天室
	query = query.Joins("JOIN chat_members ON chat_rooms.id = chat_members.room_id").
		Where("chat_members.user_id = ? AND chat_members.status = ?", userID, model.MemberStatusActive)

	if filter.Type != "" {
		query = query.Where("chat_rooms.type = ?", filter.Type)
	}

	if filter.Keyword != "" {
		query = query.Where("chat_rooms.name LIKE ?", "%"+filter.Keyword+"%")
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var rooms []*model.ChatRoom
	err := query.Offset(filter.Offset).Limit(filter.Limit).Find(&rooms).Error
	return rooms, total, err
}

// GetPrivateChatRoom 获取私聊室
func (r *chatRepository) GetPrivateChatRoom(ctx context.Context, userID1, userID2 uint64) (*model.ChatRoom, error) {
	var room model.ChatRoom
	err := r.db.WithContext(ctx).
		Joins("JOIN chat_members cm1 ON chat_rooms.id = cm1.room_id AND cm1.user_id = ?", userID1).
		Joins("JOIN chat_members cm2 ON chat_rooms.id = cm2.room_id AND cm2.user_id = ?", userID2).
		Where("chat_rooms.type = ?", model.ChatRoomTypePrivate).
		First(&room).Error

	if err != nil {
		return nil, err
	}
	return &room, nil
}

// CreateChatMember 创建聊天室成员
func (r *chatRepository) CreateChatMember(ctx context.Context, member *model.ChatMember) error {
	return r.db.WithContext(ctx).Create(member).Error
}

// GetChatMember 获取聊天室成员
func (r *chatRepository) GetChatMember(ctx context.Context, roomID, userID uint64) (*model.ChatMember, error) {
	var member model.ChatMember
	err := r.db.WithContext(ctx).Where("room_id = ? AND user_id = ?", roomID, userID).First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

// UpdateChatMember 更新聊天室成员
func (r *chatRepository) UpdateChatMember(ctx context.Context, roomID, userID uint64, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.ChatMember{}).
		Where("room_id = ? AND user_id = ?", roomID, userID).
		Updates(updates).Error
}

// DeleteChatMember 删除聊天室成员
func (r *chatRepository) DeleteChatMember(ctx context.Context, roomID, userID uint64) error {
	return r.db.WithContext(ctx).Where("room_id = ? AND user_id = ?", roomID, userID).Delete(&model.ChatMember{}).Error
}

// ListChatMembers 获取聊天室成员列表
func (r *chatRepository) ListChatMembers(ctx context.Context, roomID uint64, filter ListChatMembersFilter) ([]*model.ChatMember, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.ChatMember{}).Where("room_id = ?", roomID)

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.Role != "" {
		query = query.Where("role = ?", filter.Role)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var members []*model.ChatMember
	err := query.Offset(filter.Offset).Limit(filter.Limit).Find(&members).Error
	return members, total, err
}

// GetChatRoomMemberCount 获取聊天室成员数量
func (r *chatRepository) GetChatRoomMemberCount(ctx context.Context, roomID uint64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.ChatMember{}).
		Where("room_id = ? AND status = ?", roomID, model.MemberStatusActive).
		Count(&count).Error
	return count, err
}

// CreateChatMessage 创建聊天消息
func (r *chatRepository) CreateChatMessage(ctx context.Context, message *model.ChatMessage) error {
	return r.db.WithContext(ctx).Create(message).Error
}

// GetChatMessageByID 根据ID获取聊天消息
func (r *chatRepository) GetChatMessageByID(ctx context.Context, id uint64) (*model.ChatMessage, error) {
	var message model.ChatMessage
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// UpdateChatMessage 更新聊天消息
func (r *chatRepository) UpdateChatMessage(ctx context.Context, id uint64, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.ChatMessage{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteChatMessage 删除聊天消息
func (r *chatRepository) DeleteChatMessage(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.ChatMessage{}).Error
}

// ListChatMessages 获取聊天消息列表
func (r *chatRepository) ListChatMessages(ctx context.Context, roomID uint64, filter ListChatMessagesFilter) ([]*model.ChatMessage, error) {
	query := r.db.WithContext(ctx).Model(&model.ChatMessage{}).
		Where("room_id = ? AND is_deleted = false", roomID).
		Order("created_at DESC")

	if filter.BeforeID > 0 {
		query = query.Where("id < ?", filter.BeforeID)
	}

	if filter.AfterID > 0 {
		query = query.Where("id > ?", filter.AfterID)
	}

	if filter.MessageType != "" {
		query = query.Where("type = ?", filter.MessageType)
	}

	if filter.SenderID > 0 {
		query = query.Where("sender_id = ?", filter.SenderID)
	}

	var messages []*model.ChatMessage
	err := query.Offset(filter.Offset).Limit(filter.Limit).Find(&messages).Error
	return messages, err
}

// MarkMessageAsRead 标记消息为已读
func (r *chatRepository) MarkMessageAsRead(ctx context.Context, messageID, userID uint64) error {
	// 检查是否已存在已读记录
	var count int64
	err := r.db.WithContext(ctx).Model(&model.MessageReadRecord{}).
		Where("message_id = ? AND user_id = ?", messageID, userID).
		Count(&count).Error
	if err != nil {
		return err
	}

	if count == 0 {
		// 创建已读记录
		readRecord := &model.MessageReadRecord{
			MessageID: messageID,
			UserID:    userID,
		}
		return r.db.WithContext(ctx).Create(readRecord).Error
	}

	return nil
}

// GetUnreadMessageCount 获取未读消息数量
func (r *chatRepository) GetUnreadMessageCount(ctx context.Context, userID uint64) (int64, error) {
	var count int64

	// 查询用户所在的聊天室中，用户未读的消息数量
	err := r.db.WithContext(ctx).Model(&model.ChatMessage{}).
		Joins("JOIN chat_members ON chat_messages.room_id = chat_members.room_id").
		Where("chat_members.user_id = ? AND chat_members.status = ?", userID, model.MemberStatusActive).
		Where("chat_messages.sender_id != ?", userID). // 排除自己发送的消息
		Where("chat_messages.id NOT IN (?)",
			r.db.Model(&model.MessageReadRecord{}).
				Select("message_id").
				Where("user_id = ?", userID)).
		Count(&count).Error

	return count, err
}
