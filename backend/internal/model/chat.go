package model

import (
	"time"

	"gorm.io/gorm"
)

// ChatRoomType 聊天室类型
type ChatRoomType string

const (
	ChatRoomTypePrivate ChatRoomType = "private" // 私聊
	ChatRoomTypeGroup   ChatRoomType = "group"   // 群聊
	ChatRoomTypeSystem  ChatRoomType = "system"  // 系统通知
)

// ChatRoom 聊天室模型
type ChatRoom struct {
	ID          uint64         `json:"id" gorm:"primarykey"`
	Type        ChatRoomType   `json:"type" gorm:"type:varchar(20);not null;default:'private';comment:聊天室类型"`
	Name        string         `json:"name" gorm:"type:varchar(100);comment:聊天室名称"`
	Description string         `json:"description" gorm:"type:text;comment:聊天室描述"`
	Avatar      string         `json:"avatar" gorm:"type:varchar(255);comment:聊天室头像"`
	OwnerID     uint64         `json:"owner_id" gorm:"comment:聊天室创建者ID"`
	MaxMembers  int            `json:"max_members" gorm:"default:500;comment:最大成员数"`
	IsActive    bool           `json:"is_active" gorm:"default:true;comment:是否活跃"`
	Settings    string         `json:"settings" gorm:"type:text;comment:聊天室设置(JSON)"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联关系
	Owner    *User         `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
	Members  []ChatMember  `json:"members,omitempty" gorm:"foreignKey:RoomID"`
	Messages []ChatMessage `json:"messages,omitempty" gorm:"foreignKey:RoomID"`
}

// MessageType 消息类型
type MessageType string

const (
	MessageTypeText     MessageType = "text"     // 文本消息
	MessageTypeImage    MessageType = "image"    // 图片消息
	MessageTypeVideo    MessageType = "video"    // 视频消息
	MessageTypeAudio    MessageType = "audio"    // 音频消息
	MessageTypeFile     MessageType = "file"     // 文件消息
	MessageTypeLocation MessageType = "location" // 位置消息
	MessageTypeCard     MessageType = "card"     // 名片消息
	MessageTypeEmoji    MessageType = "emoji"    // 表情消息
	MessageTypeSystem   MessageType = "system"   // 系统消息
	MessageTypeRecall   MessageType = "recall"   // 撤回消息
)

// MessageStatus 消息状态
type MessageStatus string

const (
	MessageStatusSending MessageStatus = "sending" // 发送中
	MessageStatusSent    MessageStatus = "sent"    // 已发送
	MessageStatusRead    MessageStatus = "read"    // 已读
	MessageStatusFailed  MessageStatus = "failed"  // 发送失败
	MessageStatusRecall  MessageStatus = "recall"  // 已撤回
)

// ChatMessage 聊天消息模型
type ChatMessage struct {
	ID             uint64        `json:"id" gorm:"primarykey"`
	RoomID         uint64        `json:"room_id" gorm:"not null;index;comment:聊天室ID"`
	SenderID       uint64        `json:"sender_id" gorm:"not null;index;comment:发送者ID"`
	Type           MessageType   `json:"type" gorm:"type:varchar(20);not null;default:'text';comment:消息类型"`
	Content        string        `json:"content" gorm:"type:text;comment:消息内容"`
	MediaURL       string        `json:"media_url" gorm:"type:varchar(500);comment:媒体文件URL"`
	MediaThumbnail string        `json:"media_thumbnail" gorm:"type:varchar(500);comment:媒体缩略图URL"`
	MediaSize      int64         `json:"media_size" gorm:"comment:媒体文件大小"`
	MediaDuration  int           `json:"media_duration" gorm:"comment:媒体时长(秒)"`
	Extra          string        `json:"extra" gorm:"type:text;comment:额外信息(JSON)"`
	Status         MessageStatus `json:"status" gorm:"type:varchar(20);not null;default:'sent';comment:消息状态"`
	ReplyToID      uint64        `json:"reply_to_id" gorm:"index;comment:回复的消息ID"`
	AtUserIDs      string        `json:"at_user_ids" gorm:"type:text;comment:@的用户ID列表(JSON数组)"`
	ReadCount      int           `json:"read_count" gorm:"default:0;comment:已读数量"`
	LikeCount      int           `json:"like_count" gorm:"default:0;comment:点赞数量"`
	IsDeleted      bool          `json:"is_deleted" gorm:"default:false;comment:是否被删除"`
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`

	// 关联关系
	Room    *ChatRoom    `json:"room,omitempty" gorm:"foreignKey:RoomID"`
	Sender  *User        `json:"sender,omitempty" gorm:"foreignKey:SenderID"`
	ReplyTo *ChatMessage `json:"reply_to,omitempty" gorm:"foreignKey:ReplyToID"`
}

// MemberRole 成员角色
type MemberRole string

const (
	MemberRoleOwner  MemberRole = "owner"  // 群主
	MemberRoleAdmin  MemberRole = "admin"  // 管理员
	MemberRoleMember MemberRole = "member" // 普通成员
)

// MemberStatus 成员状态
type MemberStatus string

const (
	MemberStatusActive  MemberStatus = "active"  // 正常
	MemberStatusMuted   MemberStatus = "muted"   // 被禁言
	MemberStatusKicked  MemberStatus = "kicked"  // 被踢出
	MemberStatusLeft    MemberStatus = "left"    // 主动退出
	MemberStatusBlocked MemberStatus = "blocked" // 被拉黑
)

// ChatMember 聊天室成员模型
type ChatMember struct {
	ID            uint64       `json:"id" gorm:"primarykey"`
	RoomID        uint64       `json:"room_id" gorm:"not null;index;comment:聊天室ID"`
	UserID        uint64       `json:"user_id" gorm:"not null;index;comment:用户ID"`
	Role          MemberRole   `json:"role" gorm:"type:varchar(20);not null;default:'member';comment:成员角色"`
	Status        MemberStatus `json:"status" gorm:"type:varchar(20);not null;default:'active';comment:成员状态"`
	Nickname      string       `json:"nickname" gorm:"type:varchar(50);comment:群内昵称"`
	MuteUntil     *time.Time   `json:"mute_until" gorm:"comment:禁言到期时间"`
	LastReadMsgID uint64       `json:"last_read_msg_id" gorm:"comment:最后已读消息ID"`
	UnreadCount   int          `json:"unread_count" gorm:"default:0;comment:未读消息数"`
	IsTop         bool         `json:"is_top" gorm:"default:false;comment:是否置顶"`
	IsNotify      bool         `json:"is_notify" gorm:"default:true;comment:是否接收通知"`
	JoinedAt      time.Time    `json:"joined_at" gorm:"comment:加入时间"`
	LeftAt        *time.Time   `json:"left_at" gorm:"comment:离开时间"`
	CreatedAt     time.Time    `json:"created_at"`
	UpdatedAt     time.Time    `json:"updated_at"`

	// 关联关系
	Room *ChatRoom `json:"room,omitempty" gorm:"foreignKey:RoomID"`
	User *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// MessageReadRecord 消息已读记录
type MessageReadRecord struct {
	ID        uint64    `json:"id" gorm:"primarykey"`
	MessageID uint64    `json:"message_id" gorm:"not null;index;comment:消息ID"`
	UserID    uint64    `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ReadAt    time.Time `json:"read_at" gorm:"comment:阅读时间"`
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	Message *ChatMessage `json:"message,omitempty" gorm:"foreignKey:MessageID"`
	User    *User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// 表名定义
func (ChatRoom) TableName() string {
	return "chat_rooms"
}

func (ChatMessage) TableName() string {
	return "chat_messages"
}

func (ChatMember) TableName() string {
	return "chat_members"
}

func (MessageReadRecord) TableName() string {
	return "message_read_records"
}

// 索引定义
func (ChatRoom) GetIndexes() []string {
	return []string{
		"idx_owner_id",
		"idx_type",
		"idx_is_active",
	}
}

func (ChatMessage) GetIndexes() []string {
	return []string{
		"idx_room_id_created_at",
		"idx_sender_id",
		"idx_type",
		"idx_status",
		"idx_is_deleted",
	}
}

func (ChatMember) GetIndexes() []string {
	return []string{
		"idx_room_id_user_id",
		"idx_user_id",
		"idx_status",
		"idx_role",
	}
}

func (MessageReadRecord) GetIndexes() []string {
	return []string{
		"idx_message_id_user_id",
		"idx_user_id",
	}
}
