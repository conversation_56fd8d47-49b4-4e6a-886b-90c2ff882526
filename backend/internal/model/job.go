package model

import (
	"time"
)

// Job 职位模型
type Job struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	UserID       uint      `gorm:"not null;index" json:"user_id"`
	Title        string    `gorm:"size:100;not null" json:"title"`
	CompanyName  string    `gorm:"size:100;not null" json:"company_name"`
	Category     int       `gorm:"not null;index" json:"category"`
	Type         int       `gorm:"not null" json:"type"` // 1:全职 2:兼职 3:实习 4:劳务
	SalaryMin    int       `json:"salary_min"`
	SalaryMax    int       `json:"salary_max"`
	SalaryType   int       `gorm:"default:1" json:"salary_type"` // 1:月薪 2:周薪 3:日薪 4:时薪
	Location     string    `gorm:"size:100;not null" json:"location"`
	Experience   int       `json:"experience"` // 工作经验要求（年）
	Education    int       `json:"education"`  // 学历要求
	Description  string    `gorm:"type:text" json:"description"`
	Requirements string    `gorm:"type:text" json:"requirements"`
	Benefits     string    `gorm:"type:text" json:"benefits"`
	ContactName  string    `gorm:"size:50" json:"contact_name"`
	ContactPhone string    `gorm:"size:20" json:"contact_phone"`
	Status       int       `gorm:"default:1;index" json:"status"` // 1:招聘中 2:暂停 3:已结束
	ViewCount    int       `gorm:"default:0" json:"view_count"`
	ApplyCount   int       `gorm:"default:0" json:"apply_count"`
	IsTop        bool      `gorm:"default:false" json:"is_top"`
	TopExpireAt  time.Time `json:"top_expire_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// JobApplication 职位申请
type JobApplication struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	JobID     uint      `gorm:"not null;index" json:"job_id"`
	UserID    uint      `gorm:"not null;index" json:"user_id"`
	ResumeID  uint      `json:"resume_id"`
	Message   string    `gorm:"type:text" json:"message"`
	Status    int       `gorm:"default:1" json:"status"` // 1:待查看 2:已查看 3:邀请面试 4:已拒绝
	ViewedAt  time.Time `json:"viewed_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Resume 简历
type Resume struct {
	ID                uint      `gorm:"primarykey" json:"id"`
	UserID            uint      `gorm:"not null;index" json:"user_id"`
	Name              string    `gorm:"size:50;not null" json:"name"`
	Phone             string    `gorm:"size:20;not null" json:"phone"`
	Email             string    `gorm:"size:100" json:"email"`
	Gender            int       `json:"gender"`
	Age               int       `json:"age"`
	Education         int       `json:"education"`
	Experience        int       `json:"experience"`
	ExpectedJob       string    `gorm:"size:100" json:"expected_job"`
	ExpectedCity      string    `gorm:"size:50" json:"expected_city"`
	ExpectedSalaryMin int       `json:"expected_salary_min"`
	ExpectedSalaryMax int       `json:"expected_salary_max"`
	Skills            string    `gorm:"type:text" json:"skills"`
	Introduction      string    `gorm:"type:text" json:"introduction"`
	IsDefault         bool      `gorm:"default:false" json:"is_default"`
	Status            int       `gorm:"default:1" json:"status"` // 1:正常 2:禁用
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// WorkExperience 工作经历
type WorkExperience struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	ResumeID    uint      `gorm:"not null;index" json:"resume_id"`
	CompanyName string    `gorm:"size:100;not null" json:"company_name"`
	Position    string    `gorm:"size:50;not null" json:"position"`
	StartDate   time.Time `json:"start_date"`
	EndDate     time.Time `json:"end_date"`
	IsCurrent   bool      `gorm:"default:false" json:"is_current"`
	Description string    `gorm:"type:text" json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
