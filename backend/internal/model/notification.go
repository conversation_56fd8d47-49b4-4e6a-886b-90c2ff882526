package model

import (
	"time"

	"gorm.io/datatypes"
)

// NotificationType 通知类型
type NotificationType string

const (
	// 基础通知类型
	NotificationTypeSystem  NotificationType = "system"  // 系统通知
	NotificationTypeAudit   NotificationType = "audit"   // 审核通知
	NotificationTypeMessage NotificationType = "message" // 消息通知

	// 业务通知类型
	NotificationTypeJob    NotificationType = "job"    // 招聘通知
	NotificationTypeHouse  NotificationType = "house"  // 房源通知
	NotificationTypeDating NotificationType = "dating" // 交友通知
	NotificationTypeGig    NotificationType = "gig"    // 零工通知

	// 互动通知类型
	NotificationTypeLike    NotificationType = "like"    // 点赞通知
	NotificationTypeComment NotificationType = "comment" // 评论通知
	NotificationTypeFollow  NotificationType = "follow"  // 关注通知
	NotificationTypeMatch   NotificationType = "match"   // 匹配通知

	// 活动通知类型
	NotificationTypeActivity     NotificationType = "activity"     // 活动通知
	NotificationTypeSurvey       NotificationType = "survey"       // 调查问卷
	NotificationTypePromotion    NotificationType = "promotion"    // 推广活动
	NotificationTypeAnnouncement NotificationType = "announcement" // 公告通知
)

// NotificationLevel 通知级别
type NotificationLevel string

const (
	NotificationLevelInfo    NotificationLevel = "info"    // 普通信息
	NotificationLevelWarning NotificationLevel = "warning" // 警告
	NotificationLevelError   NotificationLevel = "error"   // 错误
	NotificationLevelSuccess NotificationLevel = "success" // 成功
	NotificationLevelUrgent  NotificationLevel = "urgent"  // 紧急
)

// NotificationStatus 通知状态
type NotificationStatus string

const (
	NotificationStatusUnread  NotificationStatus = "unread"  // 未读
	NotificationStatusRead    NotificationStatus = "read"    // 已读
	NotificationStatusDeleted NotificationStatus = "deleted" // 已删除
)

// SystemNotification 系统通知模型 - 支持多种通知类型的扩展架构
type SystemNotification struct {
	ID     uint `json:"id" gorm:"primarykey"`
	UserID uint `json:"user_id" gorm:"not null;index;comment:接收用户ID"`

	// 通知基础信息
	Type   NotificationType   `json:"type" gorm:"type:varchar(30);not null;index;comment:通知类型"`
	Level  NotificationLevel  `json:"level" gorm:"type:varchar(20);default:'info';comment:通知级别"`
	Status NotificationStatus `json:"status" gorm:"type:varchar(20);default:'unread';index;comment:通知状态"`

	// 通知内容
	Title   string `json:"title" gorm:"type:varchar(200);not null;comment:通知标题"`
	Content string `json:"content" gorm:"type:text;comment:通知内容"`
	Summary string `json:"summary" gorm:"type:varchar(500);comment:通知摘要，用于列表显示"`

	// 扩展数据 - 支持不同类型通知的个性化数据
	Data *datatypes.JSON `json:"data,omitempty" gorm:"type:json;comment:扩展数据，JSON格式"`

	// 行为配置
	ActionURL  string `json:"action_url" gorm:"type:varchar(500);comment:点击跳转URL"`
	ActionText string `json:"action_text" gorm:"type:varchar(50);comment:行为按钮文本"`

	// 显示配置
	ImageURL string `json:"image_url" gorm:"type:varchar(500);comment:通知图片URL"`
	IconURL  string `json:"icon_url" gorm:"type:varchar(500);comment:通知图标URL"`

	// 时间控制
	ExpireAt  *time.Time `json:"expire_at" gorm:"comment:过期时间"`
	ReadAt    *time.Time `json:"read_at" gorm:"comment:已读时间"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"comment:删除时间"`

	CreatedAt time.Time `json:"created_at" gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP;index"`
	UpdatedAt time.Time `json:"updated_at" gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP"`
}

// TableName 设置表名
func (SystemNotification) TableName() string {
	return "system_notifications"
}

// IsExpired 判断通知是否已过期
func (n *SystemNotification) IsExpired() bool {
	if n.ExpireAt == nil {
		return false
	}
	return time.Now().After(*n.ExpireAt)
}

// IsRead 判断通知是否已读
func (n *SystemNotification) IsRead() bool {
	return n.Status == NotificationStatusRead
}

// MarkAsRead 标记为已读
func (n *SystemNotification) MarkAsRead() {
	now := time.Now()
	n.Status = NotificationStatusRead
	n.ReadAt = &now
}

// MarkAsDeleted 标记为已删除
func (n *SystemNotification) MarkAsDeleted() {
	now := time.Now()
	n.Status = NotificationStatusDeleted
	n.DeletedAt = &now
}

// =====================================================
// 通知模板定义 - 用于不同类型通知的标准化处理
// =====================================================

// NotificationTemplate 通知模板接口
type NotificationTemplate interface {
	GetType() NotificationType
	GetTitle() string
	GetContent() string
	GetData() map[string]interface{}
	GetActionURL() string
	GetLevel() NotificationLevel
}

// AuditNotificationData 审核通知数据
type AuditNotificationData struct {
	AuditType   string `json:"audit_type"`   // 审核类型：job/house/dating/gig
	ItemID      uint   `json:"item_id"`      // 审核项目ID
	ItemTitle   string `json:"item_title"`   // 审核项目标题
	AuditResult string `json:"audit_result"` // 审核结果：approved/rejected
	Reason      string `json:"reason"`       // 审核原因
}

// ActivityNotificationData 活动通知数据
type ActivityNotificationData struct {
	ActivityID   uint      `json:"activity_id"`   // 活动ID
	ActivityType string    `json:"activity_type"` // 活动类型
	StartTime    time.Time `json:"start_time"`    // 开始时间
	EndTime      time.Time `json:"end_time"`      // 结束时间
	Reward       string    `json:"reward"`        // 奖励信息
}

// SurveyNotificationData 调查问卷通知数据
type SurveyNotificationData struct {
	SurveyID    uint      `json:"survey_id"`    // 问卷ID
	SurveyTitle string    `json:"survey_title"` // 问卷标题
	Deadline    time.Time `json:"deadline"`     // 截止时间
	Reward      string    `json:"reward"`       // 完成奖励
}

// InteractionNotificationData 互动通知数据
type InteractionNotificationData struct {
	FromUserID   uint   `json:"from_user_id"`   // 操作用户ID
	FromUserName string `json:"from_user_name"` // 操作用户名称
	FromAvatar   string `json:"from_avatar"`    // 操作用户头像
	TargetType   string `json:"target_type"`    // 目标类型：post/comment/profile
	TargetID     uint   `json:"target_id"`      // 目标ID
	TargetTitle  string `json:"target_title"`   // 目标标题
}

// =====================================================
// 通知构建器 - 简化不同类型通知的创建
// =====================================================

// NotificationBuilder 通知构建器
type NotificationBuilder struct {
	notification *SystemNotification
}

// NewNotificationBuilder 创建通知构建器
func NewNotificationBuilder(userID uint) *NotificationBuilder {
	return &NotificationBuilder{
		notification: &SystemNotification{
			UserID: userID,
			Level:  NotificationLevelInfo,
			Status: NotificationStatusUnread,
		},
	}
}

// SetType 设置通知类型
func (b *NotificationBuilder) SetType(notificationType NotificationType) *NotificationBuilder {
	b.notification.Type = notificationType
	return b
}

// SetLevel 设置通知级别
func (b *NotificationBuilder) SetLevel(level NotificationLevel) *NotificationBuilder {
	b.notification.Level = level
	return b
}

// SetTitle 设置标题
func (b *NotificationBuilder) SetTitle(title string) *NotificationBuilder {
	b.notification.Title = title
	return b
}

// SetContent 设置内容
func (b *NotificationBuilder) SetContent(content string) *NotificationBuilder {
	b.notification.Content = content
	return b
}

// SetSummary 设置摘要
func (b *NotificationBuilder) SetSummary(summary string) *NotificationBuilder {
	b.notification.Summary = summary
	return b
}

// SetData 设置扩展数据
func (b *NotificationBuilder) SetData(data interface{}) *NotificationBuilder {
	if data != nil {
		jsonData := datatypes.JSON{}
		jsonData.UnmarshalJSON([]byte{}) // 初始化
		b.notification.Data = &jsonData
	}
	return b
}

// SetActionURL 设置行为URL
func (b *NotificationBuilder) SetActionURL(url string) *NotificationBuilder {
	b.notification.ActionURL = url
	return b
}

// SetActionText 设置行为按钮文本
func (b *NotificationBuilder) SetActionText(text string) *NotificationBuilder {
	b.notification.ActionText = text
	return b
}

// SetExpireAt 设置过期时间
func (b *NotificationBuilder) SetExpireAt(expireAt time.Time) *NotificationBuilder {
	b.notification.ExpireAt = &expireAt
	return b
}

// Build 构建通知
func (b *NotificationBuilder) Build() *SystemNotification {
	return b.notification
}
