package model

import (
	"time"

	"gorm.io/gorm"
)

// Notification 通知模型
type Notification struct {
	ID        uint64         `json:"id" gorm:"primarykey"`
	UserID    uint64         `json:"user_id" gorm:"not null;index;comment:接收用户ID"`
	Type      string         `json:"type" gorm:"type:varchar(50);not null;comment:通知类型"`
	Title     string         `json:"title" gorm:"type:varchar(200);not null;comment:通知标题"`
	Content   string         `json:"content" gorm:"type:text;not null;comment:通知内容"`
	Data      string         `json:"data" gorm:"type:text;comment:额外数据(JSON格式)"`
	URL       string         `json:"url" gorm:"type:varchar(500);comment:跳转链接"`
	Level     string         `json:"level" gorm:"type:varchar(20);default:'info';comment:通知级别(info,warning,error,success)"`
	IsRead    bool           `json:"is_read" gorm:"default:false;comment:是否已读"`
	ReadAt    *time.Time     `json:"read_at" gorm:"comment:阅读时间"`
	ExpireAt  *time.Time     `json:"expire_at" gorm:"comment:过期时间"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

// TableName 设置表名
func (Notification) TableName() string {
	return "notifications"
}
