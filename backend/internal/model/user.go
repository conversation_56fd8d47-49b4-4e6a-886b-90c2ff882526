package model

import (
	"time"

	"gorm.io/datatypes"
)

// User 用户模型
type User struct {
	BaseModel
	UID         string         `gorm:"type:varchar(64);not null;uniqueIndex:idx_uid" json:"uid"`
	OpenID      string         `gorm:"type:varchar(128);not null;uniqueIndex:idx_openid" json:"openid"`
	UnionID     *string        `gorm:"type:varchar(128)" json:"unionid"`
	Phone       string         `gorm:"type:varchar(20);not null;uniqueIndex:idx_phone" json:"phone"`
	Nickname    string         `gorm:"type:varchar(50)" json:"nickname"`
	Avatar      string         `gorm:"type:varchar(255)" json:"avatar"`
	Gender      int            `gorm:"default:0" json:"gender"`                                     // 0:未知 1:男 2:女
	Source      string         `gorm:"type:varchar(20);not null;default:'mp-weixin'" json:"source"` // 用户来源：mp-weixin, app等
	Settings    datatypes.JSON `gorm:"type:jsonb;default:'{}'" json:"settings"`
	LastLoginAt *time.Time     `gorm:"type:timestamp(0)" json:"last_login_at"`
}

func (u User) TableName() string {
	return "users"
}

// UserProfile 用户资料
type UserProfile struct {
	UserID     uint      `gorm:"primarykey" json:"user_id"`
	Height     int       `json:"height"`
	Weight     int       `json:"weight"`
	Education  int       `json:"education"`
	Occupation string    `gorm:"size:100" json:"occupation"`
	Income     int       `json:"income"`
	Hobbies    string    `gorm:"type:text" json:"hobbies"`
	SelfIntro  string    `gorm:"type:text" json:"self_intro"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// UserSettings 用户设置
type UserSettings struct {
	UserID              uint      `gorm:"primarykey" json:"user_id"`
	AllowMessageFrom    int       `gorm:"default:1" json:"allow_message_from"` // 1:所有人 2:关注的人 3:不允许
	AllowLocationShare  bool      `gorm:"default:true" json:"allow_location_share"`
	AllowJobRecommend   bool      `gorm:"default:true" json:"allow_job_recommend"`
	AllowHouseRecommend bool      `gorm:"default:true" json:"allow_house_recommend"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}
