-- =====================================================
-- PostgreSQL 枚举类型操作指南
-- =====================================================

-- 1. 添加新的枚举值
-- 语法：ALTER TYPE enum_name ADD VALUE 'new_value' [BEFORE 'existing_value' | AFTER 'existing_value'];

-- 示例：向conversation_type添加新值
ALTER TYPE conversation_type ADD VALUE 'group';  -- 添加到末尾
ALTER TYPE conversation_type ADD VALUE 'channel' AFTER 'group';  -- 添加到指定位置

-- 示例：向message_type添加新值
ALTER TYPE message_type ADD VALUE 'sticker' AFTER 'image';
ALTER TYPE message_type ADD VALUE 'location';

-- 2. 重命名枚举值（需要通过更新实现）
-- PostgreSQL不支持直接重命名枚举值，需要以下步骤：

-- 方法1：添加新值，然后更新数据，最后删除旧值
BEGIN;
-- 1) 添加新值
ALTER TYPE conversation_type ADD VALUE 'single';
ALTER TYPE conversation_type ADD VALUE 'system';

-- 2) 更新现有数据
UPDATE conversations SET type = 'single' WHERE type = 'private';
UPDATE conversations SET type = 'system' WHERE type = 'system_notification';

-- 3) 删除旧值（需要先确保没有数据使用）
-- 注意：PostgreSQL不支持直接删除枚举值，需要重建类型
COMMIT;

-- 方法2：重建枚举类型（推荐用于大幅修改）
-- 1) 创建新的枚举类型
CREATE TYPE conversation_type_new AS ENUM ('single', 'system', 'group');

-- 2) 添加新列
ALTER TABLE conversations ADD COLUMN type_new conversation_type_new;

-- 3) 迁移数据
UPDATE conversations 
SET type_new = CASE 
    WHEN type = 'private' THEN 'single'::conversation_type_new
    WHEN type = 'system_notification' THEN 'system'::conversation_type_new
    ELSE type::text::conversation_type_new
END;

-- 4) 删除旧列，重命名新列
ALTER TABLE conversations DROP COLUMN type;
ALTER TABLE conversations RENAME COLUMN type_new TO type;
ALTER TABLE conversations ALTER COLUMN type SET NOT NULL;
ALTER TABLE conversations ALTER COLUMN type SET DEFAULT 'single';

-- 5) 删除旧类型
DROP TYPE conversation_type;
ALTER TYPE conversation_type_new RENAME TO conversation_type;

-- 3. 查看枚举类型信息
-- 查看所有枚举类型
SELECT t.typname, e.enumlabel, e.enumsortorder
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN ('conversation_type', 'message_type', 'message_status')
ORDER BY t.typname, e.enumsortorder;

-- 4. 注意事项
-- - 添加枚举值时，建议在事务中操作
-- - 不能删除正在使用的枚举值
-- - 重命名枚举值需要重建类型
-- - 枚举值的顺序影响比较操作 