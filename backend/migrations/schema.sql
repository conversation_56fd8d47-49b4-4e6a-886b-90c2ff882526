-- =====================================================
-- 聊天与通知系统数据库表结构
-- 基于设计方案 V2.0
-- 数据库：PostgreSQL 
-- 创建时间：2024-01-01
-- =====================================================

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS user_conversations CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;

-- 删除已存在的类型（如果存在）
DROP TYPE IF EXISTS conversation_type CASCADE;
DROP TYPE IF EXISTS message_type CASCADE;
DROP TYPE IF EXISTS message_status CASCADE;

-- 创建枚举类型
CREATE TYPE conversation_type AS ENUM ('single', 'system');
CREATE TYPE message_type AS ENUM ('text', 'image', 'voice', 'video', 'file', 'system');
CREATE TYPE message_status AS ENUM ('sent', 'delivered', 'read', 'revoked', 'failed');

-- =====================================================
-- 1. 会话表 (conversations)
-- 存储会话的基础信息，不包含频繁变化的字段
-- =====================================================
CREATE TABLE conversations (
    id          BIGSERIAL PRIMARY KEY,
    type        conversation_type NOT NULL DEFAULT 'single',
    user_id1    BIGINT NOT NULL,                    -- 用户ID1（单聊时保证user_id1 < user_id2，系统通知时为0）
    user_id2    BIGINT NOT NULL,                    -- 用户ID2（单聊时较大的用户ID，系统通知时为接收者ID）
    created_by  BIGINT NOT NULL,                    -- 会话创建者ID
    created_at  TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 确保同一对用户只能有一个会话
    CONSTRAINT uk_conversation_users UNIQUE (user_id1, user_id2, type)
);

-- 会话表索引
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_user1 ON conversations(user_id1);
CREATE INDEX idx_conversations_user2 ON conversations(user_id2);

-- 会话表注释
COMMENT ON TABLE conversations IS '会话基础信息表，存储单聊和系统通知会话';
COMMENT ON COLUMN conversations.id IS '会话唯一标识';
COMMENT ON COLUMN conversations.type IS '会话类型：single-单聊，system-系统通知';
COMMENT ON COLUMN conversations.user_id1 IS '用户ID1，单聊时较小的用户ID，系统通知时为0';
COMMENT ON COLUMN conversations.user_id2 IS '用户ID2，单聊时较大的用户ID，系统通知时为接收者ID';
COMMENT ON COLUMN conversations.created_by IS '会话创建者用户ID';

-- =====================================================
-- 2. 用户会话状态表 (user_conversations)
-- 存储每个用户对会话的个人设置和状态
-- =====================================================
CREATE TABLE user_conversations (
    user_id           BIGINT NOT NULL,
    conversation_id   BIGINT NOT NULL,
    is_visible        BOOLEAN NOT NULL DEFAULT true,    -- 会话是否对用户可见
    is_deleted        BOOLEAN NOT NULL DEFAULT false,   -- 用户是否删除了该会话
    deleted_at        TIMESTAMP(0),                      -- 会话删除时间
    
    -- 已读状态
    last_read_message_id BIGINT,                         -- 最后已读消息ID
    last_read_time    TIMESTAMP(0),                      -- 最后已读时间
    
    is_pinned         BOOLEAN NOT NULL DEFAULT false,   -- 是否置顶
    is_muted          BOOLEAN NOT NULL DEFAULT false,   -- 是否免打扰
    custom_name       VARCHAR(100),                      -- 用户自定义的会话名称
    last_active_time  TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 最后活跃时间（用于排序）
    created_at        TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at        TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id, conversation_id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- 用户会话状态表索引
CREATE INDEX idx_user_conversations_list ON user_conversations(user_id, is_visible, is_pinned, last_active_time DESC);
CREATE INDEX idx_user_conversations_active ON user_conversations(last_active_time DESC);

-- 用户会话状态表注释
COMMENT ON TABLE user_conversations IS '用户会话状态表，存储用户对会话的个人设置';
COMMENT ON COLUMN user_conversations.user_id IS '用户ID';
COMMENT ON COLUMN user_conversations.conversation_id IS '会话ID';
COMMENT ON COLUMN user_conversations.is_visible IS '会话是否对该用户可见';
COMMENT ON COLUMN user_conversations.is_deleted IS '用户是否主动删除该会话';
COMMENT ON COLUMN user_conversations.deleted_at IS '用户删除会话的时间点';
COMMENT ON COLUMN user_conversations.last_read_message_id IS '最后已读消息ID';
COMMENT ON COLUMN user_conversations.last_read_time IS '最后已读时间';
COMMENT ON COLUMN user_conversations.is_pinned IS '是否置顶该会话';
COMMENT ON COLUMN user_conversations.is_muted IS '是否对该会话开启免打扰';
COMMENT ON COLUMN user_conversations.custom_name IS '用户为该会话设置的自定义名称';
COMMENT ON COLUMN user_conversations.last_active_time IS '该会话的最后活跃时间，用于会话列表排序';

-- =====================================================
-- 3. 消息表 (messages)
-- 存储所有消息内容和状态信息
-- =====================================================
CREATE TABLE messages (
    id              BIGSERIAL PRIMARY KEY,
    msg_id          VARCHAR(36) NOT NULL UNIQUE,        -- 客户端生成的全局唯一消息ID
    conversation_id BIGINT NOT NULL,
    sender_id       BIGINT NOT NULL,                    -- 发送者ID，系统消息为0
    receiver_id     BIGINT NOT NULL,                    -- 接收者ID
    content         TEXT,                               -- 消息内容
    message_type    message_type NOT NULL DEFAULT 'text', -- 消息类型
    extra           JSONB,                              -- 扩展字段：图片尺寸、语音时长等
    status          message_status NOT NULL DEFAULT 'sent', -- 消息状态
    is_revoked      BOOLEAN NOT NULL DEFAULT false,    -- 是否已撤回
    revoked_at      TIMESTAMP(0),                       -- 撤回时间
    created_at      TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 消息创建时间
    delivered_at    TIMESTAMP(0),                       -- 消息送达时间
    read_at         TIMESTAMP(0),                       -- 消息已读时间
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- 消息表索引
CREATE UNIQUE INDEX uk_messages_msg_id ON messages(msg_id);
CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_sender ON messages(sender_id);
CREATE INDEX idx_messages_time ON messages(created_at DESC);
CREATE INDEX idx_messages_status ON messages(status);
CREATE INDEX idx_messages_unread ON messages(conversation_id, sender_id, status, id);

-- 消息表注释
COMMENT ON TABLE messages IS '消息表，存储所有聊天消息和系统通知';
COMMENT ON COLUMN messages.id IS '消息自增ID';
COMMENT ON COLUMN messages.msg_id IS '全局唯一消息ID，由客户端生成，用于防重复和跨端同步';
COMMENT ON COLUMN messages.conversation_id IS '所属会话ID';
COMMENT ON COLUMN messages.sender_id IS '发送者用户ID，系统消息时为0';
COMMENT ON COLUMN messages.content IS '消息文本内容';
COMMENT ON COLUMN messages.message_type IS '消息类型：text/image/voice/video/file/system';
COMMENT ON COLUMN messages.extra IS '扩展信息，JSON格式存储图片尺寸、语音时长等';
COMMENT ON COLUMN messages.status IS '消息状态：sent/delivered/read/revoked/failed';
COMMENT ON COLUMN messages.is_revoked IS '消息是否已被撤回';
COMMENT ON COLUMN messages.revoked_at IS '消息撤回时间';
COMMENT ON COLUMN messages.delivered_at IS '消息送达时间';
COMMENT ON COLUMN messages.read_at IS '消息已读时间';



-- =====================================================
-- 6. 创建更新时间触发器函数
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_user_conversations_updated_at 
    BEFORE UPDATE ON user_conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 7. 创建分区表（可选，用于大数据量场景）
-- =====================================================
-- 按时间分区messages表（可选）
-- CREATE TABLE messages_2024_01 PARTITION OF messages
-- FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- =====================================================
-- 8. 插入测试数据（可选）
-- =====================================================
-- 插入测试会话
-- INSERT INTO conversations (type, user_id1, user_id2, created_by) 
-- VALUES ('private', 1, 2, 1);

-- 插入测试用户会话状态
-- INSERT INTO user_conversations (user_id, conversation_id, is_visible, last_active_time)
-- VALUES (1, 1, true, CURRENT_TIMESTAMP), (2, 1, true, CURRENT_TIMESTAMP);

-- =====================================================
-- Schema创建完成
-- =====================================================
