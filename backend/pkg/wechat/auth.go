package wechat

import (
	"context"
	"errors"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/rs/zerolog/log"
)

// AuthService 微信认证服务接口
type AuthService interface {
	// GetSessionInfo 根据登录码获取会话信息（包含openid）
	GetSessionInfo(ctx context.Context, loginCode string) (*SessionInfo, error)
	// GetPhoneNumber 根据手机号码获取Code获取手机号
	GetPhoneNumber(ctx context.Context, phoneCode string) (*PhoneInfo, error)
}

type authService struct {
	miniProgram *miniProgram.MiniProgram
}

// NewAuthService 创建微信认证服务
func NewAuthService(miniProgram *miniProgram.MiniProgram) AuthService {
	return &authService{
		miniProgram: miniProgram,
	}
}

// GetSessionInfo 根据登录码获取会话信息
func (s *authService) GetSessionInfo(ctx context.Context, loginCode string) (*SessionInfo, error) {
	if loginCode == "" {
		return nil, errors.New("登录码不能为空")
	}

	// 调用微信接口获取会话信息
	session, err := s.miniProgram.Auth.Session(ctx, loginCode)
	if err != nil {
		log.Error().Err(err).Str("login_code", loginCode).Msg("Failed to get wechat session")
		return nil, errors.New("获取微信会话信息失败")
	}

	if session.ErrCode != 0 {
		log.Error().Int("err_code", session.ErrCode).Str("err_msg", session.ErrMsg).Msg("Wechat session error")
		return nil, errors.New("微信登录失败: " + session.ErrMsg)
	}

	return &SessionInfo{
		OpenID:     session.OpenID,
		SessionKey: session.SessionKey,
		UnionID:    session.UnionID,
		ErrCode:    session.ErrCode,
		ErrMsg:     session.ErrMsg,
	}, nil
}

// GetPhoneNumber 根据手机号码获取Code获取手机号
func (s *authService) GetPhoneNumber(ctx context.Context, phoneCode string) (*PhoneInfo, error) {
	if phoneCode == "" {
		return nil, errors.New("手机号码授权码不能为空")
	}

	phoneNumber, err := s.miniProgram.PhoneNumber.GetUserPhoneNumber(ctx, phoneCode)
	if err != nil {
		log.Error().Err(err).Str("phone_code", phoneCode).Msg("Failed to get wechat phone number")
		return nil, errors.New("获取手机号失败")
	}

	return &PhoneInfo{
		PhoneNumber:     phoneNumber.PhoneInfo.PhoneNumber,
		PurePhoneNumber: phoneNumber.PhoneInfo.PurePhoneNumber,
		CountryCode:     phoneNumber.PhoneInfo.CountryCode,
	}, nil
}
