package wechat

import (
	"bdb-backend/pkg/config"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
)

// Client 微信客户端
type Client struct {
	miniProgram *miniProgram.MiniProgram
	config      *config.Config
}

// NewClient 创建微信客户端
func NewClient(cfg *config.Config) (*Client, error) {
	miniProgram, err := NewMiniProgram(cfg)
	if err != nil {
		return nil, err
	}

	return &Client{
		miniProgram: miniProgram,
		config:      cfg,
	}, nil
}

// GetMiniProgram 获取小程序实例
func (c *Client) GetMiniProgram() *miniProgram.MiniProgram {
	return c.miniProgram
}

// NewMiniProgram 创建微信小程序实例
func NewMiniProgram(cfg *config.Config) (*miniProgram.MiniProgram, error) {
	miniConfig := &miniProgram.UserConfig{
		AppID:  cfg.WeChat.AppID,
		Secret: cfg.WeChat.AppSecret,

		HttpDebug: cfg.Server.Mode == "debug",
		Debug:     cfg.Server.Mode == "debug",
	}

	return miniProgram.NewMiniProgram(miniConfig)
}
