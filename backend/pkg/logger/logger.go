package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"bdb-backend/pkg/config"

	"github.com/rs/zerolog"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	Log zerolog.Logger
)

// Init initializes the logger with the given configuration.
func Init(cfg config.LoggerConfig, appMode string) {
	var writers []io.Writer

	// Configure console writer for development
	if appMode == "debug" {
		writers = append(writers, zerolog.NewConsoleWriter(func(w *zerolog.ConsoleWriter) {
			w.Out = os.Stderr
			w.TimeFormat = time.DateTime
		}))
	}

	// Configure file writer for all environments
	fileLogger := &lumberjack.Logger{
		Filename:   getLogFile(cfg.OutputPaths),
		MaxSize:    cfg.Rotation.MaxSize,
		MaxBackups: cfg.Rotation.MaxBackups,
		MaxAge:     cfg.Rotation.MaxAge,
		Compress:   cfg.Rotation.Compress,
	}
	writers = append(writers, fileLogger)

	// Create a multi-level writer
	mw := io.MultiWriter(writers...)

	// Set log level
	level, err := zerolog.ParseLevel(cfg.Level)
	if err != nil {
		level = zerolog.InfoLevel
	}

	// Create the logger
	Log = zerolog.New(mw).
		Level(level).
		With().
		Timestamp().
		Caller().
		Logger()
}

// getLogFile 获取日志文件路径，使用app-2025-03-19格式
func getLogFile(paths []string) string {
	baseDir := "./logs"

	// 从配置的路径中提取目录
	for _, p := range paths {
		if !strings.Contains(p, "stdout") && !strings.Contains(p, "stderr") {
			baseDir = filepath.Dir(p)
			break
		}
	}

	// 确保日志目录存在
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		fmt.Printf("Failed to create log directory: %v\n", err)
	}

	// 生成格式为 app-2025-03-19.log 的文件名
	today := time.Now().Format("2006-01-02")
	filename := fmt.Sprintf("app-%s.log", today)

	return filepath.Join(baseDir, filename)
}

// ============= 基础日志方法 =============

// Debug 记录调试信息
func Debug(message string, fields ...interface{}) {
	logWithFields(Log.Debug(), message, fields...)
}

// Info 记录一般信息
func Info(message string, fields ...interface{}) {
	logWithFields(Log.Info(), message, fields...)
}

// Warn 记录警告信息
func Warn(message string, fields ...interface{}) {
	logWithFields(Log.Warn(), message, fields...)
}

// Error 记录错误信息
func Error(message string, err error, fields ...interface{}) {
	event := Log.Error()
	if err != nil {
		event = event.Err(err)
	}
	logWithFields(event, message, fields...)
}

// Fatal 记录致命错误并退出程序
func Fatal(message string, err error, fields ...interface{}) {
	event := Log.Fatal()
	if err != nil {
		event = event.Err(err)
	}
	logWithFields(event, message, fields...)
}

// ============= 带上下文的日志方法 =============

// DebugCtx 带上下文的调试日志
func DebugCtx(ctx context.Context, message string, fields ...interface{}) {
	logWithContext(ctx, Log.Debug(), message, fields...)
}

// InfoCtx 带上下文的信息日志
func InfoCtx(ctx context.Context, message string, fields ...interface{}) {
	logWithContext(ctx, Log.Info(), message, fields...)
}

// WarnCtx 带上下文的警告日志
func WarnCtx(ctx context.Context, message string, fields ...interface{}) {
	logWithContext(ctx, Log.Warn(), message, fields...)
}

// ErrorCtx 带上下文的错误日志
func ErrorCtx(ctx context.Context, message string, err error, fields ...interface{}) {
	event := Log.Error()
	if err != nil {
		event = event.Err(err)
	}
	logWithContext(ctx, event, message, fields...)
}

// ============= 辅助方法 =============

// logWithFields 处理字段参数的通用方法
func logWithFields(event *zerolog.Event, message string, fields ...interface{}) {
	// 处理fields参数，支持key-value对
	for i := 0; i < len(fields)-1; i += 2 {
		key, ok := fields[i].(string)
		if !ok {
			continue
		}
		value := fields[i+1]
		event = event.Interface(key, value)
	}
	event.Msg(message)
}

// logWithContext 带上下文的日志记录
func logWithContext(ctx context.Context, event *zerolog.Event, message string, fields ...interface{}) {
	event = event.Ctx(ctx)

	// 尝试从上下文中提取请求ID等信息
	if requestID := ctx.Value("request_id"); requestID != nil {
		event = event.Interface("request_id", requestID)
	}

	if userID := ctx.Value("user_id"); userID != nil {
		event = event.Interface("user_id", userID)
	}

	logWithFields(event, message, fields...)
}
