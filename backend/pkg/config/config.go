package config

import (
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
)

type Config struct {
	Server       ServerConfig       `yaml:"server"`
	Database     DatabaseConfig     `yaml:"database"`
	Redis        RedisConfig        `yaml:"redis"`
	JWT          JWTConfig          `yaml:"jwt"`
	Storage      StorageConfig      `yaml:"storage"`
	SMS          SMSConfig          `yaml:"sms"`
	Payment      PaymentConfig      `yaml:"payment"`
	WeChat       WeChatConfig       `yaml:"wechat"`
	Logger       LoggerConfig       `yaml:"logger"`
	Centrifugo   CentrifugoConfig   `yaml:"centrifugo"`
	CORS         CORSConfig         `yaml:"cors"`
	RateLimit    RateLimitConfig    `yaml:"rate_limit"`
	Security     SecurityConfig     `yaml:"security"`
	FileUpload   FileUploadConfig   `yaml:"file_upload"`
	Cache        CacheConfig        `yaml:"cache"`
	Notification NotificationConfig `yaml:"notification"`
	Monitoring   MonitoringConfig   `yaml:"monitoring"`
	Swagger      SwaggerConfig      `yaml:"swagger"`
}

type ServerConfig struct {
	Port         string        `yaml:"port"`
	Mode         string        `yaml:"mode"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
}

type DatabaseConfig struct {
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	User         string `yaml:"user"`
	Password     string `yaml:"password"`
	DBName       string `yaml:"dbname"`
	SSLMode      string `yaml:"sslmode"`
	MaxIdleConns int    `yaml:"max_idle_conns"`
	MaxOpenConns int    `yaml:"max_open_conns"`
}

type RedisConfig struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

type JWTConfig struct {
	Secret string        `yaml:"secret"`
	TTL    time.Duration `yaml:"ttl"`
}

type StorageConfig struct {
	Qiniu QiniuConfig `yaml:"qiniu"`
}

type QiniuConfig struct {
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
	Bucket    string `yaml:"bucket"`
	Domain    string `yaml:"domain"`
}

type SMSConfig struct {
	Aliyun AliyunSMSConfig `yaml:"aliyun"`
}

type AliyunSMSConfig struct {
	AccessKey    string `yaml:"access_key"`
	SecretKey    string `yaml:"secret_key"`
	SignName     string `yaml:"sign_name"`
	TemplateCode string `yaml:"template_code"`
}

type PaymentConfig struct {
	Wechat WechatConfig `yaml:"wechat"`
	Alipay AlipayConfig `yaml:"alipay"`
}

type WeChatConfig struct {
	AppID     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
}

type WechatConfig struct {
	AppID     string `yaml:"app_id"`
	MchID     string `yaml:"mch_id"`
	APIKey    string `yaml:"api_key"`
	AppSecret string `yaml:"app_secret"`
}

type AlipayConfig struct {
	AppID      string `yaml:"app_id"`
	PrivateKey string `yaml:"private_key"`
	PublicKey  string `yaml:"public_key"`
}

type LoggerConfig struct {
	Level            string         `yaml:"level"`
	Format           string         `yaml:"format"`
	OutputPaths      []string       `yaml:"output_paths"`
	ErrorOutputPaths []string       `yaml:"error_output_paths"`
	Rotation         RotationConfig `yaml:"rotation"`
}

type RotationConfig struct {
	MaxSize    int  `yaml:"max_size"`
	MaxAge     int  `yaml:"max_age"`
	MaxBackups int  `yaml:"max_backups"`
	Compress   bool `yaml:"compress"`
}

type CentrifugoConfig struct {
	URL        string `yaml:"url"`
	APIKey     string `yaml:"api_key"`
	HMACSecret string `yaml:"hmac_secret"`
	GRPCPort   int    `yaml:"grpc_port"`
}

type CORSConfig struct {
	Enabled          bool     `yaml:"enabled"`
	AllowedOrigins   []string `yaml:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
}

type RateLimitConfig struct {
	Enabled           bool `yaml:"enabled"`
	RequestsPerSecond int  `yaml:"requests_per_second"`
	Burst             int  `yaml:"burst"`
}

type SecurityConfig struct {
	BcryptCost             int           `yaml:"bcrypt_cost"`
	MaxLoginAttempts       int           `yaml:"max_login_attempts"`
	LockoutDuration        time.Duration `yaml:"lockout_duration"`
	PasswordMinLength      int           `yaml:"password_min_length"`
	PasswordRequireSpecial bool          `yaml:"password_require_special"`
	PasswordRequireNumber  bool          `yaml:"password_require_number"`
	PasswordRequireUpper   bool          `yaml:"password_require_uppercase"`
	PasswordRequireLower   bool          `yaml:"password_require_lowercase"`
}

type FileUploadConfig struct {
	MaxFileSize  int64    `yaml:"max_file_size"`
	AllowedTypes []string `yaml:"allowed_types"`
	UploadPath   string   `yaml:"upload_path"`
}

type CacheConfig struct {
	Enabled bool          `yaml:"enabled"`
	TTL     time.Duration `yaml:"ttl"`
	Prefix  string        `yaml:"prefix"`
}

type NotificationConfig struct {
	Enabled     bool        `yaml:"enabled"`
	PushService string      `yaml:"push_service"`
	JPush       JPushConfig `yaml:"jpush"`
}

type JPushConfig struct {
	AppKey         string `yaml:"app_key"`
	MasterSecret   string `yaml:"master_secret"`
	ApnsProduction bool   `yaml:"apns_production"`
}

type MonitoringConfig struct {
	Enabled     bool              `yaml:"enabled"`
	Prometheus  PrometheusConfig  `yaml:"prometheus"`
	HealthCheck HealthCheckConfig `yaml:"health_check"`
}

type PrometheusConfig struct {
	Port string `yaml:"port"`
	Path string `yaml:"path"`
}

type HealthCheckConfig struct {
	Enabled bool   `yaml:"enabled"`
	Path    string `yaml:"path"`
}

type SwaggerConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Title       string `yaml:"title"`
	Description string `yaml:"description"`
	Version     string `yaml:"version"`
	Host        string `yaml:"host"`
	BasePath    string `yaml:"base_path"`
}

var Conf *Config

func Load() *Config {
	env := os.Getenv("GO_ENV")
	if env == "" {
		env = "dev"
	}

	configName := "config"
	if env == "dev" {
		configName = "config.dev"
	} else if env == "prod" {
		configName = "config.prod"
	}

	viper.SetConfigName(configName)
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("./")

	// 自动绑定环境变量
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}

	if err := viper.Unmarshal(&Conf); err != nil {
		log.Fatalf("Error unmarshaling config: %v", err)
	}

	return Conf
}
