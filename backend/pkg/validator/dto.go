package validator

import "time"

// 用户相关验证结构体

// LoginRequest 登录请求
type LoginRequest struct {
	Phone    string `json:"phone" form:"phone" binding:"required,phone" validate:"required,phone"`
	Password string `json:"password" form:"password" binding:"required,min=8,max=20" validate:"required,min=8,max=20"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Phone           string `json:"phone" form:"phone" binding:"required,phone" validate:"required,phone"`
	Password        string `json:"password" form:"password" binding:"required,password" validate:"required,password"`
	ConfirmPassword string `json:"confirm_password" form:"confirm_password" binding:"required,eqfield=Password" validate:"required,eqfield=Password"`
	Code            string `json:"code" form:"code" binding:"required,len=6,numeric" validate:"required,len=6,numeric"`
	NickName        string `json:"nick_name" form:"nick_name" binding:"required,min=2,max=20" validate:"required,min=2,max=20"`
}

// UpdatePasswordRequest 修改密码请求
type UpdatePasswordRequest struct {
	OldPassword     string `json:"old_password" form:"old_password" binding:"required" validate:"required"`
	NewPassword     string `json:"new_password" form:"new_password" binding:"required,password" validate:"required,password"`
	ConfirmPassword string `json:"confirm_password" form:"confirm_password" binding:"required,eqfield=NewPassword" validate:"required,eqfield=NewPassword"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Phone           string `json:"phone" form:"phone" binding:"required,phone" validate:"required,phone"`
	Code            string `json:"code" form:"code" binding:"required,len=6,numeric" validate:"required,len=6,numeric"`
	NewPassword     string `json:"new_password" form:"new_password" binding:"required,password" validate:"required,password"`
	ConfirmPassword string `json:"confirm_password" form:"confirm_password" binding:"required,eqfield=NewPassword" validate:"required,eqfield=NewPassword"`
}

// UserProfileRequest 用户资料请求
type UserProfileRequest struct {
	NickName string `json:"nick_name" form:"nick_name" binding:"required,min=2,max=20" validate:"required,min=2,max=20"`
	Avatar   string `json:"avatar" form:"avatar" binding:"omitempty,url" validate:"omitempty,url"`
	Gender   int    `json:"gender" form:"gender" binding:"oneof=0 1 2" validate:"oneof=0 1 2"` // 0:未知, 1:男, 2:女
	Birthday string `json:"birthday" form:"birthday" binding:"omitempty,datetime=2006-01-02" validate:"omitempty,datetime=2006-01-02"`
	RealName string `json:"real_name" form:"real_name" binding:"omitempty,min=2,max=10,chinese" validate:"omitempty,min=2,max=10,chinese"`
	IDCard   string `json:"id_card" form:"id_card" binding:"omitempty,idcard" validate:"omitempty,idcard"`
	Location string `json:"location" form:"location" binding:"omitempty,min=2,max=50" validate:"omitempty,min=2,max=50"`
	Bio      string `json:"bio" form:"bio" binding:"omitempty,max=200" validate:"omitempty,max=200"`
}

// 分页查询相关

// PageRequest 分页请求
type PageRequest struct {
	Page     int `json:"page" form:"page" binding:"omitempty,gte=1" validate:"omitempty,gte=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"omitempty,gte=1,lte=100" validate:"omitempty,gte=1,lte=100"`
}

// SearchRequest 搜索请求
type SearchRequest struct {
	PageRequest
	Keyword string `json:"keyword" form:"keyword" binding:"omitempty,min=1,max=50" validate:"omitempty,min=1,max=50"`
}

// 时间范围查询

// TimeRangeRequest 时间范围请求
type TimeRangeRequest struct {
	StartTime string `json:"start_time" form:"start_time" binding:"omitempty,datetime=2006-01-02 15:04:05" validate:"omitempty,datetime=2006-01-02 15:04:05"`
	EndTime   string `json:"end_time" form:"end_time" binding:"omitempty,datetime=2006-01-02 15:04:05" validate:"omitempty,datetime=2006-01-02 15:04:05"`
}

// 文件上传相关

// FileUploadRequest 文件上传请求
type FileUploadRequest struct {
	FileName string `json:"file_name" form:"file_name" binding:"required,min=1,max=100" validate:"required,min=1,max=100"`
	FileSize int64  `json:"file_size" form:"file_size" binding:"required,gt=0" validate:"required,gt=0"`
	FileType string `json:"file_type" form:"file_type" binding:"required,oneof=image video audio document" validate:"required,oneof=image video audio document"`
	FileMD5  string `json:"file_md5" form:"file_md5" binding:"required,len=32,alphanum" validate:"required,len=32,alphanum"`
}

// 短信验证相关

// SendSMSRequest 发送短信请求
type SendSMSRequest struct {
	Phone string `json:"phone" form:"phone" binding:"required,phone" validate:"required,phone"`
	Type  string `json:"type" form:"type" binding:"required,oneof=login register reset_password bind_phone" validate:"required,oneof=login register reset_password bind_phone"`
}

// VerifySMSRequest 验证短信请求
type VerifySMSRequest struct {
	Phone string `json:"phone" form:"phone" binding:"required,phone" validate:"required,phone"`
	Code  string `json:"code" form:"code" binding:"required,len=6,numeric" validate:"required,len=6,numeric"`
	Type  string `json:"type" form:"type" binding:"required,oneof=login register reset_password bind_phone" validate:"required,oneof=login register reset_password bind_phone"`
}

// 零工相关

// GigCreateRequest 创建零工请求
type GigCreateRequest struct {
	Title        string    `json:"title" form:"title" binding:"required,min=5,max=100" validate:"required,min=5,max=100"`
	Description  string    `json:"description" form:"description" binding:"required,min=10,max=1000" validate:"required,min=10,max=1000"`
	Category     string    `json:"category" form:"category" binding:"required,min=2,max=20" validate:"required,min=2,max=20"`
	Budget       int       `json:"budget" form:"budget" binding:"required,gte=0" validate:"required,gte=0"`
	Location     string    `json:"location" form:"location" binding:"required,min=2,max=50" validate:"required,min=2,max=50"`
	StartTime    time.Time `json:"start_time" form:"start_time" binding:"required" validate:"required"`
	EndTime      time.Time `json:"end_time" form:"end_time" binding:"required" validate:"required"`
	Requirements string    `json:"requirements" form:"requirements" binding:"omitempty,max=500" validate:"omitempty,max=500"`
	Tags         []string  `json:"tags" form:"tags" binding:"omitempty,dive,min=1,max=20" validate:"omitempty,dive,min=1,max=20"`
}

// GigSearchRequest 零工搜索请求
type GigSearchRequest struct {
	PageRequest
	Keyword   string `json:"keyword" form:"keyword" binding:"omitempty,min=1,max=50" validate:"omitempty,min=1,max=50"`
	Category  string `json:"category" form:"category" binding:"omitempty,min=2,max=20" validate:"omitempty,min=2,max=20"`
	Location  string `json:"location" form:"location" binding:"omitempty,min=2,max=50" validate:"omitempty,min=2,max=50"`
	MinBudget int    `json:"min_budget" form:"min_budget" binding:"omitempty,gte=0" validate:"omitempty,gte=0"`
	MaxBudget int    `json:"max_budget" form:"max_budget" binding:"omitempty,gte=0" validate:"omitempty,gte=0"`
	SortBy    string `json:"sort_by" form:"sort_by" binding:"omitempty,oneof=created_at budget deadline" validate:"omitempty,oneof=created_at budget deadline"`
	SortOrder string `json:"sort_order" form:"sort_order" binding:"omitempty,oneof=asc desc" validate:"omitempty,oneof=asc desc"`
}

// 房屋相关

// HouseCreateRequest 创建房屋请求
type HouseCreateRequest struct {
	Title       string   `json:"title" form:"title" binding:"required,min=5,max=100" validate:"required,min=5,max=100"`
	Description string   `json:"description" form:"description" binding:"required,min=10,max=1000" validate:"required,min=10,max=1000"`
	Type        string   `json:"type" form:"type" binding:"required,oneof=rent sale" validate:"required,oneof=rent sale"`
	Price       int      `json:"price" form:"price" binding:"required,gt=0" validate:"required,gt=0"`
	Area        float64  `json:"area" form:"area" binding:"required,gt=0" validate:"required,gt=0"`
	Room        int      `json:"room" form:"room" binding:"required,gte=1" validate:"required,gte=1"`
	Hall        int      `json:"hall" form:"hall" binding:"required,gte=1" validate:"required,gte=1"`
	Bathroom    int      `json:"bathroom" form:"bathroom" binding:"required,gte=1" validate:"required,gte=1"`
	Floor       int      `json:"floor" form:"floor" binding:"required,gte=1" validate:"required,gte=1"`
	TotalFloor  int      `json:"total_floor" form:"total_floor" binding:"required,gte=1" validate:"required,gte=1"`
	Address     string   `json:"address" form:"address" binding:"required,min=5,max=100" validate:"required,min=5,max=100"`
	Facilities  []string `json:"facilities" form:"facilities" binding:"omitempty,dive,min=1,max=20" validate:"omitempty,dive,min=1,max=20"`
	Images      []string `json:"images" form:"images" binding:"required,min=1,max=10,dive,url" validate:"required,min=1,max=10,dive,url"`
}

// 聊天相关

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	ToUserID uint   `json:"to_user_id" form:"to_user_id" binding:"required,gt=0" validate:"required,gt=0"`
	Content  string `json:"content" form:"content" binding:"required,min=1,max=1000" validate:"required,min=1,max=1000"`
	Type     string `json:"type" form:"type" binding:"required,oneof=text image voice video file" validate:"required,oneof=text image voice video file"`
}

// 举报相关

// ReportRequest 举报请求
type ReportRequest struct {
	TargetID   uint   `json:"target_id" form:"target_id" binding:"required,gt=0" validate:"required,gt=0"`
	TargetType string `json:"target_type" form:"target_type" binding:"required,oneof=user post comment gig house" validate:"required,oneof=user post comment gig house"`
	Reason     string `json:"reason" form:"reason" binding:"required,min=2,max=20" validate:"required,min=2,max=20"`
	Content    string `json:"content" form:"content" binding:"required,min=5,max=200" validate:"required,min=5,max=200"`
}

// 邮箱绑定相关

// BindEmailRequest 绑定邮箱请求
type BindEmailRequest struct {
	Email string `json:"email" form:"email" binding:"required,email" validate:"required,email"`
	Code  string `json:"code" form:"code" binding:"required,len=6,numeric" validate:"required,len=6,numeric"`
}

// 收藏相关

// FavoriteRequest 收藏请求
type FavoriteRequest struct {
	TargetID   uint   `json:"target_id" form:"target_id" binding:"required,gt=0" validate:"required,gt=0"`
	TargetType string `json:"target_type" form:"target_type" binding:"required,oneof=post gig house" validate:"required,oneof=post gig house"`
}

// 评论相关

// CommentCreateRequest 创建评论请求
type CommentCreateRequest struct {
	TargetID   uint   `json:"target_id" form:"target_id" binding:"required,gt=0" validate:"required,gt=0"`
	TargetType string `json:"target_type" form:"target_type" binding:"required,oneof=post gig house" validate:"required,oneof=post gig house"`
	Content    string `json:"content" form:"content" binding:"required,min=1,max=500" validate:"required,min=1,max=500"`
	ParentID   uint   `json:"parent_id" form:"parent_id" binding:"omitempty,gt=0" validate:"omitempty,gt=0"`
}
