//go:build wireinject
// +build wireinject

package chat

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/service"
	"bdb-backend/pkg/auth"
	"bdb-backend/pkg/centrifugo"

	"github.com/google/wire"
)

// ChatProviderSet Wire Provider集合
var ChatProviderSet = wire.NewSet(
	// Centrifugo客户端
	centrifugo.NewClient,

	// JWT服务
	auth.NewJWTService,

	// 聊天服务
	service.NewChatService,

	// 通知服务
	service.NewNotificationService,

	// 聊天控制器
	controller.NewChatController,
)
