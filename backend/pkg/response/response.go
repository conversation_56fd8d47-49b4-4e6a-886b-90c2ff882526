package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginatedData 分页数据结构
type PaginatedData struct {
	List     interface{} `json:"list"`     // 数据列表
	Total    int64       `json:"total"`    // 总记录数
	Page     int         `json:"page"`     // 当前页码
	PageSize int         `json:"pageSize"` // 每页大小
	Pages    int         `json:"pages"`    // 总页数
}

// Success 成功响应
func Success(data interface{}) Response {
	return Response{
		Code:    CodeSuccess,
		Message: MessageSuccess,
		Data:    data,
	}
}

// Error 错误响应
func Error(code int, message string) Response {
	return Response{
		Code:    code,
		Message: message,
	}
}

// ErrorWithData 带数据的错误响应
func ErrorWithData(code int, message string, data interface{}) Response {
	return Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// Paginated 分页响应
func Paginated(data interface{}, total int64, page, pageSize int) Response {
	// 计算总页数
	pages := int((total + int64(pageSize) - 1) / int64(pageSize))

	paginatedData := PaginatedData{
		List:     data,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Pages:    pages,
	}

	return Response{
		Code:    CodeSuccess,
		Message: MessageSuccess,
		Data:    paginatedData,
	}
}

// ============= 便捷方法 =============

// OK 返回成功响应 - 保留一个简单的便捷方法
func OK(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, Success(data))
}

// Fail 返回错误响应 - 保留一个简单的便捷方法
func Fail(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusOK, Error(CodeError, message))
}

// ServerErrorJSON 500错误响应
func ServerErrorJSON(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageServerError
	}
	ctx.JSON(HTTPStatusInternalServerError, Error(CodeError, message))
}

// BadRequestJSON 400错误响应
func BadRequest(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageBadRequest
	}
	ctx.JSON(HTTPStatusBadRequest, Error(CodeError, message))
}

// UnauthorizedJSON 401错误响应
func Unauthorized(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageUnauthorized
	}
	ctx.JSON(HTTPStatusUnauthorized, Error(CodeError, message))
}

// ============= 扩展便捷方法 =============

// Forbidden 403错误响应
func Forbidden(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageForbidden
	}
	ctx.JSON(HTTPStatusForbidden, Error(CodeError, message))
}

// NotFound 404错误响应
func NotFound(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageNotFound
	}
	ctx.JSON(HTTPStatusNotFound, Error(CodeError, message))
}

// TooManyRequests 429错误响应
func TooManyRequests(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageTooManyRequests
	}
	ctx.JSON(HTTPStatusTooManyRequests, Error(CodeError, message))
}

// ServiceUnavailable 503错误响应
func ServiceUnavailable(ctx *gin.Context, message string) {
	if message == "" {
		message = MessageServiceUnavailable
	}
	ctx.JSON(HTTPStatusServiceUnavailable, Error(CodeError, message))
}
