package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginatedResponse 分页响应结构
type PaginatedResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
}

// 常用状态码
const (
	CodeSuccess      = 0
	CodeError        = 1
	CodeBadRequest   = 400
	CodeUnauthorized = 401
	CodeForbidden    = 403
	CodeNotFound     = 404
	CodeServerError  = 500
)

// 常用消息
const (
	MsgSuccess      = "success"
	MsgBadRequest   = "bad request"
	MsgUnauthorized = "unauthorized"
	MsgForbidden    = "forbidden"
	MsgNotFound     = "not found"
	MsgServerError  = "internal server error"
)

// Success 成功响应
func Success(data interface{}) Response {
	return Response{
		Code:    CodeSuccess,
		Message: MsgSuccess,
		Data:    data,
	}
}

// Error 错误响应
func Error(code int, message string) Response {
	return Response{
		Code:    code,
		Message: message,
	}
}

// ErrorWithData 带数据的错误响应
func ErrorWithData(code int, message string, data interface{}) Response {
	return Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// Paginated 分页响应
func Paginated(data interface{}, total int64, page, size int) PaginatedResponse {
	return PaginatedResponse{
		Code:    CodeSuccess,
		Message: MsgSuccess,
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
	}
}

// ============= 便捷方法 =============

// OK 返回成功响应 - 保留一个简单的便捷方法
func OK(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, Success(data))
}

// Fail 返回错误响应 - 保留一个简单的便捷方法
func Fail(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusOK, Error(CodeError, message))
}

// ServerErrorJSON 500错误响应
func ServerErrorJSON(ctx *gin.Context, message string) {
	if message == "" {
		message = MsgServerError
	}
	ctx.JSON(http.StatusInternalServerError, Error(CodeServerError, message))
}

// BadRequestJSON 400错误响应
func BadRequest(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusBadRequest, Error(CodeBadRequest, message))
}

// UnauthorizedJSON 401错误响应
func Unauthorized(ctx *gin.Context, message string) {
	if message == "" {
		message = MsgUnauthorized
	}
	ctx.JSON(http.StatusUnauthorized, Error(CodeUnauthorized, message))
}
