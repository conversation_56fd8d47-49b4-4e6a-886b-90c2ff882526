# Makefile
.PHONY: build run dev test clean docker-build docker-run

# 项目名称
PROJECT_NAME=bdb-backend
BINARY_NAME=server

# 构建
build:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME) cmd/server/main.go

# 运行
run:
	go run cmd/server/main.go

# 开发模式（热重载）
dev:
	air -c .air.toml

# 测试
test:
	go test -v ./...

# 清理
clean:
	rm -rf bin/

# Docker构建
docker-build:
	docker build -t $(PROJECT_NAME):latest .

# Docker运行
docker-run:
	docker-compose up -d

# 停止Docker
docker-stop:
	docker-compose down

# 数据库迁移
migrate-up:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" up

migrate-down:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" down

# 代码格式化
fmt:
	go fmt ./...
	gofumpt -w .

# 代码检查
lint:
	golangci-lint run

# 生成API文档
docs:
	swag init -g cmd/server/main.go -o ./docs

# 安装依赖
deps:
	go mod tidy
	go mod download 