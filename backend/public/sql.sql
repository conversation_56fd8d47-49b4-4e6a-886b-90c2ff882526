--
-- 用户信息表 `users`
--

-- 自动更新 updated_at 的函数和触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 删除旧表（如果存在），方便重复执行
DROP TABLE IF EXISTS "public"."users";

-- 创建新表
CREATE TABLE "public"."users" (
    "id" BIGSERIAL PRIMARY KEY,
    "uid" VARCHAR(64) NOT NULL UNIQUE,
    "openid" VARCHAR(128) NOT NULL UNIQUE,
    "unionid" VARCHAR(128),
    "phone" VARCHAR(20) NOT NULL UNIQUE,
    "nickname" VARCHAR(50),
    "avatar" VARCHAR(255),
    "gender" SMALLINT DEFAULT 0,
    "settings" JSONB DEFAULT '{}'::jsonb,
    "last_login_at" TIMESTAMP(0) WITHOUT TIME ZONE,
    "created_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_del" BIGINT DEFAULT 0
);

-- 添加注释
COMMENT ON TABLE "public"."users" IS '用户信息表';
COMMENT ON COLUMN "public"."users"."id" IS '主键ID';
COMMENT ON COLUMN "public"."users"."uid" IS '业务用户ID，用于对外暴露';
COMMENT ON COLUMN "public"."users"."openid" IS '微信小程序唯一标识';
COMMENT ON COLUMN "public"."users"."unionid" IS '微信开放平台唯一标识';
COMMENT ON COLUMN "public"."users"."phone" IS '手机号码';
COMMENT ON COLUMN "public"."users"."nickname" IS '用户昵称';
COMMENT ON COLUMN "public"."users"."avatar" IS '用户头像URL';
COMMENT ON COLUMN "public"."users"."gender" IS '性别: 0-未知, 1-男, 2-女';
COMMENT ON COLUMN "public"."users"."settings" IS '用户设置';
COMMENT ON COLUMN "public"."users"."last_login_at" IS '最后登录时间';
COMMENT ON COLUMN "public"."users"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."users"."is_del" IS '是否删除: 0-未删除, >0-已删除 (存储删除时间戳)';

-- 创建索引
-- UNIQUE 约束会自动创建唯一索引，所以 openid, phone, uid 字段无需重复创建索引
CREATE INDEX "idx_users_is_del" ON "public"."users" ("is_del");

-- 创建触发器
CREATE TRIGGER users_updated_at_modtime
BEFORE UPDATE ON "public"."users"
FOR EACH ROW
EXECUTE PROCEDURE update_updated_at_column();