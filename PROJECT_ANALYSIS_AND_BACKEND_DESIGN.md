# 项目功能分析与后端架构设计

本文档旨在全面分析 `fnbdb-mini` 前端项目的功能，并基于此设计一个稳健、可扩展的 Golang 后端服务架构。

## 1. 前端项目功能分析

通过分析 `pages.json` 和核心页面组件，总结出项目具备以下功能模块和特点：

### 1.1 核心 TabBar 模块

应用底部导航栏包含四个主要入口：

-   **首页 (`pages/home/<USER>
-   **社区 (`pages.post/list`)**: 一个通用的社区论坛模块，用户可以在此发布动态、进行评论和互动，独立于其他业务模块。
-   **消息 (`pages/message/conversation`)**: 即时通讯模块，支持用户之间进行私信聊天。
-   **我的 (`pages/mine/mine`)**: 个人中心，包含用户资料管理、我的收藏、我的发布、设置等功能。

### 1.2 业务功能模块 (分包)

项目采用了分包（Sub-packages）机制来按需加载，核心业务功能如下：

-   **招聘模块 (`/job`)**:
    -   **功能**: 面向求职者和招聘方。提供职位列表、详情、搜索、筛选功能。支持在线简历创建、编辑和投递。企业方可进行认证、发布和管理职位。
    -   **关键数据字段**: 职位类型、职位名称、薪资范围、工作地点、联系电话、学历要求、经验要求、职位描述、福利标签。

-   **房产模块 (`/house`)**:
    -   **功能**: 覆盖新房、二手房、租房、商铺等多种房产类型。支持房源发布、列表、详情、筛选和管理。
    -   **关键数据字段 (以租房为例)**: 出租方式 (整租/合租)、小区名称、楼栋号、面积、户型、朝向、租金、付款方式、包含费用、房源图片、配套设施、亮点标签、房源描述。

-   **零工模块 (`/gig`)**:
    -   **功能**: 用于发布和承接短期、临时性的工作任务。支持任务列表、详情、发布、报名管理和个人主页展示。
    -   **关键数据字段**: 任务标题、酬劳、工作时间、地点、任务描述、性别/经验要求。

-   **交友模块 (`/dating`)**:
    -   **功能**: 同城交友服务。包含个人主页、动态广场、偏好设置、用户匹配和查看等功能。
    -   **关键数据字段**: 用户昵称、头像、年龄、身高、学历、职业、交友宣言、相册、兴趣标签。

-   **通用发布模块 (`/publish`)**:
    -   **功能**: 一个聚合的发布入口页面，引导用户选择并跳转到不同类型的具体发布页（如发布职位、发布房源等）。

### 1.3 基础功能

-   **认证 (`/auth`)**: 用户登录、注册、隐私政策和用户协议。
-   **支付**: 各个模块的付费功能（如职位置顶、加急）都依赖于支付服务。

## 2. 后端服务架构设计

基于上述功能需求和用户指定的技术栈，设计后端服务架构如下。

### 2.1 技术栈

-   **语言**: Golang
-   **Web 框架**: Gin
-   **ORM**: Gorm
-   **数据库**: PostgreSQL
-   **缓存**: Redis
-   **消息队列**: RabbitMQ
-   **支付**: 微信支付
-   **定时任务**: Gocron

### 2.2 Golang 项目目录结构 (优化版)

采纳您的建议，我们优化为更现代化的、**按业务领域驱动（Domain-Driven）**的目录结构。这种结构将特定业务的所有相关代码（数据模型、业务逻辑、接口处理）都组织在一起，提高了内聚性，更利于长期维护和团队协作。

```
fnbdb-backend/
├── api/                # OpenAPI/Swagger 文档, 对外暴露的API契约
│   └── v1/
├── cmd/                # 项目启动入口
│   └── server/
│       └── main.go
├── configs/            # 配置文件 (config.yaml)
├── internal/           # 核心业务代码
│   ├── app/            # 应用层，组装业务所需的服务
│   │   ├── handler/    # HTTP Handler (Controller) 层，负责请求路由和参数校验
│   │   ├── middleware/ # Gin 中间件
│   │   ├── router/     # 路由注册
│   │   └── server/     # HTTP 服务启动
│   ├── domain/         # 领域层，核心业务逻辑
│   │   ├── entity/     # 领域实体 (贫血模型，对应数据库表)
│   │   ├── repository/ # 仓储接口定义 (interface)
│   │   └── service/    # 领域服务 (包含核心业务逻辑)
│   ├── infrastructure/ # 基础设施层，实现具体的技术细节
│   │   ├── persistence/ # 数据库仓储实现 (GORM)
│   │   ├── cache/      # 缓存实现 (Redis)
│   │   ├── mq/         # 消息队列实现 (RabbitMQ)
│   │   └── payment/    # 支付SDK封装 (Wechat)
│   └── pkg/            # 项目内部可共享的工具库
│       ├── log/        # 日志
│       ├── e/          # 统一错误码
│       └── utils/      # 通用工具函数
├── go.mod
└── go.sum

```
**结构优势说明:**
*   **高内聚**：`domain` 文件夹中按业务（如 `user`, `job`, `house`）组织代码，每个业务模块都是一个独立的单元。
*   **低耦合**：`infrastructure` 负责实现具体的技术，`domain` 层只定义接口，不依赖具体实现。未来更换数据库或缓存，只需修改 `infrastructure` 层的代码，无需改动核心业务逻辑。
*   **关注点分离**：`app` 层负责处理 HTTP 请求和响应，`domain` 层处理业务规则，`infrastructure` 层处理数据存储和外部服务，职责清晰。


### 2.3 核心服务与组件职责

-   **Gin (`controller`, `router`, `middleware`)**: 负责处理 HTTP 请求，解析参数，调用业务逻辑，并返回响应。中间件用于统一处理认证、日志、异常恢复等。
-   **Gorm (`model`, `repository`)**: `model` 定义了与 PostgreSQL 数据表映射的 Go 结构体。`repository` 层封装了所有数据库的增删改查操作，为 `service` 层提供清晰的数据访问接口。
-   **PostgreSQL**: 作为主数据库，存储所有业务核心数据，如用户信息、职位、房源、帖子、订单等。利用其事务和 ACID 特性保证数据一致性。
-   **Redis**:
    -   **缓存**: 缓存高频读取的数据，如用户 Session/Token、热门职位/房源列表、配置信息等，降低数据库压力。
    -   **分布式锁**: 用于处理并发场景，如秒杀、抢单。
    -   **计数器**: 帖子浏览量、点赞数等。
-   **RabbitMQ**:
    -   **异步解耦**: 用于处理耗时或非核心的流程，如发送短信/邮件通知、用户行为日志记录、数据同步到ES等。当用户发布信息后，可以发送消息到队列，由 worker 异步处理图片审核、信息推送等。
-   **微信支付 (`/internal/pkg/wechat`)**: 封装微信支付的 SDK，处理与职位推广、服务购买等相关的支付流程，包括生成预付单、接收异步回调通知等。
-   **定时任务 (`Go-Cron`)**: 用于执行周期性任务，例如：
    -   每天清理过期的职位/房源信息。
    -   定期生成数据报表。
    -   发送每日/每周精选内容推送。

### 2.4 数据库模型设计 (高阶)

根据前端页面分析，初步设计核心数据表：

-   **`users`** (用户表)
    -   `id`, `open_id`, `union_id`, `nickname`, `avatar`, `phone`, `gender`, `status`, `created_at`, `updated_at`

-   **`jobs`** (职位表)
    -   `id`, `user_id` (发布者), `company_id`, `title`, `job_type`, `category_id`, `salary_min`, `salary_max`, `description`, `education`, `experience`, `welfares` (JSON/Array), `address`, `lat`, `lon`, `contact_phone`, `status` (审核中/已发布/已下架), `is_premium` (是否置顶), `expired_at`, `created_at`, `updated_at`

-   **`houses`** (房源表)
    -   `id`, `user_id` (发布者), `house_type` (租房/二手房), `rent_type` (整租/合租), `community_name`, `building_no`, `area`, `layout` (几室几厅), `orientation`, `rent_price`, `payment_method`, `included_fees` (JSON/Array), `images` (JSON/Array), `facilities` (JSON/Array), `tags` (JSON/Array), `description`, `address`, `lat`, `lon`, `status`, `created_at`, `updated_at`

-   **`gigs`** (零工表)
    -   类似 `jobs` 表，但字段更简化，如 `title`, `reward`, `start_time`, `end_time`, `description`, `location` 等。

-   **`posts`** (社区帖子表)
    -   `id`, `user_id`, `content`, `images` (JSON/Array), `topic_id`, `view_count`, `like_count`, `comment_count`, `created_at`

-   **`orders`** (支付订单表)
    -   `id`, `user_id`, `target_id` (如 job_id), `target_type` (如 'job_premium'), `amount`, `status` (待支付/已支付/失败), `wx_transaction_id`, `created_at`, `paid_at`

-   **`users`** (用户表) - **已更新**
    -   `id` (主键)
    -   `open_id`, `union_id` (微信身份标识)
    -   `nickname` (昵称)
    -   `avatar` (头像)
    -   `phone` (手机号, **新增**)
    -   `email` (邮箱, **新增**)
    -   `gender` (性别)
    -   `name` (真实姓名, **新增**)
    -   `job_title` (职位/职业, **新增**)
    -   `company` (公司, **新增**)
    -   `location` (所在地, **新增**)
    -   `intro` (个人简介, **新增**)
    -   `status` (用户状态：正常/禁用)
    -   `is_real_name_verified` (是否实名认证, **新增**, 布尔类型)
    -   `created_at`, `updated_at`

-   **`jobs`** (职位表)
    -   `id`, `user_id` (发布者), `company_id`, `title`, `job_type`, `category_id`, `salary_min`, `salary_max`, `description`, `education`, `experience`, `welfares` (JSON/Array), `address`, `lat`, `lon`, `contact_phone`, `status` (审核中/已发布/已下架), `is_premium` (是否置顶), `expired_at`, `created_at`, `updated_at`

---
此文档为项目后续开发提供了清晰的蓝图。后端开发可基于此架构进行模块化开发和迭代。 