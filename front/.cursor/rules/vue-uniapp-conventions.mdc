---
globs: *.vue
description: Vue 3 和 UniApp 开发规范和最佳实践
---

# Vue 3 + UniApp 开发规范

## 组件结构规范

### 单文件组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 组合式API，优先使用 script setup
import { ref, reactive, computed, onMounted } from 'vue'

// 类型定义
interface Props {
  // props类型定义
}

// 组件逻辑
</script>

<style lang="scss" scoped>
/* 样式使用scss，添加scoped避免样式污染 */
</style>
```

## UniApp 开发规范

### 页面配置
- 使用 [pages.json](mdc:src/pages.json) 配置页面路由和样式
- 页面组件必须在 `pages` 目录下
- 分包页面使用 `subPackages` 配置

### 生命周期
```ts
import { onLoad, onShow, onReady, onHide, onUnload } from '@dcloudio/uni-app'

// 页面生命周期
onLoad((options) => {
  // 页面加载时触发
})

onShow(() => {
  // 页面显示时触发
})
```

### 状态管理
使用Pinia进行状态管理：
- 全局状态存储在 [src/stores/](mdc:src/stores/) 目录
- 示例：[用户状态](mdc:src/stores/user.ts)

### API调用
```ts
// 统一使用uni.request进行网络请求
const response = await uni.request({
  url: 'https://api.example.com/data',
  method: 'GET',
  data: {}
})
```

## 组件开发规范

### Props定义
```ts
interface Props {
  title: string
  count?: number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  count: 0,
  disabled: false
})
```

### Emits定义
```ts
interface Emits {
  change: [value: string]
  submit: [data: any]
}

const emit = defineEmits<Emits>()
```

### 响应式数据
```ts
// 基本类型使用ref
const count = ref(0)
const message = ref('')

// 对象类型使用reactive
const form = reactive({
  name: '',
  email: ''
})

// 计算属性
const computedValue = computed(() => count.value * 2)
```

## 样式规范

### 使用uni-app内置样式类
- 优先使用uni-app的内置CSS类
- 使用flex布局：`display: flex`
- 单位使用rpx进行适配

### 颜色系统
参考 [样式指南](mdc:src/scripts/css-guide.md) 使用统一的颜色变量

### 响应式设计
```scss
// 使用媒体查询适配不同设备
@media (max-width: 750rpx) {
  .container {
    padding: 20rpx;
  }
}
```

## 性能优化

### 条件渲染
```vue
<!-- 使用v-show对于频繁切换的元素 -->
<view v-show="isVisible">内容</view>

<!-- 使用v-if对于条件很少改变的元素 -->
<view v-if="hasPermission">内容</view>
```

### 列表渲染
```vue
<!-- 为列表项提供唯一key -->
<view v-for="item in list" :key="item.id">
  {{ item.name }}
</view>
```

### 组件懒加载
```ts
// 使用动态导入进行组件懒加载
const LazyComponent = defineAsyncComponent(() => import('./LazyComponent.vue'))
```
