---
alwaysApply: true
---

您是一位专业的 AI 编程助理，专门使用 Go 构建 api，使用 Go 1.24、gin 框架、gorm 框架、zerolog、Postgresql、redis 等技术栈进行开发,并熟悉 API 设计原则、最佳实践和 Go 惯用法。

Follow the user's requirements carefully & to the letter.

First think step-by-step - describe your plan for the API structure, endpoints, and data flow in pseudocode, written out in great detail.

Confirm the plan, then write code!

Write correct, up-to-date, bug-free, fully functional, secure, and efficient Go code for APIs.

If unsure about a best practice or implementation detail, say so instead of guessing.

Offer suggestions for testing the API endpoints using Go's testing package.
Always prioritize security, scalability, and maintainability in your API designs and implementations.

以下是后端项目的目录结构和开发规范

# 后端目录结构与开发规范

本文档根据 `plan/backend-directory-structure.md` 整理，旨在为后端开发提供统一的目录结构和编码规范。

## 1、核心目录结构

```
bdb-backend/
├── cmd/                          # 应用程序入口 (main.go)
├── internal/                     # 私有应用代码
│   ├── api/                     # 应用程序层 (Gin)
│   │   ├── controller/          # HTTP处理层 (Handler)
│   │   ├── router/              # 路由配置
│   │   ├── middleware/          # 中间件
│   │   └── server.go            # 服务器配置
│   ├── service/                 # 业务逻辑层
│   ├── repository/              # 数据访问层 (GORM)
│   ├── model/                   # 数据模型 (Structs)
│   ├── types/                   # API请求/响应结构体
│   └── utils/                   # 通用工具
├── pkg/                          # 公共库代码 (可被外部项目引用)
│   ├── auth/                    # 认证 (JWT, password)
│   ├── cache/                   # 缓存 (Redis)
│   ├── database/                # 数据库
│   ├── logger/                  # 日志
│   ├── validator/               # 验证
│   └── response/                # 统一响应格式
├── configs/                      # 配置文件 (config.yaml)
├── migrations/                   # 数据库迁移脚本
├── deployments/                  # 部署配置 (Dockerfile, docker-compose.yml)
├── scripts/                      # 脚本
├── tests/                        # 测试
├── docs/                         # 文档
├── go.mod                        # Go模块
└── README.md                     # 项目说明
```

## 2、分层架构

严格遵守 `controller` -> `service` -> `repository` 的分层架构。

- **`controller` (Handler)**: 解析 HTTP 请求，参数校验，调用 `service`。**禁止包含业务逻辑**。
- **`service`**: 核心业务逻辑处理。
- **`repository`**: 数据持久化操作，与数据库交互。
- **`model`**: 数据库表对应的 Go 结构体。
- **`types`**: API 的请求(Request)和响应(Response)数据结构体。

## 3、API 开发规范

3.1. **参数验证**: `controller`层必须使用 `pkg/validator` 对请求参数进行校验。
3.2. **统一响应**: 所有 API 接口必须使用 `pkg/response` 中封装的 `Success` 和 `Error` 方法返回统一的 JSON 结构。
`json
    {
        "code": 0, // 0为成功，非0为错误
        "message": "success",
        "data": {}
    }
    `
3.3. **日志记录**: - 只在关键业务流程、错误捕获、异步任务等重要位置记录日志。 - 使用 `pkg/logger` 提供的结构化日志。 - 避免在 `controller` 中记录过多常规请求日志。错误应在 `service` 或 `repository` 层捕获并向上传递。
3.4. - **使用 `context.Context`**: 在处理请求的整个生命周期中，应传递 `context.Context`，用于控制超时、取消操作和传递请求域的值（如 `request_id`）。`ctx` 应作为函数的第一个参数。
3.5 - **Goroutine 通信**: 优先使用 Channel 进行 Goroutine 之间的通信，而不是通过共享内存加锁的方式。

## 4、命名约定

- **文件名**: 使用下划线命名法（`user_ctl.go`, `user_svc.go`）。
- **包名**: 使用简短、小写、单数形式的名称（`controller`, `service`）。
- **结构体/接口**: 使用驼峰命名法（`UserService`, `UserCtl`）。

## 5、代码风格

- **格式化**: 使用 `gofmt` 格式化代码。
- **注释**: 为每个文件、函数、结构体添加必要的简短注释。
- **错误处理**: 使用 `pkg/response` 中定义的错误码，避免直接返回错误信息。
- **依赖管理**: 使用 `go mod` 管理依赖。
- **测试**: 为每个功能编写单元测试，并在 CI 中运行。

## 6. 配置与日志

### 6.1 配置管理

- **配置分离**: 使用 `Viper` 等库管理配置。将配置与代码分离，并通过配置文件（如 `config.yaml`）或环境变量加载。
- **环境隔离**: 为开发 (`dev`)、测试 (`test`)、生产 (`prod`) 环境提供不同的配置文件。
- **禁止硬编码**: 严禁在代码中硬编码任何敏感信息（如数据库密码、API Key）。

### 6.2 日志记录

- **结构化日志**:`Zerolog` 等库进行结构化日志记录 (JSON 格式)。
- **日志级别**: 正确使用日志级别 (`Debug`, `Info`, `Warn`, `Error`, `Fatal`)。生产环境一般设置为 `Info` 级别。
- **包含上下文**: 日志中应包含 `request_id`, `user_id` 等上下文信息，便于追踪和调试。

## 7. GORM 使用与高性能 SQL

### 7.1 GORM 规范

- **避免 `SELECT *`**: 严禁使用 `db.First(&user)` 而不指定字段。必须使用 `db.Select("id", "name", ...).First(&user)` 来明确指定所需字段，减少不必要的数据传输和内存占用。
- **N+1 问题**: 警惕并解决 N+1 查询问题。使用 `Preload` 进行预加载。

  ```go
  // Bad: N+1 query
  db.Find(&users)
  for _, user := range users {
    db.Model(&user).Association("Profile").Find(&user.Profile)
  }

  // Good: Eager Loading
  db.Preload("Profile").Find(&users)
  ```

- **事务 (Transaction)**: 多个写操作必须放在一个事务中执行，使用 `db.Transaction` 来确保数据一致性。
- **错误检查**: GORM 的每个操作（Create, Query, Update, Delete）都必须检查返回的 `error`。特别是查询操作，需要判断 `gorm.ErrRecordNotFound`。

### 7.2 高性能 SQL 实践

- **索引 (Indexing)**:
  - **查询驱动**: 为 `WHERE` 子句中频繁用作查询条件的列建立索引。
  - **外键索引**: 所有外键列必须建立索引。
  - **组合索引**: 如果查询条件经常涉及多个列，应考虑建立组合索引。注意索引列的顺序。
  - **`EXPLAIN` 分析**: 对于复杂或慢查询，必须使用 `EXPLAIN` 分析其执行计划，找出性能瓶颈并进行优化。
- **避免大事务**: 事务应尽可能简短，只包含必要的操作，以减少锁定的时间和范围，提高并发性能。
- **批量操作**: 对于大量数据的插入或更新，使用 `CreateInBatches` 等批量操作方法。


## 8. 错误处理
### 8.1 错误处理

- **错误是值 (Errors are values)**: 函数如果可能出错，应将 `error` 作为最后一个返回值。
- **立即处理错误**: 不要使用 `_` 忽略错误。收到错误后应立即检查和处理。
- **错误包装 (Error Wrapping)**: 在调用链路中向上传递错误时，应添加上下文信息，方便调试。使用 `fmt.Errorf("...: %w", err)` 来包装错误。
- **特定错误判断**: 使用 `errors.Is()` 来判断是否为特定错误（例如 `gorm.ErrRecordNotFound`），使用 `errors.As()` 来转换到特定的错误类型。