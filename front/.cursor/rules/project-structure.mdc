---
alwaysApply: true
description: UniApp项目结构和组织规范
---

# UniApp项目结构指导

## 核心文件结构
- 主入口文件：[main.ts](mdc:src/main.ts)
- 应用配置：[App.vue](mdc:src/App.vue)
- 页面配置：[pages.json](mdc:src/pages.json)
- 类型定义：[env.d.ts](mdc:src/env.d.ts) 和 [shims-uni.d.ts](mdc:shims-uni.d.ts)

## 目录结构规范

### 页面模块 (`src/pages/`)
按功能模块组织页面：
- `auth/` - 认证相关页面
- `dating/` - 交友功能页面
- `gig/` - 零工相关页面
- `house/` - 房屋相关页面
- `job/` - 求职相关页面
- `message/` - 消息功能页面
- `mine/` - 个人中心页面
- `user/` - 用户相关页面

### 组件结构 (`src/components/`)
- `common/` - 通用组件
- `thorui/` - UI组件库
- 按功能模块分组：`dating/`, `gig/`, `house/`, `job/`, `post/`

### 工具和配置
- `src/utils/` - 工具函数
- `src/stores/` - Pinia状态管理
- `src/constants/` - 常量定义
- `src/api/` - API接口
- `src/types/` - TypeScript类型定义

## 开发规范
1. 新增页面应放在对应功能模块目录下
2. 通用组件放在 `components/common/` 中
3. 功能特定组件放在对应模块目录中
4. API接口统一在 `src/api/` 中管理
5. 类型定义统一在 `src/types/` 中管理
