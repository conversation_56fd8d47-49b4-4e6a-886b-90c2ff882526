---
description: API接口、服务层架构和数据管理最佳实践
---

# API和服务层开发规范

## API层架构

### API目录结构
- 接口定义：[src/api/](mdc:src/api/) 目录
- 示例接口：[foo.ts](mdc:src/api/foo.ts)

### 接口定义规范
```ts
// API接口文件结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 请求参数接口
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
}

// 响应数据接口
export interface LoginResponse {
  token: string
  user: User
  permissions: string[]
}

// API函数定义
export const authApi = {
  // 登录
  login: (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return uni.request({
      url: '/api/auth/login',
      method: 'POST',
      data
    }).then(res => res.data)
  },
  
  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<User>> => {
    return uni.request({
      url: '/api/auth/userinfo',
      method: 'GET'
    }).then(res => res.data)
  }
}
```

## 网络请求规范

### uni.request封装
```ts
// 请求拦截器
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
}

class HttpClient {
  private baseURL = 'https://api.example.com'
  private timeout = 10000
  
  private getDefaultHeaders(): Record<string, string> {
    const token = uni.getStorageSync('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  }
  
  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const {
      url,
      method = 'GET',
      data,
      header = {},
      timeout = this.timeout
    } = config
    
    try {
      const response = await uni.request({
        url: this.baseURL + url,
        method,
        data,
        header: {
          ...this.getDefaultHeaders(),
          ...header
        },
        timeout
      })
      
      // 统一响应处理
      const result = response.data as ApiResponse<T>
      
      if (result.code === 200) {
        return result
      } else {
        throw new Error(result.message || '请求失败')
      }
    } catch (error) {
      // 统一错误处理
      this.handleError(error)
      throw error
    }
  }
  
  private handleError(error: any) {
    if (error.statusCode === 401) {
      // 登录过期
      uni.removeStorageSync('token')
      uni.navigateTo({ url: '/pages/auth/login' })
    } else if (error.statusCode === 403) {
      // 无权限
      uni.showToast({
        title: '无访问权限',
        icon: 'none'
      })
    } else {
      // 其他错误
      uni.showToast({
        title: error.message || '网络错误',
        icon: 'none'
      })
    }
  }
}

export const http = new HttpClient()
```

### 请求方法封装
```ts
// GET请求
export const get = <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
  const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
  return http.request<T>({
    url: url + queryString,
    method: 'GET'
  })
}

// POST请求
export const post = <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
  return http.request<T>({
    url,
    method: 'POST',
    data
  })
}

// PUT请求
export const put = <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
  return http.request<T>({
    url,
    method: 'PUT',
    data
  })
}

// DELETE请求
export const del = <T = any>(url: string): Promise<ApiResponse<T>> => {
  return http.request<T>({
    url,
    method: 'DELETE'
  })
}
```

## 状态管理集成

### Pinia Store规范
参考现有Store：
- [用户状态](mdc:src/stores/user.ts)
- [求职状态](mdc:src/stores/job.ts)
- [主Store](mdc:src/stores/index.ts)

```ts
// Store定义示例
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const token = ref<string | null>(null)
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })
  
  // 操作
  const login = async (credentials: LoginRequest) => {
    try {
      const response = await authApi.login(credentials)
      const { token: newToken, user, permissions: userPermissions } = response.data
      
      token.value = newToken
      currentUser.value = user
      permissions.value = userPermissions
      
      // 持久化存储
      uni.setStorageSync('token', newToken)
      uni.setStorageSync('user', user)
      
      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  }
  
  const logout = () => {
    token.value = null
    currentUser.value = null
    permissions.value = []
    
    // 清除存储
    uni.removeStorageSync('token')
    uni.removeStorageSync('user')
  }
  
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      const response = await userApi.updateProfile(profileData)
      currentUser.value = { ...currentUser.value, ...response.data }
      uni.setStorageSync('user', currentUser.value)
      return true
    } catch (error) {
      console.error('Profile update failed:', error)
      return false
    }
  }
  
  return {
    // 状态
    currentUser,
    token,
    permissions,
    // 计算属性
    isLoggedIn,
    hasPermission,
    // 操作
    login,
    logout,
    updateProfile
  }
})
```

## 数据缓存策略

### 本地存储管理
```ts
class StorageManager {
  // 设置数据（带过期时间）
  static set(key: string, value: any, expireTime?: number) {
    const data = {
      value,
      timestamp: Date.now(),
      expireTime: expireTime ? Date.now() + expireTime : null
    }
    uni.setStorageSync(key, JSON.stringify(data))
  }
  
  // 获取数据
  static get<T = any>(key: string): T | null {
    try {
      const dataStr = uni.getStorageSync(key)
      if (!dataStr) return null
      
      const data = JSON.parse(dataStr)
      
      // 检查是否过期
      if (data.expireTime && Date.now() > data.expireTime) {
        uni.removeStorageSync(key)
        return null
      }
      
      return data.value
    } catch (error) {
      console.error('Storage get error:', error)
      return null
    }
  }
  
  // 删除数据
  static remove(key: string) {
    uni.removeStorageSync(key)
  }
  
  // 清空所有数据
  static clear() {
    uni.clearStorageSync()
  }
}
```

### 请求缓存
```ts
class RequestCache {
  private static cache = new Map<string, {
    data: any
    timestamp: number
    expireTime: number
  }>()
  
  static set(key: string, data: any, expireTime = 5 * 60 * 1000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expireTime: Date.now() + expireTime
    })
  }
  
  static get(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() > cached.expireTime) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }
  
  static clear() {
    this.cache.clear()
  }
}
```

## 错误处理

### 统一错误处理
```ts
interface AppError {
  code: string
  message: string
  details?: any
}

class ErrorHandler {
  static handle(error: any) {
    if (error.response) {
      // HTTP错误
      this.handleHttpError(error.response)
    } else if (error.request) {
      // 网络错误
      this.handleNetworkError()
    } else {
      // 其他错误
      this.handleGenericError(error)
    }
  }
  
  private static handleHttpError(response: any) {
    const { status, data } = response
    
    switch (status) {
      case 400:
        uni.showToast({ title: '请求参数错误', icon: 'none' })
        break
      case 401:
        uni.showToast({ title: '登录已过期', icon: 'none' })
        // 跳转到登录页
        break
      case 403:
        uni.showToast({ title: '无访问权限', icon: 'none' })
        break
      case 404:
        uni.showToast({ title: '资源不存在', icon: 'none' })
        break
      case 500:
        uni.showToast({ title: '服务器错误', icon: 'none' })
        break
      default:
        uni.showToast({ title: data?.message || '请求失败', icon: 'none' })
    }
  }
  
  private static handleNetworkError() {
    uni.showToast({
      title: '网络连接失败，请检查网络设置',
      icon: 'none'
    })
  }
  
  private static handleGenericError(error: any) {
    console.error('Application Error:', error)
    uni.showToast({
      title: error.message || '操作失败',
      icon: 'none'
    })
  }
}
```

## 业务逻辑层

### Service层设计
```ts
// 用户服务
export class UserService {
  static async getProfile(userId: string): Promise<User> {
    const cacheKey = `user_profile_${userId}`
    
    // 检查缓存
    const cached = RequestCache.get(cacheKey)
    if (cached) return cached
    
    try {
      const response = await userApi.getProfile(userId)
      const profile = response.data
      
      // 设置缓存
      RequestCache.set(cacheKey, profile)
      
      return profile
    } catch (error) {
      ErrorHandler.handle(error)
      throw error
    }
  }
  
  static async updateAvatar(file: File): Promise<string> {
    try {
      // 先上传文件
      const uploadResponse = await uploadApi.uploadImage(file)
      const avatarUrl = uploadResponse.data.url
      
      // 更新用户头像
      await userApi.updateProfile({ avatar: avatarUrl })
      
      // 更新本地存储
      const userStore = useUserStore()
      await userStore.updateProfile({ avatar: avatarUrl })
      
      return avatarUrl
    } catch (error) {
      ErrorHandler.handle(error)
      throw error
    }
  }
}
```

### 数据转换层
```ts
// 数据转换工具
export class DataTransformer {
  // API数据转换为前端模型
  static apiToUser(apiData: any): User {
    return {
      id: apiData.id,
      name: apiData.name,
      email: apiData.email,
      avatar: apiData.avatar_url,
      createdAt: new Date(apiData.created_at),
      updatedAt: new Date(apiData.updated_at)
    }
  }
  
  // 前端模型转换为API数据
  static userToApi(user: User): any {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      avatar_url: user.avatar,
      created_at: user.createdAt.toISOString(),
      updated_at: user.updatedAt.toISOString()
    }
  }
  
  // 列表数据转换
  static transformList<T, R>(
    list: T[],
    transformer: (item: T) => R
  ): R[] {
    return list.map(transformer)
  }
}
```
