---
description: TypeScript 开发规范和类型定义最佳实践
alwaysApply: false
---
# TypeScript 开发规范

## 类型定义规范

### 接口定义
```ts
// 使用interface定义对象类型
interface User {
  id: string
  name: string
  email: string
  avatar?: string // 可选属性使用?
  readonly createdAt: string // 只读属性
}

// 扩展接口
interface AdminUser extends User {
  permissions: string[]
  role: 'admin' | 'super_admin' // 联合类型
}
```

### 类型别名
```ts
// 使用type定义联合类型、函数类型等
type Status = 'pending' | 'approved' | 'rejected'
type ID = string | number

// 函数类型
type EventHandler = (event: Event) => void

// 泛型类型
type ApiResponse<T> = {
  data: T
  message: string
  code: number
}
```

### 枚举
```ts
// 使用const enum获得更好的性能
const enum StatusCode {
  SUCCESS = 200,
  NOT_FOUND = 404,
  SERVER_ERROR = 500
}

// 字符串枚举
enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}
```

## 代码规范

### 变量声明
```ts
// 优先使用const，需要重新赋值时使用let
const apiUrl = 'https://api.example.com'
let currentUser: User | null = null

// 避免使用var
// ❌ var message = 'hello'
// ✅ const message = 'hello'
```

### 函数定义
```ts
// 箭头函数（简短逻辑）
const formatDate = (date: Date): string => date.toLocaleDateString()

// 普通函数（复杂逻辑）
function processUserData(users: User[]): ProcessedUser[] {
  return users.map(user => ({
    ...user,
    displayName: `${user.name} (${user.email})`
  }))
}

// 异步函数
async function fetchUser(id: string): Promise<User> {
  const response = await uni.request({
    url: `/api/users/${id}`,
    method: 'GET'
  })
  return response.data
}
```

### 类型注解
```ts
// 基本类型注解
const count: number = 0
const message: string = 'hello'
const isActive: boolean = true

// 数组类型
const users: User[] = []
const ids: Array<string> = []

// 对象类型
const config: {
  apiUrl: string
  timeout: number
} = {
  apiUrl: 'https://api.example.com',
  timeout: 5000
}
```

## 工具类型

### 内置工具类型
```ts
// Partial - 所有属性变为可选
type PartialUser = Partial<User>

// Required - 所有属性变为必需
type RequiredUser = Required<User>

// Pick - 选择特定属性
type UserBasicInfo = Pick<User, 'id' | 'name'>

// Omit - 排除特定属性
type CreateUserInput = Omit<User, 'id' | 'createdAt'>

// Record - 创建键值对类型
type UserRoleMap = Record<string, UserRole>
```

### 自定义工具类型
```ts
// 深度只读
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

// 非空类型
type NonNullable<T> = T extends null | undefined ? never : T
```

## 错误处理

### 类型安全的错误处理
```ts
// 结果类型
type Result<T, E = Error> = {
  success: true
  data: T
} | {
  success: false
  error: E
}

// 使用示例
async function safeApiCall<T>(url: string): Promise<Result<T>> {
  try {
    const response = await uni.request({ url })
    return { success: true, data: response.data }
  } catch (error) {
    return { success: false, error: error as Error }
  }
}
```

### 类型守卫
```ts
// 类型守卫函数
function isUser(obj: any): obj is User {
  return obj && typeof obj.id === 'string' && typeof obj.name === 'string'
}

// 使用类型守卫
function processData(data: unknown) {
  if (isUser(data)) {
    // 这里TypeScript知道data是User类型
    console.log(data.name)
  }
}
```

## 模块导入导出

### 导出规范
```ts
// 命名导出（推荐）
export const utils = { ... }
export interface ApiResponse { ... }
export class UserService { ... }

// 默认导出（组件或主要功能）
export default class AuthService { ... }
```

### 导入规范
```ts
// 命名导入
import { ref, reactive } from 'vue'
import { UserService, type User } from '@/services/user'

// 默认导入
import AuthService from '@/services/auth'

// 类型导入（TypeScript 3.8+）
import type { ComponentInstance } from 'vue'
```

## 配置文件

### tsconfig.json 建议配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```
