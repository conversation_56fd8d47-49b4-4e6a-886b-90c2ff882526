---
description: 通用组件和功能组件开发指导
---

# 组件开发指导

## 组件分类和组织

### 通用组件 (`src/components/common/`)
通用组件应该：
- 高度可复用
- 接口简洁明确
- 样式可定制
- 支持多种使用场景

参考现有组件：
- [Card组件](mdc:src/components/common/Card.vue)
- [EmptyResult组件](mdc:src/components/common/EmptyResult.vue)
- [FormInput组件](mdc:src/components/common/FormInput.vue)

### 功能组件
按业务模块组织：
- `dating/` - 交友功能组件
- `gig/` - 零工功能组件  
- `house/` - 房屋功能组件
- `job/` - 求职功能组件

## 组件开发规范

### 组件命名
```ts
// 使用PascalCase命名
// ✅ 好的命名
UserProfile.vue
JobCategorySelector.vue
HouseFilterPanel.vue

// ❌ 避免的命名
userprofile.vue
job-category-selector.vue
filter.vue
```

### Props设计原则
```ts
interface Props {
  // 1. 必需属性在前，可选属性在后
  title: string
  items: Item[]
  
  // 2. 使用描述性的属性名
  isLoading?: boolean
  placeholder?: string
  maxItems?: number
  
  // 3. 提供合理的默认值
  size?: 'small' | 'medium' | 'large'
  variant?: 'primary' | 'secondary'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  variant: 'primary',
  maxItems: 10
})
```

### 事件设计
```ts
// 事件命名使用动词，遵循Vue约定
interface Emits {
  // 输入事件
  'update:modelValue': [value: string]
  'change': [value: string, oldValue: string]
  
  // 用户行为事件
  'click': [event: MouseEvent]
  'submit': [data: FormData]
  'select': [item: Item]
  
  // 状态变化事件
  'loading-start': []
  'loading-end': []
  'error': [error: Error]
}
```

### 插槽设计
```vue
<template>
  <div class="card">
    <!-- 具名插槽 -->
    <header v-if="$slots.header" class="card-header">
      <slot name="header" />
    </header>
    
    <!-- 默认插槽 -->
    <main class="card-content">
      <slot />
    </main>
    
    <!-- 作用域插槽 -->
    <footer v-if="$slots.footer" class="card-footer">
      <slot name="footer" :data="footerData" />
    </footer>
  </div>
</template>
```

## 状态管理集成

### 组件内状态
```ts
// 本地状态使用ref/reactive
const isVisible = ref(false)
const formData = reactive({
  name: '',
  email: ''
})

// 计算属性
const isValid = computed(() => {
  return formData.name && formData.email
})
```

### 全局状态集成
```ts
import { useUserStore } from '@/stores/user'
import { useJobStore } from '@/stores/job'

// 在组件中使用store
const userStore = useUserStore()
const jobStore = useJobStore()

// 响应式访问store数据
const currentUser = computed(() => userStore.currentUser)
```

## API集成模式

### 数据获取
```ts
interface Props {
  userId?: string
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

const { data: user, loading, error, refresh } = useAsyncData(
  'user',
  () => props.userId ? fetchUser(props.userId) : null,
  {
    immediate: props.autoLoad
  }
)
```

### 表单提交
```ts
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)

const handleSubmit = async (formData: FormData) => {
  isSubmitting.value = true
  submitError.value = null
  
  try {
    await submitForm(formData)
    emit('submit-success', formData)
  } catch (error) {
    submitError.value = error.message
    emit('submit-error', error)
  } finally {
    isSubmitting.value = false
  }
}
```

## 样式规范

### CSS类命名
```scss
// 使用BEM方法论
.job-card {
  // 组件根元素
  
  &__header {
    // 组件元素
  }
  
  &__title {
    // 组件元素
    
    &--featured {
      // 修饰符
    }
  }
  
  &--compact {
    // 组件修饰符
  }
}
```

### 响应式样式
```scss
.component {
  // 基础样式
  padding: 20rpx;
  
  // 小屏适配
  @media (max-width: 750rpx) {
    padding: 16rpx;
  }
  
  // 大屏适配
  @media (min-width: 1200rpx) {
    padding: 24rpx;
  }
}
```

### 颜色和主题
```scss
.component {
  // 使用CSS变量
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  
  // 支持主题切换
  &.dark-theme {
    background-color: var(--color-background-dark);
    color: var(--color-text-dark);
  }
}
```

## 测试考虑

### 可测试性设计
```ts
// 暴露测试所需的refs
defineExpose({
  formData,
  isValid,
  submit: handleSubmit,
  reset: handleReset
})
```

### 错误边界
```ts
// 错误处理
const error = ref<Error | null>(null)

const handleError = (err: Error) => {
  error.value = err
  emit('error', err)
  console.error('Component error:', err)
}
```

## 性能优化

### 组件懒加载
```ts
// 大型组件使用懒加载
const HeavyComponent = defineAsyncComponent({
  loader: () => import('./HeavyComponent.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

### 计算属性缓存
```ts
// 昂贵计算使用computed
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})

// 避免在模板中直接调用函数
// ❌ {{ heavyCalculation(data) }}
// ✅ {{ expensiveValue }}
```
