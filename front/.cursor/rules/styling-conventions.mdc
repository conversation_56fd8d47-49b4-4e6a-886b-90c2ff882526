---
globs: *.scss,*.css,*.vue
description: 样式系统、设计规范和主题管理指导
---

# 样式和设计系统规范

## 设计系统概览

项目使用了统一的设计系统，相关文档：
- [样式指南](mdc:src/scripts/css-guide.md)
- [颜色替换指南](mdc:src/scripts/color-replacement-guide.md)
- [样式系统更新](mdc:src/scripts/style-system-updates.md)

## 样式架构

### 全局样式
- 主样式文件：[app.css](mdc:src/styles/app.css)
- UnoCSS配置：[uno.config.ts](mdc:uno.config.ts)

### 单位规范
```scss
// UniApp推荐使用rpx单位，自动适配不同屏幕
.container {
  width: 750rpx;        // 全屏宽度
  padding: 32rpx;       // 内边距
  margin: 16rpx 0;      // 外边距
  font-size: 28rpx;     // 字体大小
}

// 避免使用px，除非需要固定尺寸
.fixed-border {
  border-width: 1px;    // 边框可以使用px
}
```

## 颜色系统

### 主色调
```scss
:root {
  // 主品牌色
  --color-primary: #007AFF;
  --color-primary-light: #4DA3FF;
  --color-primary-dark: #0056CC;
  
  // 辅助色
  --color-secondary: #34C759;
  --color-warning: #FF9500;
  --color-danger: #FF3B30;
  --color-info: #5AC8FA;
  
  // 中性色
  --color-text-primary: #000000;
  --color-text-secondary: #8E8E93;
  --color-text-disabled: #C7C7CC;
  --color-background: #FFFFFF;
  --color-background-secondary: #F2F2F7;
  --color-border: #E5E5EA;
}
```

### 深色主题
```scss
[data-theme="dark"] {
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #8E8E93;
  --color-background: #000000;
  --color-background-secondary: #1C1C1E;
  --color-border: #38383A;
}
```

## 布局系统

### Flex布局
```scss
// 水平居中
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 水平分布
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 垂直布局
.flex-column {
  display: flex;
  flex-direction: column;
}

// 响应式flex
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
```

### Grid布局
```scss
// 等宽网格
.grid-equal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 16rpx;
}

// 固定列数
.grid-3-col {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
```

## 间距系统

### 标准间距
```scss
// 使用8的倍数作为间距基准
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;
$spacing-xxl: 64rpx;

// 应用示例
.card {
  padding: $spacing-md;
  margin: $spacing-sm 0;
}

.section {
  margin-bottom: $spacing-lg;
}
```

### 内边距类
```scss
// 通用内边距类
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }

// 方向性内边距
.pt-md { padding-top: $spacing-md; }
.pr-md { padding-right: $spacing-md; }
.pb-md { padding-bottom: $spacing-md; }
.pl-md { padding-left: $spacing-md; }

// 水平/垂直内边距
.px-md { padding-left: $spacing-md; padding-right: $spacing-md; }
.py-md { padding-top: $spacing-md; padding-bottom: $spacing-md; }
```

## 字体系统

### 字体大小
```scss
$font-size-xs: 20rpx;    // 12px
$font-size-sm: 24rpx;    // 14px
$font-size-base: 28rpx;  // 16px (基准)
$font-size-lg: 32rpx;    // 18px
$font-size-xl: 36rpx;    // 20px
$font-size-xxl: 48rpx;   // 24px
$font-size-display: 72rpx; // 36px

// 应用示例
.title {
  font-size: $font-size-xxl;
  font-weight: 600;
  line-height: 1.3;
}

.body-text {
  font-size: $font-size-base;
  line-height: 1.5;
}

.caption {
  font-size: $font-size-sm;
  color: var(--color-text-secondary);
}
```

### 字体权重
```scss
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
```

## 组件样式规范

### 按钮样式
```scss
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: $font-size-base;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  // 主按钮
  &--primary {
    background-color: var(--color-primary);
    color: #FFFFFF;
    
    &:hover {
      background-color: var(--color-primary-dark);
    }
  }
  
  // 次要按钮
  &--secondary {
    background-color: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
  }
  
  // 危险按钮
  &--danger {
    background-color: var(--color-danger);
    color: #FFFFFF;
  }
  
  // 尺寸变体
  &--small {
    padding: 12rpx 24rpx;
    font-size: $font-size-sm;
  }
  
  &--large {
    padding: 20rpx 40rpx;
    font-size: $font-size-lg;
  }
}
```

### 卡片样式
```scss
.card {
  background-color: var(--color-background);
  border-radius: 12rpx;
  border: 1px solid var(--color-border);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  &__header {
    padding: $spacing-md $spacing-md $spacing-sm;
    border-bottom: 1px solid var(--color-border);
  }
  
  &__content {
    padding: $spacing-md;
  }
  
  &__footer {
    padding: $spacing-sm $spacing-md $spacing-md;
    border-top: 1px solid var(--color-border);
  }
}
```

## 响应式设计

### 断点系统
```scss
$breakpoint-sm: 480rpx;
$breakpoint-md: 750rpx;
$breakpoint-lg: 1024rpx;
$breakpoint-xl: 1200rpx;

// Mixins
@mixin mobile {
  @media (max-width: #{$breakpoint-sm}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1rpx}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

// 使用示例
.responsive-component {
  padding: $spacing-md;
  
  @include mobile {
    padding: $spacing-sm;
  }
  
  @include desktop {
    padding: $spacing-lg;
  }
}
```

## 动画系统

### 过渡动画
```scss
// 标准过渡时间
$transition-fast: 0.15s;
$transition-base: 0.2s;
$transition-slow: 0.3s;

// 缓动函数
$ease-out: cubic-bezier(0.25, 0.8, 0.25, 1);
$ease-in: cubic-bezier(0.55, 0.06, 0.68, 0.19);
$ease-in-out: cubic-bezier(0.45, 0, 0.55, 1);

// 通用过渡类
.transition {
  transition: all $transition-base $ease-out;
}

.transition-fast {
  transition: all $transition-fast $ease-out;
}

.transition-slow {
  transition: all $transition-slow $ease-out;
}
```

### 关键帧动画
```scss
// 淡入动画
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fade-in $transition-base $ease-out;
}

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 2s infinite;
}
```

## 样式优化

### 性能考虑
```scss
// 使用transform和opacity进行动画（GPU加速）
.optimized-animation {
  will-change: transform, opacity;
  transform: translateZ(0); // 触发硬件加速
}

// 避免昂贵的CSS属性
.avoid {
  // ❌ 避免使用box-shadow动画
  // transition: box-shadow 0.2s;
  
  // ✅ 使用transform替代
  transition: transform 0.2s;
}
```

### 代码组织
```scss
// 按功能组织样式
/* ==========================================================================
   Base styles
   ========================================================================== */

/* ==========================================================================
   Layout
   ========================================================================== */

/* ==========================================================================
   Components
   ========================================================================== */

/* ==========================================================================
   Utilities
   ========================================================================== */
```
