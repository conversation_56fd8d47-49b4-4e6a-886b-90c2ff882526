/**
 * 响应状态码常量定义
 * 与后端保持一致
 */

// 业务状态码
export const RESPONSE_CODE = {
    // 成功
    SUCCESS: 0,
    // 错误
    ERROR: 1,
} as const

// HTTP 状态码
export const HTTP_STATUS = {
    OK: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
} as const

// 响应提示语
export const RESPONSE_MESSAGE = {
    SUCCESS: '操作成功',
    ERROR: '操作失败',

    // 网络相关
    NETWORK_ERROR: '网络错误，请检查网络连接',
    REQUEST_TIMEOUT: '请求超时，请稍后重试',

    // 认证相关
    UNAUTHORIZED: '登录已过期，请重新登录',
    TOKEN_INVALID: 'Token无效，请重新登录',
    FORBIDDEN: '您没有权限访问该资源',

    // 请求相关
    BAD_REQUEST: '请求参数错误',
    NOT_FOUND: '接口不存在',
    METHOD_NOT_ALLOWED: '请求方法不允许',
    TOO_MANY_REQUESTS: '请求过于频繁，请稍后重试',

    // 服务器相关
    SERVER_ERROR: '服务器错误，请稍后重试',
    SERVICE_UNAVAILABLE: '服务暂时不可用，请稍后重试',
} as const

// 响应类型定义
export interface ApiResponse<T = any> {
    code: number
    message: string
    data: T
    timestamp?: number
}

// 分页数据结构
export interface PaginatedData<T = any> {
    list: T[]
    total: number
    page: number
    pageSize: number
    pages: number
}

// 错误信息类型
export interface ApiError {
    name: string
    code: number
    message: string
    data?: any
    statusCode?: number
}

// 类型导出
export type ResponseCode = typeof RESPONSE_CODE[keyof typeof RESPONSE_CODE]
export type HttpStatus = typeof HTTP_STATUS[keyof typeof HTTP_STATUS] 