/**
 * 通用常量定义
 */

// 表单验证规则
export const FORM_RULES = {
    REQUIRED: 'required',
    NUMBER: 'number',
    EMAIL: 'email',
    PHONE: 'phone',
    ID_CARD: 'idCard'
};

// 图片上传相关常量
export const IMAGE_UPLOAD = {
    MAX_COUNT: 9,
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
    QUALITY: 80
};

// 房源状态
export const HOUSE_STATUS = [
    { label: "待审核", value: "pending" },
    { label: "已上架", value: "active" },
    { label: "已下架", value: "inactive" },
    { label: "已租出", value: "rented" },
    { label: "已售出", value: "sold" }
];

// 联系方式类型
export const CONTACT_TYPE = [
    { label: "手机", value: "mobile" },
    { label: "座机", value: "landline" },
    { label: "微信", value: "wechat" },
    { label: "QQ", value: "qq" }
];

// 性别选项
export const GENDER_OPTIONS = [
    { label: "男", value: "male" },
    { label: "女", value: "female" },
    { label: "不限", value: "unlimited" }
];

// 年龄区间选项
export const AGE_RANGE_OPTIONS = [
    { label: "18-25岁", value: "18-25" },
    { label: "26-30岁", value: "26-30" },
    { label: "31-35岁", value: "31-35" },
    { label: "36-40岁", value: "36-40" },
    { label: "41-45岁", value: "41-45" },
    { label: "46-50岁", value: "46-50" },
    { label: "50岁以上", value: "50+" }
];

// 学历选项
export const EDUCATION_OPTIONS = [
    { label: "小学", value: "primary" },
    { label: "初中", value: "junior" },
    { label: "高中", value: "senior" },
    { label: "中专", value: "technical" },
    { label: "大专", value: "college" },
    { label: "本科", value: "bachelor" },
    { label: "硕士", value: "master" },
    { label: "博士", value: "phd" }
];

// 零工经验选项
export const GIG_EXPERIENCE_OPTIONS = [
    { id: 0, name: '不限' },
    { id: 1, name: '在校生' },
    { id: 2, name: '应届生' },
    { id: 3, name: '1年以内' },
    { id: 4, name: '1-3年' },
    { id: 5, name: '3年以上' }
];

// 工作经验选项
export const WORK_EXPERIENCE_OPTIONS = [
    { label: "无经验", value: "none" },
    { label: "1年以下", value: "less_1" },
    { label: "1-3年", value: "1-3" },
    { label: "3-5年", value: "3-5" },
    { label: "5-10年", value: "5-10" },
    { label: "10年以上", value: "10+" }
];

// 薪资范围选项
export const SALARY_RANGE_OPTIONS = [
    { label: "2K以下", value: "below_2k" },
    { label: "2K-3K", value: "2k-3k" },
    { label: "3K-5K", value: "3k-5k" },
    { label: "5K-8K", value: "5k-8k" },
    { label: "8K-12K", value: "8k-12k" },
    { label: "12K-20K", value: "12k-20k" },
    { label: "20K-30K", value: "20k-30k" },
    { label: "30K以上", value: "above_30k" },
    { label: "面议", value: "negotiable" }
];

// 工作类型选项
export const WORK_TYPE_OPTIONS = [
    { label: "全职", value: "full_time" },
    { label: "兼职", value: "part_time" },
    { label: "实习", value: "internship" },
    { label: "临时", value: "temporary" }
];

// 发布来源
export const PUBLISH_SOURCE = [
    { label: "个人", value: "personal" },
    { label: "中介", value: "agent" },
    { label: "开发商", value: "developer" }
];

// 时间格式
export const TIME_FORMAT = {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIME: 'HH:mm'
};

// 分页配置
export const PAGINATION = {
    PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100
};

// API响应状态码
export const API_CODE = {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
};

// 健康证要求选项
export const HEALTH_CERTIFICATE_OPTIONS = [
    { label: "不需要", value: "none" },
    { label: "需要有效健康证", value: "required" },
    { label: "可后续办理", value: "can_apply_later" }
];

// 零工类别选项
export const GIG_CATEGORY_OPTIONS = [
    { label: "餐饮服务", value: "food_service" },
    { label: "保洁清洁", value: "cleaning" },
    { label: "搬运装卸", value: "moving" },
    { label: "配送跑腿", value: "delivery" },
    { label: "家政服务", value: "housekeeping" },
    { label: "维修安装", value: "repair" },
    { label: "促销推广", value: "promotion" },
    { label: "其他", value: "others" }
];

// 结算方式选项
export const SETTLEMENT_OPTIONS = [
    { label: "日结", value: "daily" },
    { label: "周结", value: "weekly" },
    { label: "月结", value: "monthly" },
    { label: "完工结算", value: "completion" }
];

// 缓存键名
export const CACHE_KEYS = {
    USER_INFO: 'USER_INFO',
    CITY_INFO: 'CITY_INFO',
    SEARCH_HISTORY: 'SEARCH_HISTORY'
}; 