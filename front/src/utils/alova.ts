import { createAlova } from 'alova'
import VueHook from 'alova/vue'
import UniAppAdapter from '@alova/adapter-uniapp'
import { useUserStore } from '@/stores'

// 获取基础URL
const baseURL = import.meta.env.VITE_SERVER_BASEURL || 'http://localhost:8080/api'

// 创建 Alova 实例
export const alovaInstance = createAlova({
    baseURL,
    timeout: 10000,

    // Vue Hook
    statesHook: VueHook,

    // 使用 uni-app 适配器
    ...UniAppAdapter(),

    // 请求拦截器
    beforeRequest: (method) => {
        console.log('请求配置:', method)

        // 添加默认headers
        method.config.headers = {
            'Content-Type': 'application/json',
            ...method.config.headers,
        }

        // 添加token
        try {
            const userStore = useUserStore()
            const token = userStore.accessToken
            if (token) {
                method.config.headers.Authorization = `Bearer ${token}`
            }
        } catch (error) {
            console.warn('获取token失败:', error)
        }
    },

    // 响应拦截器
    responded: {
        onSuccess: (response, method) => {
            console.log('响应成功:', response)

            // 对于不同类型的响应，需要分别处理
            if ('data' in response) {
                // 普通请求响应
                const data = response.data as ResponseData<unknown>

                // 根据业务状态码判断是否请求成功
                if (data.code === 200 || data.code === 0) {
                    return data.data // 直接返回业务数据
                }

                // 处理401未授权
                if (data.code === 401) {
                    const userStore = useUserStore()
                    userStore.clearUserInfo()
                    uni.navigateTo({ url: '/pages/auth/login' })
                    throw new Error(data.msg || '登录已过期')
                }

                // 其他业务错误
                const errorMessage = data.msg || '请求失败'
                uni.showToast({
                    icon: 'none',
                    title: errorMessage,
                })
                throw new Error(errorMessage)
            } else {
                // 下载等其他类型响应，直接返回
                return response
            }
        },

        onError: (error, method) => {
            console.error('请求错误:', error)

            // 网络错误等情况
            let errorMessage = '网络错误，请检查网络连接'

            if (error.statusCode) {
                switch (error.statusCode) {
                    case 400:
                        errorMessage = '请求参数错误'
                        break
                    case 403:
                        errorMessage = '无访问权限'
                        break
                    case 404:
                        errorMessage = '资源不存在'
                        break
                    case 500:
                        errorMessage = '服务器错误'
                        break
                    default:
                        errorMessage = `请求失败 (${error.statusCode})`
                }
            }

            uni.showToast({
                icon: 'none',
                title: errorMessage,
            })

            throw new Error(errorMessage)
        }
    },

    // 缓存配置
    cacheFor: {
        GET: 5 * 60 * 1000, // GET请求缓存5分钟
        POST: 0, // POST请求不缓存
        PUT: 0,
        DELETE: 0,
    },

    // 请求共享配置
    shareRequest: true,
})

// 导出便捷方法
export default alovaInstance 