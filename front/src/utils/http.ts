import { un, type UnData, type UnResponse } from '@uni-helper/uni-network'
import { useUserStore } from '@/stores'

// 获取基础URL
const baseUrl = import.meta.env.VITE_SERVER_BASEURL

// 创建自定义实例
const instance = un.create({
  baseUrl,
  timeout: 10000,
})

// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 添加平台标识
    config.headers = {
      ...config.headers,
    }

    // 添加token
    try {
      const userStore = useUserStore()
      const token = userStore.accessToken
      console.log(token)
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      console.warn('获取token失败:', error)
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 添加响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 直接返回 data 部分，简化调用
    const data = response.data as ResponseData<unknown>

    // 根据业务状态码判断是否请求成功
    if (data.code === 200 || data.code === 0) {
      return data
    }

    // 处理401未授权
    if (data.code === 401) {
      const userStore = useUserStore()
      userStore.clearUserInfo()
      uni.navigateTo({ url: '/pages/login/login' })
      return Promise.reject(data)
    }

    // 其他错误情况
    uni.showToast({
      icon: 'none',
      title: data.msg || '请求错误',
    })
    return Promise.reject(data)
  },
  (error) => {
    // 网络错误等情况
    // uni.showToast({
    //   icon: 'none',
    //   title: '网络错误，请检查网络连接',
    // })
    console.log(error)
    return Promise.reject(error)
  },
)

/**
 * 网络请求封装
 * @param T 返回的数据类型
 * @param D 请求的数据类型
 * @param R 响应的最终类型，默认为 T
 */
export const http = {
  // GET请求
  get: <T = any, D = any, R = T>(url: string, params?: Record<string, any>, config = {}) => {
    return instance<T, D, R>({
      url,
      method: 'GET',
      params,
      ...config,
    })
  },

  // POST请求
  post: <T = any, D = any, R = T>(url: string, data?: D, config = {}) => {
    return instance<T, D, R>({
      url,
      method: 'POST',
      data,
      ...config,
    })
  },

  // PUT请求
  put: <T = any, D = any, R = T>(url: string, data?: D, config = {}) => {
    return instance<T, D, R>({
      url,
      method: 'PUT',
      data,
      ...config,
    })
  },

  // DELETE请求
  delete: <T = any, D = any, R = T>(url: string, params?: Record<string, any>, config = {}) => {
    return instance<T, D, R>({
      url,
      method: 'DELETE',
      params,
      ...config,
    })
  },

  // 上传文件
  upload: <T = any, D = any, R = T>(url: string, data: D, config = {}) => {
    return instance<T, D, R>({
      url,
      method: 'POST',
      data,
      adapter: 'upload',
      ...config,
    })
  },

  // 下载文件
  download: <T = any, D = any, R = T>(url: string, config = {}) => {
    return instance<T, D, R>({
      url,
      adapter: 'download',
      ...config,
    })
  },
}

export default http
