/**
 * WeChat Integration Utilities
 * 微信集成工具类
 */

import { useUserStore } from '@/stores/user'

// WeChat API 响应接口
export interface WeChatLoginResponse {
  code: string
  errMsg: string
}

export interface WeChatUserInfo {
  nickName: string
  avatarUrl: string
  gender: number
  city: string
  province: string
  country: string
  language: string
}

// WeChat 登录状态
export enum WeChatLoginStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

class WeChatService {
  private userStore = useUserStore()
  private loginStatus: WeChatLoginStatus = WeChatLoginStatus.NOT_STARTED

  /**
   * 检查微信环境
   */
  isWeChatEnvironment(): boolean {
    // #ifdef MP-WEIXIN
    return true
    // #endif
    
    // #ifndef MP-WEIXIN
    return false
    // #endif
  }

  /**
   * 微信登录
   */
  async login(): Promise<{ success: boolean; data?: any; error?: string }> {
    if (!this.isWeChatEnvironment()) {
      return {
        success: false,
        error: '当前环境不支持微信登录'
      }
    }

    this.loginStatus = WeChatLoginStatus.IN_PROGRESS

    try {
      // 获取微信登录凭证
      const loginResult = await this.getWeChatCode()
      if (!loginResult.success) {
        this.loginStatus = WeChatLoginStatus.FAILED
        return loginResult
      }

      // 获取用户信息
      const userInfoResult = await this.getWeChatUserInfo()
      if (!userInfoResult.success) {
        this.loginStatus = WeChatLoginStatus.FAILED
        return userInfoResult
      }

      // 模拟服务器登录验证
      const authResult = await this.authenticateWithServer(
        loginResult.data.code,
        userInfoResult.data
      )

      if (authResult.success) {
        this.loginStatus = WeChatLoginStatus.SUCCESS
        // 保存用户信息到store
        this.userStore.setUserInfo(authResult.data.user, authResult.data.token)
      } else {
        this.loginStatus = WeChatLoginStatus.FAILED
      }

      return authResult
    } catch (error) {
      this.loginStatus = WeChatLoginStatus.FAILED
      return {
        success: false,
        error: error instanceof Error ? error.message : '登录失败'
      }
    }
  }

  /**
   * 获取微信登录凭证
   */
  private async getWeChatCode(): Promise<{ success: boolean; data?: any; error?: string }> {
    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      uni.login({
        provider: 'weixin',
        success: (res: WeChatLoginResponse) => {
          if (res.code) {
            resolve({
              success: true,
              data: { code: res.code }
            })
          } else {
            resolve({
              success: false,
              error: '获取微信登录凭证失败'
            })
          }
        },
        fail: (error) => {
          resolve({
            success: false,
            error: error.errMsg || '微信登录失败'
          })
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信环境模拟
      setTimeout(() => {
        resolve({
          success: true,
          data: { code: 'mock_wechat_code_' + Date.now() }
        })
      }, 1000)
      // #endif
    })
  }

  /**
   * 获取微信用户信息
   */
  private async getWeChatUserInfo(): Promise<{ success: boolean; data?: any; error?: string }> {
    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve({
            success: true,
            data: res.userInfo
          })
        },
        fail: (error) => {
          resolve({
            success: false,
            error: error.errMsg || '获取用户信息失败'
          })
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信环境模拟
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            nickName: '微信用户',
            avatarUrl: '/static/images/wechat-avatar.png',
            gender: 1,
            city: '北京',
            province: '北京',
            country: '中国',
            language: 'zh_CN'
          }
        })
      }, 500)
      // #endif
    })
  }

  /**
   * 服务器认证（模拟）
   */
  private async authenticateWithServer(
    code: string, 
    userInfo: WeChatUserInfo
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // 模拟服务器请求
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟成功响应
        const mockUser = {
          id: 'wechat_user_' + Date.now(),
          name: userInfo.nickName || '微信用户',
          avatar: userInfo.avatarUrl || '/static/images/wechat-avatar.png',
          intro: '通过微信登录的用户',
          isVip: false,
          followers: 0,
          following: 0,
          posts: 0,
          phone: '',
          wechat: userInfo.nickName || '',
          email: '',
          joinDate: new Date().toISOString().split('T')[0],
          location: `${userInfo.province || ''} ${userInfo.city || ''}`.trim(),
          isVerified: true,
          verificationLevel: 'phone' as const
        }

        resolve({
          success: true,
          data: {
            user: mockUser,
            token: 'wechat_token_' + Date.now()
          }
        })
      }, 1500)
    })
  }

  /**
   * 获取当前登录状态
   */
  getLoginStatus(): WeChatLoginStatus {
    return this.loginStatus
  }

  /**
   * 重置登录状态
   */
  resetLoginStatus(): void {
    this.loginStatus = WeChatLoginStatus.NOT_STARTED
  }

  /**
   * 微信分享
   */
  async share(options: {
    title: string
    desc?: string
    path?: string
    imageUrl?: string
  }): Promise<{ success: boolean; error?: string }> {
    if (!this.isWeChatEnvironment()) {
      return {
        success: false,
        error: '当前环境不支持微信分享'
      }
    }

    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      uni.shareAppMessage({
        title: options.title,
        desc: options.desc,
        path: options.path || '/pages/home/<USER>',
        imageUrl: options.imageUrl,
        success: () => {
          resolve({ success: true })
        },
        fail: (error) => {
          resolve({
            success: false,
            error: error.errMsg || '分享失败'
          })
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      uni.showToast({
        title: '分享功能仅在微信小程序中可用',
        icon: 'none'
      })
      resolve({
        success: false,
        error: '当前环境不支持微信分享'
      })
      // #endif
    })
  }

  /**
   * 微信支付（预留接口）
   */
  async pay(options: {
    orderInfo: any
  }): Promise<{ success: boolean; error?: string }> {
    // 预留微信支付接口
    return {
      success: false,
      error: '微信支付功能待开发'
    }
  }
}

// 导出单例
export const weChatService = new WeChatService()

// 导出便捷方法
export const useWeChat = () => {
  return {
    login: () => weChatService.login(),
    share: (options: Parameters<typeof weChatService.share>[0]) => weChatService.share(options),
    isWeChatEnv: () => weChatService.isWeChatEnvironment(),
    getLoginStatus: () => weChatService.getLoginStatus(),
    resetLoginStatus: () => weChatService.resetLoginStatus()
  }
}
