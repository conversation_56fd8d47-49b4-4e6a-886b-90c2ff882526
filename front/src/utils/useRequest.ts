/**
 * Alova 使用示例和工具函数
 * 
 * 基于 alova 的请求 Hook 使用指南
 */

import { useRequest, useWatcher } from 'alova'
import { api } from '@/api'

// 重新导出 alova 的 hooks，方便使用
export { useRequest, useWatcher }

/**
 * 使用示例：
 * 
 * 1. 基本用法
 * ```ts
 * import { useRequest } from '@/utils/useRequest'
 * import { api } from '@/api'
 * 
 * // 在组件中使用
 * const { data, loading, error, send } = useRequest(() => api.get('/users'))
 * ```
 * 
 * 2. 带参数的请求
 * ```ts
 * const userId = ref(1)
 * const { data, loading } = useRequest(() => api.get(`/users/${userId.value}`))
 * ```
 * 
 * 3. 监听式请求
 * ```ts
 * import { useWatcher } from '@/utils/useRequest'
 * 
 * const searchKey = ref('')
 * const { data, loading } = useWatcher(
 *   () => api.get('/search', { q: searchKey.value }),
 *   [searchKey], // 监听 searchKey 变化
 *   { immediate: false } // 不立即执行
 * )
 * ```
 * 
 * 4. 手动触发请求
 * ```ts
 * const { loading, data, send } = useRequest(
 *   (id: number) => api.get(`/users/${id}`),
 *   { immediate: false }
 * )
 * 
 * // 手动触发
 * const handleClick = () => send(123)
 * ```
 * 
 * 5. 文件上传
 * ```ts
 * const uploadFile = (file: File) => {
 *   const formData = new FormData()
 *   formData.append('file', file)
 *   return api.upload('/upload', formData)
 * }
 * 
 * const { loading, send: upload } = useRequest(uploadFile, { immediate: false })
 * ```
 */

// 导出 api 以便在组件中直接使用
export { api }
export default useRequest 