import { request } from '@/utils/alova'

/**
 * 认证相关 API
 * 使用统一的 request 实例，遵循 GET/POST 规范
 */

/**
 * 微信小程序登录
 * @param data 登录数据
 * @returns 登录结果
 */
export const wechatLogin = (data: { loginCode: string; phoneCode: string }) => {
  return request.Post("/auth/wechat-login", data)
}

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = () => {
  return request.Get("/auth/userinfo")
}

/**
 * 刷新Token
 * @param refreshToken 刷新令牌
 * @returns 新的Token
 */
export const refreshToken = (refreshToken: string) => {
  return request.Post("/auth/refresh", { refreshToken })
}

/**
 * 退出登录
 * @returns 退出结果
 */
export const logout = () => {
  return request.Post("/auth/logout", {})
} 