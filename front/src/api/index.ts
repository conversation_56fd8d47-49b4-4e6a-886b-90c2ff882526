import { alovaInstance } from '@/utils/alova'

/**
 * API 服务封装
 * 基于 alova 提供便捷的请求方法
 */
export const api = {
    // GET 请求
    get: <T = any>(url: string, params?: Record<string, any>, config?: any) => {
        return alovaInstance.Get<T>(url, {
            params,
            ...config,
        })
    },

    // POST 请求
    post: <T = any>(url: string, data?: any, config?: any) => {
        return alovaInstance.Post<T>(url, data, {
            ...config,
        })
    },

    // PUT 请求
    put: <T = any>(url: string, data?: any, config?: any) => {
        return alovaInstance.Put<T>(url, data, {
            ...config,
        })
    },

    // DELETE 请求
    delete: <T = any>(url: string, params?: Record<string, any>, config?: any) => {
        return alovaInstance.Delete<T>(url, {
            params,
            ...config,
        })
    },

    // 文件上传
    upload: <T = any>(url: string, fileData: any, config?: any) => {
        return alovaInstance.Post<T>(url, fileData, {
            requestType: 'upload',
            ...config,
        })
    },

    // 文件下载
    download: <T = any>(url: string, config?: any) => {
        return alovaInstance.Get<T>(url, {
            requestType: 'download',
            ...config,
        })
    },
}

export default api 