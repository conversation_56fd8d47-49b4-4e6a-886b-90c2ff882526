import { request } from '@/utils/alova'

/**
 * 通用服务 API
 * 使用统一的 request 实例，遵循 GET/POST 规范
 */

/**
 * 获取上传凭证
 * @returns 上传凭证
 */
export const getUploadToken = () => {
  return request.Post('/common/getUploadToken', {})
}

/**
 * 文件上传
 * @param file 文件数据
 * @returns 上传结果
 */
export const uploadFile = (file: any) => {
  return request.Post('/common/upload', file)
}

/**
 * 获取配置信息
 * @returns 配置信息
 */
export const getConfig = () => {
  return request.Get('/common/config')
}

// 修改获取token接口的响应类型
export interface QiniuTokenResponse {
  token: string
  expire: number
  key: string
  uploadDomain: string // 新增上传域名字段
}
