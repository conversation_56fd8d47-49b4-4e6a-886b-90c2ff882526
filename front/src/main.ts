import 'virtual:uno.css'
import uvUI from '@climblee/uv-ui'
import { createSSRApp } from "vue";
import App from "./App.vue";
import store from "./stores";
import NetworkImage from "./components/NetworkImage.vue";
import CustomNavBar from "./components/CustomNavBar.vue";
import Card from "./components/common/Card.vue";
import Tag from "./components/common/Tag.vue";
import zPaging from 'z-paging/components/z-paging/z-paging.vue';

export function createApp() {
  const app = createSSRApp(App);
  app.use(store);
  app.use(uvUI);
  // 全局注册组件
  app.component('NetworkImage', NetworkImage);
  app.component('CustomNavBar', CustomNavBar);
  app.component('Card', Card);
  app.component('Tag', Tag);
  app.component('z-paging', zPaging);
  return {
    app,
  };
}
