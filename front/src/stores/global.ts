import { defineStore } from 'pinia';

export const useGlobalStore = defineStore('global', {
    state: () => ({
        isPrivacyPopupVisible: false,
    }),
    actions: {
        showPrivacyPopup() {
            uni.hideTabBar()
            this.isPrivacyPopupVisible = true;
        },
        hidePrivacyPopup() {
            uni.showTabBar()
            this.isPrivacyPopupVisible = false;
        },
    },
}); 