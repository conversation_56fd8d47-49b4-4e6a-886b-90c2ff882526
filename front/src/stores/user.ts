import { defineStore } from 'pinia'

// 用户信息接口定义
export interface UserInfo {
  id: string
  name: string
  avatar?: string
  intro?: string
  isVip?: boolean
  followers?: number
  following?: number
  posts?: number
  phone?: string
  wechat?: string
  email?: string
  joinDate?: string
  // 职业相关信息
  jobTitle?: string
  company?: string
  location?: string
  // 认证状态
  isVerified?: boolean
  verificationLevel?: 'none' | 'phone' | 'identity' | 'enterprise'
  // 钱包相关
  balance?: number
  points?: number
  coupons?: number
  gifts?: number
}

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as UserInfo | null,
    accessToken: null as string | null,
    loginTime: null as number | null,
  }),

  actions: {
    // 设置用户信息
    setUserInfo(user: UserInfo, accessToken: string) {
      this.user = user
      this.accessToken = accessToken
      this.loginTime = Date.now()
    },

    // 清除用户信息
    clearUserInfo() {
      this.user = null
      this.accessToken = null
      this.loginTime = null
    },

    // 更新用户信息
    updateUserInfo(userInfo: Partial<UserInfo>) {
      if (this.user) {
        this.user = { ...this.user, ...userInfo }
      }
    },

    // 检查登录状态
    checkLoginStatus() {
      if (!this.user || !this.accessToken) {
        return false
      }

      // 检查token是否过期 (7天)
      const tokenExpireTime = 7 * 24 * 60 * 60 * 1000 // 7天
      if (this.loginTime && Date.now() - this.loginTime > tokenExpireTime) {
        this.clearUserInfo()
        return false
      }

      return true
    },

    // Mock登录方法 (用于测试)
    mockLogin() {
      const mockUser: UserInfo = {
        id: "mock_user_001",
        name: "张三",
        avatar: "/static/images/mock-avatar.png",
        intro: "资深前端开发工程师，专注于移动端开发",
        isVip: true,
        followers: 128,
        following: 89,
        posts: 45,
        phone: "138****8888",
        wechat: "zhangsan_dev",
        email: "<EMAIL>",
        joinDate: "2023-01-15",
        jobTitle: "高级前端工程师",
        company: "科技有限公司",
        location: "北京市",
        isVerified: true,
        verificationLevel: "identity",
        balance: 1234.56,
        points: 2580,
        coupons: 5,
        gifts: 2
      }

      this.setUserInfo(mockUser, "mock_access_token_123456")
      return mockUser
    }
  },

  getters: {
    // 获取用户信息
    getUser: (state) => state.user,

    // 是否已登录
    isLoggedIn: (state) => !!state.user && !!state.accessToken,

    // 用户显示名称
    displayName: (state) => state.user?.name || "用户",

    // 用户头像
    userAvatar: (state) => state.user?.avatar || "/static/images/default-avatar.png",

    // 是否VIP用户
    isVipUser: (state) => !!state.user?.isVip,

    // 认证状态
    verificationStatus: (state) => state.user?.verificationLevel || 'none',

    // 是否已认证
    isVerified: (state) => !!state.user?.isVerified,
  },

  // 启用数据持久化
  persist: true
})