<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useGlobalStore } from "@/stores/global";
import { useUserStore } from "@/stores/user";

onLaunch(() => {
  console.log("App Launch");

  // 确保在 onLaunch 中可以访问到 store 实例
  const globalStore = useGlobalStore();
  const userStore = useUserStore();

  // 检查用户是否已同意隐私协议
  const hasAgreed = uni.getStorageSync("hasAgreedPrivacy");
  // 如果未同意，并且用户未登录，则通过全局状态请求显示弹窗
  if (!hasAgreed && !userStore.isLoggedIn) {
    // PageLayout 组件将会响应该状态
    globalStore.showPrivacyPopup();
  }
});

onShow(() => {
  console.log("App Show");
});
onHide(() => {
  console.log("App Hide");
});
</script>

<template>
  <!-- 
    App.vue 的 template 在小程序端不会被渲染。
    全局UI（如隐私弹窗）的渲染已整合到 PageLayout.vue 组件中。
    请在你的页面，特别是tabBar页面，使用 PageLayout.vue 作为根组件。
  -->
</template>

<style>
/* 全局样式 */
@import "./styles/app.css";
</style>

<style lang="scss">
/* 第三方 UI 库 */
@import "@climblee/uv-ui/index.scss";

::v-deep .uni-nav-bar-text {
  font-size: 34rpx !important;
  font-weight: 500 !important;
  color: #212529;
}

::v-deep .uni-icons {
  font-size: 48rpx !important;
}
</style>
