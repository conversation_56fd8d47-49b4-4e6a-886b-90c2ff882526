<template>
  <view class="page-layout" :style="{ backgroundColor: bgColor }">
    <!-- Custom Navigation Bar -->
    <CustomNavBar v-if="showNavBar" :title="title" />

    <!-- Page Content Slot -->
    <view class="page-content">
      <slot></slot>
    </view>

    <!-- Global Privacy Popup -->
    <PrivacyPopup
      :show="globalStore.isPrivacyPopupVisible"
      @agree="handleAgreePrivacy"
      @reject="handleRejectPrivacy"
      @close="handleClosePrivacy"
    />
  </view>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/global";
import CustomNavBar from "@/components/CustomNavBar.vue";
import PrivacyPopup from "@/components/common/PrivacyPopup.vue";
import { wechatLogin } from "@/api/auth";
import { useUserStore } from "@/stores/user";

// Props for customization
defineProps({
  showNavBar: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "",
  },
  bgColor: {
    type: String,
    default: "var(--bg-page)",
  },
});

onLoad(() => {
  uni.$on("onLogin", () => {
    globalStore.showPrivacyPopup();
  });
});

const globalStore = useGlobalStore();
const userStore = useUserStore();

const handleAgreePrivacy = async (phoneCode: string) => {
  uni.setStorageSync("hasAgreedPrivacy", true);
  globalStore.hidePrivacyPopup();

  // try {
  //   uni.showLoading({ title: "登录中..." });

  //   // 1. 获取 loginCode
  //   const loginRes = await uni.login();
  //   const loginCode = loginRes.code;

  //   // 2. 调用后端登录接口
  //   const res: any = await wechatLogin({
  //     loginCode,
  //     phoneCode,
  //   });

  //   // 3. 更新 Pinia 和本地存储
  //   userStore.setUserInfo(
  //     {
  //       id: res.data.user_id,
  //       name: res.data.nickname,
  //       avatar: res.data.avatar,
  //     },
  //     res.data.token
  //   );
  //   uni.showToast({ title: "登录成功", icon: "success" });
  // } catch (error) {
  //   console.error("Login failed:", error);
  //   uni.showToast({ title: "登录失败，请重试", icon: "none" });
  // } finally {
  //   uni.hideLoading();
  // }
};

const handleRejectPrivacy = () => {
  // 根据业务需求处理拒绝逻辑，例如退出小程序
  uni.showModal({
    title: "提示",
    content: "您需要同意隐私协议才能继续使用",
    showCancel: false,
  });
};

const handleClosePrivacy = () => {
  // 点击遮罩层关闭，也视为临时拒绝
  handleRejectPrivacy();
};
</script>

<style scoped>
.page-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}
</style>
