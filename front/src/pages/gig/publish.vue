<template>
  <view class="container">
    <view class="content">
      <tui-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 基本信息 -->
        <view class="card">
          <view class="section-title">
            <text class="required">*</text>
            <text>基本信息</text>
          </view>

          <form-input
            prop="title"
            label="工作标题"
            :asterisk="true"
            v-model="formData.title"
            placeholder="请输入工作标题"
          />

          <form-input
            prop="workTime"
            label="工作时间"
            :asterisk="true"
            v-model="formData.workTime"
            placeholder="请选择工作时间安排"
            @tap="showTimeSelector = true"
            :disabled="true"
          />
          <tui-datetime
            :show="showTimeSelector"
            @confirm="handleTimeConfirm"
            @cancel="showTimeSelector = false"
          ></tui-datetime>
          <form-input
            prop="location"
            label="工作地点"
            :asterisk="true"
            v-model="formData.location"
            placeholder="请输入详细地址"
            @tap="showAddressSelector = true"
            :disabled="true"
          />
          <AddressSelector
            :show="showAddressSelector"
            @close="showAddressSelector = false"
            @confirm="handleAddressConfirm"
          />
          <form-input
            prop="peopleCount"
            label="招聘人数"
            :asterisk="true"
            v-model="formData.peopleCount"
            placeholder="请输入招聘人数"
            type="number"
            unit="人"
          />
          <form-input
            prop="salary"
            label="工作薪酬"
            :asterisk="true"
            v-model="formData.salary"
            placeholder="请输入薪酬金额"
            type="number"
            unit="元"
          />
          <form-input
            prop="settlement"
            label="结算方式"
            :asterisk="true"
            :arrow="true"
            :bottomBorder="false"
          >
            <picker
              mode="selector"
              :range="settlementList"
              :value="settlementPickerValue"
              @change="onSettlementChange"
            >
              <view class="picker-input">{{
                formData.settlement || "请选择结算方式"
              }}</view>
            </picker>
          </form-input>
        </view>

        <!-- 招聘要求 -->
        <view class="card">
          <view class="section-title">招聘要求</view>
          <form-input prop="gender" label="性别要求" :arrow="true">
            <picker
              mode="selector"
              :range="genderList"
              :value="genderPickerValue"
              @change="onGenderChange"
            >
              <view class="picker-input">{{
                formData.gender || "请选择性别要求"
              }}</view>
            </picker>
          </form-input>
          <form-input prop="ageRange" label="年龄要求" :arrow="true">
            <picker
              mode="selector"
              :range="ageRangeList"
              :value="ageRangePickerValue"
              @change="onAgeRangeChange"
            >
              <view class="picker-input">{{
                formData.ageRange || "请选择年龄范围"
              }}</view>
            </picker>
          </form-input>
          <form-input prop="education" label="学历要求" :arrow="true">
            <picker
              mode="selector"
              :range="educationList"
              :value="educationPickerValue"
              @change="onEducationChange"
            >
              <view class="picker-input">{{
                formData.education || "请选择学历要求"
              }}</view>
            </picker>
          </form-input>
          <form-input prop="experience" label="经验要求" :arrow="true">
            <picker
              mode="selector"
              :range="experienceList"
              :value="experiencePickerValue"
              @change="onExperienceChange"
            >
              <view class="picker-input">{{
                formData.experience || "请选择经验要求"
              }}</view>
            </picker>
          </form-input>
          <form-input
            prop="healthCert"
            label="健康证要求"
            :arrow="true"
            :bottomBorder="false"
          >
            <picker
              mode="selector"
              :range="healthCertList"
              :value="healthCertPickerValue"
              @change="onHealthCertChange"
            >
              <view class="picker-input">{{
                formData.healthCert || "请选择健康证要求"
              }}</view>
            </picker>
          </form-input>
        </view>

        <!-- 工作详情 -->
        <view class="card">
          <view class="section-title">工作详情</view>
          <text class="section-tip"
            >详细描述工作内容、工作环境、注意事项等</text
          >
          <tui-form-item
            prop="description"
            asteriskColor="#ff4757"
            :bottomBorder="false"
            padding="0 0"
            flexStart
          >
            <tui-textarea
              v-model="formData.description"
              placeholder="请详细描述工作内容、要求、工作环境等信息，让求职者更好地了解这份工作"
              maxlength="500"
              height="200rpx"
              minHeight="200rpx"
              :borderTop="false"
              :borderBottom="false"
              backgroundColor="var(--bg-input)"
              color="var(--text-base)"
              placeholderStyle="color: var(--text-grey)"
              showConfirmBar
              isCounter
              counterSize="24"
              counterColor="var(--text-info)"
            />
          </tui-form-item>
        </view>

        <!-- 付费方案 -->
        <view class="card">
          <view class="section-title">付费方案</view>
          <view class="pricing-plans">
            <view
              v-for="(plan, index) in pricingPlans"
              :key="index"
              class="plan-card"
              :class="{
                selected: selectedPlan === index,
                popular: plan.popular,
              }"
              @tap="selectedPlan = index"
            >
              <view v-if="plan.popular" class="popular-badge">推荐</view>
              <view class="plan-name">{{ plan.name }}</view>
              <view class="plan-price">
                <text class="price-value">¥{{ plan.price }}</text>
                <text class="price-duration">/{{ plan.duration }}</text>
              </view>
              <view class="plan-features">
                <view
                  v-for="(feature, fIndex) in plan.features"
                  :key="fIndex"
                  class="feature-item"
                >
                  <text class="i-carbon-checkmark"></text>
                  <text>{{ feature }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="card">
          <view class="section-title">
            <text class="required">*</text>
            <text>联系方式</text>
          </view>
          <form-input
            prop="contactName"
            label="联系人"
            :asterisk="true"
            v-model="formData.contactName"
            placeholder="请输入联系人姓名"
          />
          <form-input
            prop="contactPhone"
            label="联系电话"
            :asterisk="true"
            v-model="formData.contactPhone"
            placeholder="请输入联系电话"
            type="number"
            :bottomBorder="false"
          />
        </view>
      </tui-form>

      <view class="agreement-section">
        <view class="agreement-item" @tap="toggleAgreement">
          <view class="checkbox" :class="{ checked: agreedToTerms }">
            <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
          </view>
          <text class="agreement-text">
            我承诺发布信息真实有效并同意《零工发布服务协议》
          </text>
        </view>
      </view>

      <payment
        :show="showPaymentPopup"
        :amount="pricingPlans[selectedPlan].price"
        @close="showPaymentPopup = false"
        @success="handlePaymentSuccess"
      />

      <view class="submit-section">
        <tui-button
          @click="submitForm"
          type="primary"
          width="100%"
          height="96rpx"
          :bold="true"
          shape="circle"
        >
          付费发布
        </tui-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import FormInput from "@/components/common/FormInput.vue";
import Payment from "@/components/common/Payment.vue";
import AddressSelector from "@/components/AddressSelector.vue";
import tuiDatetime from "@/components/thorui/tui-datetime/tui-datetime.vue";
import {
  GENDER_OPTIONS,
  AGE_RANGE_OPTIONS,
  EDUCATION_OPTIONS,
  GIG_EXPERIENCE_OPTIONS,
  HEALTH_CERTIFICATE_OPTIONS,
  GIG_CATEGORY_OPTIONS,
  SETTLEMENT_OPTIONS,
} from "@/constants/common";

const formRef = ref();
const agreedToTerms = ref(false);
const showPaymentPopup = ref(false);
const selectedPlan = ref(0);
const showAddressSelector = ref(false);
const showTimeSelector = ref(false);

const pricingPlans = ref([
  {
    name: "普通发布",
    price: 9,
    description: "普通展示，7天有效期",
    features: ["普通展示", "7天有效期"],
  },
  {
    name: "置顶推荐",
    price: 29,
    description: "首页置顶3天，优先展示",
    features: ["首页置顶3天", "优先展示", "专属标识"],
    popular: true,
  },
  {
    name: "紧急招聘",
    price: 49,
    description: "加急标记，24小时内快速匹配",
    features: ["加急标记", "24小时内快速匹配", "专属客服"],
  },
]);

// 表单数据
const formData = reactive({
  title: "",
  workTime: "",
  location: "",
  peopleCount: "",
  salary: "",
  settlement: "",
  gender: "",
  ageRange: "",
  education: "",
  experience: "",
  healthCert: "",
  description: "",
  contactName: "",
  contactPhone: "",
});

// 表单验证规则
const formRules = reactive({
  title: [{ required: true, message: "请输入工作标题" }],

  workTime: [{ required: true, message: "请输入工作时间" }],
  location: [{ required: true, message: "请输入工作地点" }],
  peopleCount: [{ required: true, message: "请输入招聘人数" }],
  salary: [{ required: true, message: "请输入工作薪酬" }],
  settlement: [{ required: true, message: "请选择结算方式" }],
  contactName: [{ required: true, message: "请输入联系人姓名" }],
  contactPhone: [
    { required: true, message: "请输入联系电话" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" },
  ],
});

// Picker选项列表

const settlementList = computed(() =>
  SETTLEMENT_OPTIONS.map((item) => item.label)
);
const genderList = computed(() => GENDER_OPTIONS.map((item) => item.label));
const ageRangeList = computed(() =>
  AGE_RANGE_OPTIONS.map((item) => item.label)
);
const educationList = computed(() =>
  EDUCATION_OPTIONS.map((item) => item.label)
);
const experienceList = computed(() =>
  GIG_EXPERIENCE_OPTIONS.map((item) => item.name)
);
const healthCertList = computed(() =>
  HEALTH_CERTIFICATE_OPTIONS.map((item) => item.label)
);

// Picker值管理

const settlementPickerValue = ref(0);
const genderPickerValue = ref(0);
const ageRangePickerValue = ref(0);
const educationPickerValue = ref(0);
const experiencePickerValue = ref(0);
const healthCertPickerValue = ref(0);

// Picker变更处理

const onSettlementChange = (e: any) => {
  const index = e.detail.value;
  settlementPickerValue.value = index;
  formData.settlement = SETTLEMENT_OPTIONS[index].label;
};

const onGenderChange = (e: any) => {
  const index = e.detail.value;
  genderPickerValue.value = index;
  formData.gender = GENDER_OPTIONS[index].label;
};

const onAgeRangeChange = (e: any) => {
  const index = e.detail.value;
  ageRangePickerValue.value = index;
  formData.ageRange = AGE_RANGE_OPTIONS[index].label;
};

const onEducationChange = (e: any) => {
  const index = e.detail.value;
  educationPickerValue.value = index;
  formData.education = EDUCATION_OPTIONS[index].label;
};

const onExperienceChange = (e: any) => {
  const index = e.detail.value;
  experiencePickerValue.value = index;
  formData.experience = GIG_EXPERIENCE_OPTIONS[index].name;
};

const onHealthCertChange = (e: any) => {
  const index = e.detail.value;
  healthCertPickerValue.value = index;
  formData.healthCert = HEALTH_CERTIFICATE_OPTIONS[index].label;
};

const handleAddressConfirm = (e: any) => {
  formData.location = e.fullPath;
  showAddressSelector.value = false;
};

const handleTimeConfirm = (e: any) => {
  formData.workTime = e.result;
  showTimeSelector.value = false;
};

// 切换协议同意状态
const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

// 返回
const goBack = () => {
  uni.navigateBack();
};

// 提交表单
const submitForm = async () => {
  if (!agreedToTerms.value) {
    uni.showToast({
      title: "请先同意服务协议",
      icon: "none",
    });
    return;
  }

  try {
    const isValid = await formRef.value.checkAll();
    if (isValid) {
      showPaymentPopup.value = true;
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

const handlePaymentSuccess = () => {
  // 这里处理提交逻辑
  console.log("表单数据:", formData);

  uni.showToast({
    title: "发布成功",
    icon: "success",
  });

  setTimeout(() => {
    uni.navigateBack();
  }, 1500);
};

onMounted(() => {
  // 初始化默认值
  formData.gender = "不限";
  formData.healthCert = "不需要";
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.content {
  margin: 20rpx;
  padding: 0;
  padding-bottom: 160rpx; /* Adjusted for bottom action bar */
}

.card {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  padding: 32rpx 0;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding: 0 32rpx;

  .required {
    color: #ff4757;
    margin-right: 8rpx;
  }
}

.section-tip {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
  line-height: 1.6;
}

.picker-input {
  height: 100%;
  display: flex;
  align-items: center;
  color: #333;
  font-size: 28rpx;
}

.pricing-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 24rpx;
  padding: 0 32rpx;
}

.plan-card {
  background-color: #f8f8f8;
  border: 2rpx solid #eee;
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;

  &.popular {
    border-color: #007bff;
    box-shadow: 0 8rpx 24rpx rgba(0, 123, 255, 0.2);
  }

  &.selected {
    background-color: #e6f7ff;
    border-color: #007bff;
  }
}

.popular-badge {
  position: absolute;
  top: -1rpx;
  right: 20rpx;
  background-color: #ffc107;
  color: #333;
  font-size: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 0 0 12rpx 12rpx;
  font-weight: 600;
}

.plan-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.plan-price {
  margin-bottom: 20rpx;

  .price-value {
    font-size: 40rpx;
    font-weight: bold;
    color: #ff5050;
  }

  .price-duration {
    font-size: 24rpx;
    color: #666;
    margin-left: 4rpx;
  }
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 20rpx;

  .feature-item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #666;

    .i-carbon-checkmark {
      font-size: 20rpx;
      color: #28a745;
      margin-right: 8rpx;
    }
  }
}

.agreement-section {
  margin: 48rpx 32rpx 32rpx;
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;

  &.checked {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
  }
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
}

.tui-button {
  background-color: #007bff;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  height: 96rpx;
  border-radius: 48rpx;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
  }
}
</style>
