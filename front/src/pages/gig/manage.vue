<template>
  <view class="gig-manage-page">
    <!-- 渐变背景 -->
    <view class="gradient-bg"></view>

    <!-- 统计概览 -->
    <view class="stats-section">
      <view class="stats-card glass-card">
        <view class="stat-item">
          <text class="stat-number">{{ publishedGigs.length }}</text>
          <text class="stat-label">我发布的</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ appliedGigs.length }}</text>
          <text class="stat-label">我报名的</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ pendingCount }}</text>
          <text class="stat-label">待处理</text>
        </view>
      </view>
    </view>

    <!-- 标签页导航 -->
    <view class="tabs-section">
      <view class="tabs-wrapper glass-card">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'published' }"
          @tap="switchTab('published')"
        >
          <text class="tab-text">我发布的</text>
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'applied' }"
          @tap="switchTab('applied')"
        >
          <text class="tab-text">我报名的</text>
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <view class="content-section">
      <!-- 我发布的列表 -->
      <view v-if="activeTab === 'published'" class="published-list">
        <view v-if="publishedGigs.length > 0" class="gigs-list">
          <view
            v-for="gig in publishedGigs"
            :key="gig.id"
            class="gig-item glass-card"
            @tap="viewDetail(gig.id)"
          >
            <view class="item-header">
              <view class="title-area">
                <text class="item-title">{{ gig.title }}</text>
                <view class="status-badge" :class="gig.status">
                  <text>{{ gig.statusText }}</text>
                </view>
              </view>
              <view class="price-area">
                <text class="price-amount">¥{{ gig.price }}</text>
                <text class="price-unit">/{{ gig.priceUnit }}</text>
              </view>
            </view>

            <view class="item-info">
              <view class="info-item">
                <text class="i-carbon-user-multiple info-icon"></text>
                <text class="info-text">{{ gig.applicantsCount }}人报名</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-time info-icon"></text>
                <text class="info-text">{{ formatTime(gig.createdTime) }}</text>
              </view>
            </view>

            <view class="item-actions">
              <view
                class="action-btn primary"
                @tap.stop="navigateToApplicants(gig.id)"
              >
                <text class="i-carbon-view"></text>
                <text>查看报名</text>
              </view>
              <view class="action-btn secondary" @tap.stop="editGig(gig.id)">
                <text class="i-carbon-edit"></text>
                <text>编辑</text>
              </view>
              <view class="action-btn danger" @tap.stop="closeGig(gig.id)">
                <text class="i-carbon-close"></text>
                <text>关闭</text>
              </view>
            </view>
          </view>
        </view>

        <view v-else class="empty-state">
          <view class="empty-icon">
            <text class="i-carbon-add-alt"></text>
          </view>
          <text class="empty-title">暂无发布的零工</text>
          <text class="empty-desc">发布零工信息，快速找到合适的工人</text>
          <view class="empty-action" @tap="goToPublish">
            <text>立即发布</text>
          </view>
        </view>
      </view>

      <!-- 我报名的列表 -->
      <view v-if="activeTab === 'applied'" class="applied-list">
        <view v-if="appliedGigs.length > 0" class="gigs-list">
          <view
            v-for="gig in appliedGigs"
            :key="gig.id"
            class="gig-item glass-card"
            @tap="viewDetail(gig.id)"
          >
            <view class="item-header">
              <view class="title-area">
                <text class="item-title">{{ gig.title }}</text>
                <view class="status-badge" :class="gig.status">
                  <text>{{ gig.statusText }}</text>
                </view>
              </view>
              <view class="price-area">
                <text class="price-amount">¥{{ gig.price }}</text>
                <text class="price-unit">/{{ gig.priceUnit }}</text>
              </view>
            </view>

            <view class="item-info">
              <view class="info-item">
                <text class="i-carbon-location info-icon"></text>
                <text class="info-text">{{ gig.location }}</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-time info-icon"></text>
                <text class="info-text">{{ gig.workTime }}</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-calendar info-icon"></text>
                <text class="info-text">{{ formatDate(gig.workDate) }}</text>
              </view>
            </view>

            <view class="employer-info">
              <view class="employer-avatar">
                <image :src="gig.employer.avatar" class="avatar-img" />
              </view>
              <view class="employer-detail">
                <text class="employer-name">{{ gig.employer.name }}</text>
                <text class="employer-rating">
                  <text class="i-carbon-star-filled rating-icon"></text>
                  {{ gig.employer.rating }}
                </text>
              </view>
              <view class="contact-actions">
                <view
                  v-if="gig.status === 'hired'"
                  class="action-btn success"
                  @tap.stop="contactEmployer(gig)"
                >
                  <text class="i-carbon-chat"></text>
                  <text>联系雇主</text>
                </view>
                <view
                  v-else-if="gig.status === 'pending'"
                  class="action-btn warning"
                  @tap.stop="cancelApplication(gig.id)"
                >
                  <text class="i-carbon-close"></text>
                  <text>取消报名</text>
                </view>
                <view
                  v-else-if="gig.status === 'rejected'"
                  class="action-btn secondary"
                  @tap.stop="viewFeedback(gig.id)"
                >
                  <text class="i-carbon-information"></text>
                  <text>查看反馈</text>
                </view>
              </view>
            </view>

            <view v-if="gig.tags && gig.tags.length > 0" class="item-tags">
              <text v-for="tag in gig.tags" :key="tag" class="tag">{{
                tag
              }}</text>
            </view>
          </view>
        </view>

        <view v-else class="empty-state">
          <view class="empty-icon">
            <text class="i-carbon-search"></text>
          </view>
          <text class="empty-title">暂无报名记录</text>
          <text class="empty-desc">去发现更多零工机会，开始你的第一份兼职</text>
          <view class="empty-action" @tap="goToExplore">
            <text>去发现</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import dayjs from "dayjs";

const activeTab = ref("published");

onLoad((options) => {
  if (options && options.tab) {
    activeTab.value = options.tab;
  }
});

const publishedGigs = ref([
  {
    id: 1,
    title: "周末招聘传单派发员",
    status: "recruiting",
    statusText: "招聘中",
    price: "150",
    priceUnit: "天",
    applicantsCount: 5,
    createdTime: dayjs().subtract(2, "day").toISOString(),
  },
  {
    id: 2,
    title: "急招临时搬家师傅",
    status: "working",
    statusText: "进行中",
    price: "300",
    priceUnit: "次",
    applicantsCount: 1,
    createdTime: dayjs().subtract(1, "day").toISOString(),
  },
  {
    id: 3,
    title: "家庭日常保洁",
    status: "completed",
    statusText: "已完成",
    price: "40",
    priceUnit: "小时",
    applicantsCount: 1,
    createdTime: dayjs().subtract(5, "day").toISOString(),
  },
]);

const appliedGigs = ref([
  {
    id: 4,
    title: "活动现场协助人员",
    status: "hired",
    statusText: "已录用",
    price: "200",
    priceUnit: "天",
    location: "国际会展中心",
    workTime: "09:00-18:00",
    workDate: dayjs().add(2, "day").toISOString(),
    tags: ["活动", "包餐", "轻松"],
    employer: {
      name: "活动策划公司",
      avatar: "https://randomuser.me/api/portraits/lego/1.jpg",
      rating: 4.8,
    },
  },
  {
    id: 5,
    title: "宠物店临时看护",
    status: "rejected",
    statusText: "未录用",
    price: "100",
    priceUnit: "天",
    location: "宠物乐园",
    workTime: "10:00-18:00",
    workDate: dayjs().add(1, "day").toISOString(),
    tags: ["动物", "轻松"],
    employer: {
      name: "爱宠之家",
      avatar: "https://randomuser.me/api/portraits/lego/2.jpg",
      rating: 4.5,
    },
  },
  {
    id: 6,
    title: "超市收银员",
    status: "pending",
    statusText: "待审核",
    price: "120",
    priceUnit: "天",
    location: "万达广场",
    workTime: "09:00-21:00",
    workDate: dayjs().add(3, "day").toISOString(),
    tags: ["室内", "稳定"],
    employer: {
      name: "万达超市",
      avatar: "https://randomuser.me/api/portraits/lego/3.jpg",
      rating: 4.6,
    },
  },
]);

// 计算属性
const pendingCount = computed(() => {
  return (
    appliedGigs.value.filter((gig) => gig.status === "pending").length +
    publishedGigs.value.filter((gig) => gig.status === "recruiting").length
  );
});

// 方法
const switchTab = (tab: "published" | "applied") => {
  activeTab.value = tab;
};

const formatTime = (time: string) => {
  const now = dayjs();
  const targetTime = dayjs(time);
  const diffDays = now.diff(targetTime, "day");

  if (diffDays === 0) {
    return "今天发布";
  } else if (diffDays === 1) {
    return "昨天发布";
  } else if (diffDays < 7) {
    return `${diffDays}天前发布`;
  } else {
    return targetTime.format("MM-DD发布");
  }
};

const formatDate = (date: string) => {
  const now = dayjs();
  const targetDate = dayjs(date);
  const diffDays = targetDate.diff(now, "day");

  if (diffDays === 0) {
    return "今天";
  } else if (diffDays === 1) {
    return "明天";
  } else if (diffDays < 7) {
    return `${diffDays}天后`;
  } else {
    return targetDate.format("MM月DD日");
  }
};

const navigateToApplicants = (gigId: number) => {
  uni.navigateTo({
    url: `/pages/gig/applicants?gigId=${gigId}`,
  });
};

const viewDetail = (gigId: number) => {
  uni.navigateTo({
    url: `/pages/gig/detail?id=${gigId}`,
  });
};

const editGig = (gigId: number) => {
  uni.showToast({
    title: "功能开发中",
    icon: "none",
  });
};

const closeGig = (gigId: number) => {
  uni.showModal({
    title: "确认关闭",
    content: "关闭后将无法继续招聘，确定要关闭这个零工吗？",
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "已关闭",
          icon: "success",
        });
      }
    },
  });
};

const contactEmployer = (gig: any) => {
  uni.showModal({
    title: "联系雇主",
    content: `是否要联系 ${gig.employer.name}？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "正在跳转到聊天",
          icon: "none",
        });
      }
    },
  });
};

const cancelApplication = (gigId: number) => {
  uni.showModal({
    title: "确认取消",
    content: "确定要取消报名吗？取消后无法恢复。",
    success: (res) => {
      if (res.confirm) {
        appliedGigs.value = appliedGigs.value.filter((gig) => gig.id !== gigId);
        uni.showToast({
          title: "已取消报名",
          icon: "success",
        });
      }
    },
  });
};

const viewFeedback = (gigId: number) => {
  uni.showModal({
    title: "未录用原因",
    content: "很抱歉，您的条件暂时不符合要求，请继续关注其他机会。",
    showCancel: false,
  });
};

const goToPublish = () => {
  uni.navigateTo({
    url: "/pages/gig/publish",
  });
};

const goToExplore = () => {
  uni.navigateTo({
    url: "/pages/gig/index",
  });
};
</script>

<style lang="scss" scoped>
.gig-manage-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.stats-section {
  padding: 32rpx;
  margin-top: 20rpx;
}

.stats-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
  flex: 1;

  .stat-number {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: #333;
    margin-bottom: 8rpx;
  }

  .stat-label {
    font-size: 24rpx;
    color: #999;
  }
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background: #eee;
  margin: 0 32rpx;
}

.tabs-section {
  padding: 0 32rpx 24rpx;
}

.tabs-wrapper {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  padding: 16rpx;
  text-align: center;
  border-radius: 20rpx;
  transition: all 0.3s ease;

  .tab-text {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
  }

  &.active {
    background-color: #007bff;
    box-shadow: 0 4rpx 16rpx rgba(0, 123, 255, 0.3);

    .tab-text {
      color: white;
      font-weight: 600;
    }
  }
}

.content-section {
  padding: 0 32rpx 40rpx;
}

.gigs-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.gig-item {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.title-area {
  flex: 1;
  margin-right: 16rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.status-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.recruiting {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  &.working {
    background-color: #fffbe6;
    color: #faad14;
  }

  &.completed {
    background-color: #f0f0f0;
    color: #666;
  }

  &.hired {
    background-color: #f6ffed;
    color: #52c41a;
  }

  &.rejected {
    background-color: #fff1f0;
    color: #f5222d;
  }

  &.pending {
    background-color: #fffbe6;
    color: #faad14;
  }
}

.price-area {
  text-align: right;

  .price-amount {
    font-size: 32rpx;
    font-weight: 700;
    color: #007bff;
  }

  .price-unit {
    font-size: 22rpx;
    color: #666;
    margin-left: 4rpx;
  }
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.info-icon {
  font-size: 28rpx;
  color: #999;
  width: 28rpx;
  flex-shrink: 0;
}

.info-text {
  font-size: 26rpx;
  color: #666;
}

.employer-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #eee;
}

.employer-avatar {
  .avatar-img {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background-color: #eee;
  }
}

.employer-detail {
  flex: 1;

  .employer-name {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }

  .employer-rating {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 24rpx;
    color: #999;

    .rating-icon {
      color: #ffc107;
      font-size: 28rpx;
    }
  }
}

.contact-actions {
  flex-shrink: 0;
  display: flex;
  gap: 16rpx;
}

.item-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  margin-top: 24rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background-color: #e0f7fa;
  color: #00796b;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.item-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 24rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;

  &.primary {
    background-color: #007bff;
    color: white;
  }

  &.secondary {
    background-color: #f0f0f0;
    color: #333;
  }

  &.danger {
    background-color: #f8d7da;
    color: #dc3545;
  }

  &.success {
    background-color: #d4edda;
    color: #28a745;
  }

  &.warning {
    background-color: #fff3cd;
    color: #ffc107;
  }

  &:active {
    transform: scale(0.95);
  }
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  margin: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 120rpx;
  color: #ccc;
  margin-bottom: 32rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-action {
  display: inline-block;
  padding: 16rpx 32rpx;
  background-color: #007bff;
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: #0056b3;
  }
}
</style>
