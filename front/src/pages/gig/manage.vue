<template>
  <view class="container">

    <view class="summary-section">
      <view class="summary-card">
        <view class="value text-primary">18</view>
        <view class="label text-secondary">本周完成</view>
      </view>
      <view class="summary-card">
        <view class="value text-green">3</view>
        <view class="label text-secondary">进行中</view>
      </view>
      <view class="summary-card">
        <view class="value text-yellow">5</view>
        <view class="label text-secondary">待开始</view>
      </view>
    </view>

    <view class="filter-section">
      <tui-tab
        :tabs="tabs"
        :current="activeTab"
        @change="(index) => activeTab = tabs[index].key"
        :scroll="true"
        :color="'var(--text-secondary)'"
        :selectedColor="'var(--primary)'"
        :backgroundColor="'transparent'"
        :selectedBackgroundColor="'var(--bg-primary-light)'"
        :borderRadius="16"
      />
    </view>

    <view class="gig-list">
      <view v-for="gig in gigs" :key="gig.id" class="card bg-card shadow-sm">
        <view class="card-header">
          <text class="title text-base font-bold">{{ gig.title }}</text>
          <view class="status-badge" :class="statusClass(gig.status)">
            <text>{{ getStatusText(gig.status) }}</text>
          </view>
        </view>
        <view class="card-body">
          <view class="info-row">
            <view class="i-mdi-clock-outline text-info mr-1"></view>
            <text class="text-secondary">{{ gig.time }}</text>
          </view>
          <view class="info-row">
            <view class="i-mdi-map-marker-outline text-info mr-1"></view>
            <text class="text-secondary">{{ gig.location }}</text>
          </view>
          <view class="tag-container">
            <text v-for="tag in gig.tags" :key="tag" class="tag tag-sm">{{ tag }}</text>
          </view>
          <view class="salary-enrollment">
            <text class="salary text-red font-bold">¥{{ gig.salary }}/天</text>
            <text class="enrollment text-info">已报名 {{ gig.enrolled }}/{{ gig.total }} 人</text>
          </view>
        </view>
        <view class="card-footer">
          <view
            v-for="button in getActionButtons(gig.status)"
            :key="button.text"
            class="action-btn"
            :class="button.type"
          >
            {{ button.text }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CustomNavBar from '@/components/CustomNavBar.vue';

const activeTab = ref('all');

const tabs = [
  { key: 'all', name: '全部' },
  { key: 'recruiting', name: '招聘中' },
  { key: 'ongoing', name: '进行中' },
  { key: 'completed', name: '已完成' },
];

const gigs = ref([
  {
    id: 1,
    title: '餐厅服务员',
    status: 'recruiting',
    time: '8月15日 08:00-17:30',
    location: '上海市浦东新区张江高科技园区',
    tags: ['大专及以上', '18-35岁', '男女不限'],
    salary: 180,
    enrolled: 12,
    total: 20,
  },
  {
    id: 2,
    title: '夜班仓库管理',
    status: 'ongoing',
    time: '8月15日 22:00 - 8月16日 06:00',
    location: '上海市嘉定区安亭镇',
    tags: ['高中及以上', '20-45岁', '男性优先'],
    salary: 220,
    enrolled: 8,
    total: 15,
  },
  {
    id: 3,
    title: '快递分拣员',
    status: 'completed',
    time: '8月14日 09:00-18:00',
    location: '上海市松江区九亭镇',
    tags: ['学历不限', '18-50岁', '男性优先'],
    salary: 160,
    enrolled: 20,
    total: 20,
  },
  {
    id: 4,
    title: '活动场地布置',
    status: 'paused',
    time: '8月17日 14:00-20:00',
    location: '上海市静安区南京西路',
    tags: ['学历不限', '18-40岁', '男女不限'],
    salary: 200,
    enrolled: 5,
    total: 10,
  },
  {
    id: 5,
    title: '夜间配送员',
    status: 'cancelled',
    time: '8月13日 19:00 - 8月14日 01:00',
    location: '上海市徐汇区漕河泾',
    tags: ['初中及以上', '20-45岁', '男性'],
    salary: 240,
    enrolled: 3,
    total: 15,
  },
]);

const statusClass = (status: string) => {
  switch (status) {
    case 'recruiting':
      return 'status-recruiting';
    case 'ongoing':
      return 'status-ongoing';
    case 'completed':
      return 'status-completed';
    case 'paused':
      return 'status-paused';
    case 'cancelled':
      return 'status-cancelled';
    default:
      return '';
  }
};

const getStatusText = (status: string) => {
  const statusMap = {
    all: '全部',
    recruiting: '招聘中',
    ongoing: '进行中',
    completed: '已完成',
  };
  return statusMap[status] || status;
};

const getActionButtons = (status: string) => {
  switch (status) {
    case 'recruiting':
      return [{ text: '查看报名', type: 'primary' }, { text: '编辑', type: 'secondary' }, { text: '暂停', type: 'warning' }];
    case 'ongoing':
      return [{ text: '联系工人', type: 'success' }, { text: '标记完成', type: 'primary' }, { text: '取消', type: 'danger' }];
    case 'completed':
      return [{ text: '查看评价', type: 'info' }, { text: '再次发布', type: 'secondary' }];
    case 'paused':
      return [{ text: '继续招聘', type: 'success' }, { text: '编辑', type: 'secondary' }, { text: '取消', type: 'danger' }];
    case 'cancelled':
      return [{ text: '重新发布', type: 'primary' }, { text: '删除', type: 'danger' }];
    default:
      return [{ text: '详情', type: 'primary' }];
  }
};

// 新增的操作方法
const navigateToApplicants = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/applicants?gigId=${gigId}` });
};

const editGig = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/edit?id=${gigId}` });
};

const closeGig = (gigId: number) => {
  uni.showModal({
    title: "确认操作",
    content: "确定要停止这个零工的招聘吗？",
    success: (res) => {
      if (res.confirm) {
        const gigIndex = gigs.value.findIndex((g) => g.id === gigId);
        if (gigIndex !== -1) {
          gigs.value[gigIndex].status = 'cancelled';
        }
        uni.showToast({ title: "已停止招聘", icon: "success" });
      }
    },
  });
};

const completeGig = (gigId: number) => {
  uni.showModal({
    title: "确认完成",
    content: "确定标记这个零工为已完成吗？",
    success: (res) => {
      if (res.confirm) {
        const gigIndex = gigs.value.findIndex((g) => g.id === gigId);
        if (gigIndex !== -1) {
          gigs.value[gigIndex].status = 'completed';
        }
        uni.showToast({ title: "已标记完成", icon: "success" });
      }
    },
  });
};

const republishGig = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/republish?id=${gigId}` });
};

const deleteGig = (gigId: number) => {
  uni.showModal({
    title: "确认删除",
    content: "删除后无法恢复，确定要删除这个零工吗？",
    success: (res) => {
      if (res.confirm) {
        const gigIndex = gigs.value.findIndex((g) => g.id === gigId);
        if (gigIndex !== -1) {
          gigs.value.splice(gigIndex, 1);
        }
        uni.showToast({ title: "删除成功", icon: "success" });
      }
    },
  });
};

const viewFeedback = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/feedback?id=${gigId}` });
};
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.summary-section {
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-16);
  background-color: var(--bg-card);
  margin: var(--spacing-12);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
}

.summary-card {
  text-align: center;
  
  .value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
  }
  
  .label {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-4);
  }
}

.filter-section {
  padding: 0 var(--spacing-12);
  margin-bottom: var(--spacing-12);
  position: sticky;
  top: 0;
  background-color: var(--bg-page);
  z-index: 10;
}

.gig-list {
  padding: 0 var(--spacing-12);
}

.card {
  border-radius: var(--radius);
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--spacing-8);
  border-bottom: 1rpx solid var(--border-color);

  .title {
    font-size: var(--font-size-lg);
  }
  
  .status-badge {
    padding: var(--spacing-6) var(--spacing-12);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }
  
  .status-recruiting {
    background-color: var(--bg-primary-light);
    color: var(--primary);
  }
  .status-ongoing {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }
  .status-completed {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }
  .status-paused {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }
  .status-cancelled {
    background-color: var(--bg-tag);
    color: var(--text-grey);
  }
}

.card-body {
  padding: var(--spacing-12) 0;
  
  .info-row {
    display: flex;
    align-items: center;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-8);
  }
  
  .tag-container {
    margin-bottom: var(--spacing-8);
  }
  
  .salary-enrollment {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-8);
    
    .salary {
      font-size: var(--font-size-lg);
    }
    
    .enrollment {
      font-size: var(--font-size-xs);
    }
  }
}

.card-footer {
  display: flex;
  gap: var(--spacing-8);
  flex-wrap: wrap;
  justify-content: flex-end;
  padding-top: var(--spacing-8);
  border-top: 1rpx solid var(--border-color);
  margin-top: var(--spacing-4);
  
  .action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-8) var(--spacing-12);
    border-radius: var(--radius);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all 0.3s ease;
    
    &.primary {
      background-color: var(--primary);
      color: var(--text-inverse);
    }
    
    &.secondary {
      background-color: var(--bg-tag);
      color: var(--text-secondary);
      border: 1rpx solid var(--border-color);
    }
    
    &.success {
      background-color: var(--bg-success-light);
      color: var(--text-green);
    }
    
    &.warning {
      background-color: var(--bg-warning-light);
      color: var(--text-yellow);
    }
    
    &.danger {
      background-color: var(--bg-danger-light);
      color: var(--text-red);
    }
    
    &.info {
      background-color: var(--bg-info-light);
      color: var(--text-blue);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}
</style>