<template>
  <view class="gig-card" @tap="goToDetail">
    <view class="card-content">
      <view class="header">
        <text class="title">{{ gig.title }}</text>
        <view class="price">
          <text class="price-value">{{ gig.price }}</text>
          <text class="price-unit">元/{{ gig.price_unit }}</text>
        </view>
      </view>
      <view class="tags">
        <view v-for="(tag, index) in gig.tags" :key="index" class="tag">{{ tag }}</view>
      </view>
      <view class="footer">
        <view class="location">
          <text class="i-carbon-location-current"></text>
          <text>{{ gig.location }} | {{ gig.distance }}</text>
        </view>
        <view class="employer">
          <image :src="gig.employer.avatar" class="avatar" />
          <text class="name">{{ gig.employer.name }}</text>
          <text v-if="gig.employer.verified" class="i-carbon-badge-filled verified-icon"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

const props = defineProps({
  gig: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const goToDetail = () => {
  uni.navigateTo({ url: `/pages/gig/detail?id=${props.gig.id}` });
};
</script>

<style lang="scss" scoped>
.gig-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  padding: var(--spacing-16);

  &:active {
    transform: scale(0.98);
  }
}

.card-content {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-8);
}

.title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  line-height: 1.4;
  max-width: 70%;
}

.price {
  white-space: nowrap;
  text-align: right;
}

.price-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary);
}

.price-unit {
  font-size: var(--font-size-sm);
  color: var(--primary);
  margin-left: var(--spacing-4);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-12);
}

.tag {
  background-color: var(--bg-tag);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-sm);
  margin-right: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--spacing-8);
}

.location {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-info);

  .i-carbon-location-current {
    margin-right: var(--spacing-4);
  }
}

.employer {
  display: flex;
  align-items: center;
}

.avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: var(--spacing-6);
  border: 1px solid var(--border-color);
}

.name {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.verified-icon {
  color: var(--text-blue);
  margin-left: var(--spacing-4);
}
</style>