<template>
  <view class="gig-calendar-page">
    <!-- 顶部统计卡片 -->
    <view class="stats-section">
      <view class="stats-card glass-card">
        <view class="stat-item">
          <text class="stat-number">{{ totalGigs }}</text>
          <text class="stat-label">本月零工</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ completedGigs }}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ pendingGigs }}</text>
          <text class="stat-label">待进行</text>
        </view>
      </view>
    </view>

    <!-- 周日期选择器 -->
    <view class="week-selector-section">
      <view class="section-title">
        <text class="title-text">选择日期</text>
        <text class="current-month">{{ currentMonth }}</text>
      </view>
      <view class="week-calendar-wrapper glass-card">
        <tui-week-date
          :value="selectedDate"
          :activeColor="'white'"
          :activeBackground="'linear-gradient(135deg, #2673FF, #667eea)'"
          :background="'transparent'"
          :weekColor="'rgba(255,255,255,0.7)'"
          :dateColor="'white'"
          :arrowColor="'white'"
          @click="onDateSelect"
        />
      </view>
    </view>

    <!-- 当日零工列表 -->
    <view class="daily-gigs-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-text">{{ formatDisplayDate(selectedDate) }}</text>
          <text class="gig-count">{{ dailyGigs.length }}个零工</text>
        </view>
        <view v-if="dailyGigs.length > 0" class="filter-tabs">
          <view
            v-for="status in statusTabs"
            :key="status.value"
            class="filter-tab"
            :class="{ active: activeStatus === status.value }"
            @tap="setActiveStatus(status.value)"
          >
            <text>{{ status.label }}</text>
          </view>
        </view>
      </view>

      <view class="gigs-list">
        <view v-if="filteredGigs.length > 0" class="gig-items">
          <view
            v-for="gig in filteredGigs"
            :key="gig.id"
            class="gig-item glass-card"
            @tap="goToGigDetail(gig.id)"
          >
            <view class="gig-header">
              <view class="gig-title-area">
                <text class="gig-title">{{ gig.title }}</text>
                <view class="gig-status" :class="gig.status">
                  <text>{{ getStatusText(gig.status) }}</text>
                </view>
              </view>
              <view class="gig-salary">
                <text class="salary-amount">¥{{ gig.salary }}</text>
                <text class="salary-unit">/{{ gig.unit }}</text>
              </view>
            </view>

            <view class="gig-info">
              <view class="info-item">
                <text class="i-carbon-time info-icon"></text>
                <text class="info-text">{{ gig.time }}</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-location info-icon"></text>
                <text class="info-text">{{ gig.location }}</text>
              </view>
              <view class="info-item">
                <text class="i-carbon-user info-icon"></text>
                <text class="info-text">{{ gig.employerName }}</text>
              </view>
            </view>

            <view class="gig-footer">
              <view class="gig-tags">
                <text v-for="tag in gig.tags" :key="tag" class="gig-tag">{{
                  tag
                }}</text>
              </view>
              <view class="gig-actions">
                <view
                  v-if="gig.status === 'pending'"
                  class="action-btn primary"
                  @tap.stop="confirmGig(gig.id)"
                >
                  <text>确认参加</text>
                </view>
                <view
                  v-else-if="gig.status === 'confirmed'"
                  class="action-btn secondary"
                  @tap.stop="contactEmployer(gig)"
                >
                  <text>联系雇主</text>
                </view>
                <view
                  v-else-if="gig.status === 'completed'"
                  class="action-btn success"
                  @tap.stop="viewResult(gig.id)"
                >
                  <text>查看结果</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view v-else class="empty-state">
          <view class="empty-icon">
            <text class="i-carbon-calendar"></text>
          </view>
          <text class="empty-title">{{ getEmptyTitle() }}</text>
          <text class="empty-desc">{{ getEmptyDesc() }}</text>
          <view class="empty-action" @tap="goToGigList">
            <text>去看看其他零工</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import dayjs from "dayjs";

// 数据状态
const selectedDate = ref(dayjs().format("YYYY-MM-DD"));
const activeStatus = ref("all");

// 状态标签页
const statusTabs = ref([
  { label: "全部", value: "all" },
  { label: "待确认", value: "pending" },
  { label: "已确认", value: "confirmed" },
  { label: "已完成", value: "completed" },
]);

// 模拟零工数据
const allGigs = ref([
  {
    id: 1,
    title: "周末传单派发员",
    date: dayjs().format("YYYY-MM-DD"),
    time: "09:00-18:00",
    location: "市中心广场",
    salary: 150,
    unit: "天",
    status: "pending",
    employerName: "广告传媒公司",
    tags: ["轻松", "日结"],
    description: "在市中心广场派发传单，工作轻松",
  },
  {
    id: 2,
    title: "家庭保洁服务",
    date: dayjs().format("YYYY-MM-DD"),
    time: "14:00-17:00",
    location: "阳光花园小区",
    salary: 80,
    unit: "小时",
    status: "confirmed",
    employerName: "李女士",
    tags: ["居家", "按时结算"],
    description: "家庭日常保洁，包括客厅、卧室、厨房清洁",
  },
  {
    id: 3,
    title: "搬家助手",
    date: dayjs().add(1, "day").format("YYYY-MM-DD"),
    time: "08:00-12:00",
    location: "幸福小区",
    salary: 200,
    unit: "次",
    status: "pending",
    employerName: "张先生",
    tags: ["体力活", "现结"],
    description: "协助搬家，主要是搬运家具和纸箱",
  },
  {
    id: 4,
    title: "活动现场协助",
    date: dayjs().add(1, "day").format("YYYY-MM-DD"),
    time: "全天",
    location: "国际会展中心",
    salary: 300,
    unit: "天",
    status: "confirmed",
    employerName: "活动策划公司",
    tags: ["活动", "包餐"],
    description: "协助活动现场布置、引导、清理等工作",
  },
  {
    id: 5,
    title: "临时收银员",
    date: dayjs().add(2, "day").format("YYYY-MM-DD"),
    time: "10:00-22:00",
    location: "万达广场超市",
    salary: 120,
    unit: "天",
    status: "completed",
    employerName: "万达超市",
    tags: ["室内", "轻松"],
    description: "超市收银工作，要求熟悉收银系统操作",
  },
  {
    id: 6,
    title: "配送员",
    date: dayjs().add(3, "day").format("YYYY-MM-DD"),
    time: "18:00-22:00",
    location: "市区范围",
    salary: 40,
    unit: "单",
    status: "pending",
    employerName: "美团外卖",
    tags: ["配送", "多劳多得"],
    description: "晚餐时段外卖配送，按单计费",
  },
  {
    id: 7,
    title: "促销员",
    date: dayjs().add(5, "day").format("YYYY-MM-DD"),
    time: "09:00-18:00",
    location: "步行街商场",
    salary: 180,
    unit: "天",
    status: "confirmed",
    employerName: "化妆品专柜",
    tags: ["销售", "提成"],
    description: "化妆品促销，有销售提成",
  },
  {
    id: 8,
    title: "装修助手",
    date: dayjs().add(7, "day").format("YYYY-MM-DD"),
    time: "08:00-17:00",
    location: "新城区别墅",
    salary: 250,
    unit: "天",
    status: "pending",
    employerName: "装修公司",
    tags: ["装修", "包午餐"],
    description: "协助装修师傅进行室内装修工作",
  },
]);

// 计算属性
const currentMonth = computed(() => {
  return dayjs(selectedDate.value).format("YYYY年MM月");
});

const dailyGigs = computed(() => {
  return allGigs.value.filter((gig) => gig.date === selectedDate.value);
});

const filteredGigs = computed(() => {
  if (activeStatus.value === "all") {
    return dailyGigs.value;
  }
  return dailyGigs.value.filter((gig) => gig.status === activeStatus.value);
});

const totalGigs = computed(() => {
  const currentMonthStart = dayjs().startOf("month").format("YYYY-MM-DD");
  const currentMonthEnd = dayjs().endOf("month").format("YYYY-MM-DD");
  return allGigs.value.filter(
    (gig) =>
      dayjs(gig.date).isAfter(dayjs(currentMonthStart).subtract(1, "day")) &&
      dayjs(gig.date).isBefore(dayjs(currentMonthEnd).add(1, "day"))
  ).length;
});

const completedGigs = computed(() => {
  const currentMonthStart = dayjs().startOf("month").format("YYYY-MM-DD");
  const currentMonthEnd = dayjs().endOf("month").format("YYYY-MM-DD");
  return allGigs.value.filter(
    (gig) =>
      gig.status === "completed" &&
      dayjs(gig.date).isAfter(dayjs(currentMonthStart).subtract(1, "day")) &&
      dayjs(gig.date).isBefore(dayjs(currentMonthEnd).add(1, "day"))
  ).length;
});

const pendingGigs = computed(() => {
  const currentMonthStart = dayjs().startOf("month").format("YYYY-MM-DD");
  const currentMonthEnd = dayjs().endOf("month").format("YYYY-MM-DD");
  return allGigs.value.filter(
    (gig) =>
      (gig.status === "pending" || gig.status === "confirmed") &&
      dayjs(gig.date).isAfter(dayjs(currentMonthStart).subtract(1, "day")) &&
      dayjs(gig.date).isBefore(dayjs(currentMonthEnd).add(1, "day"))
  ).length;
});

// 方法
const onDateSelect = (dateInfo: any) => {
  selectedDate.value = dateInfo.date;
  activeStatus.value = "all"; // 重置状态筛选
};

const setActiveStatus = (status: string) => {
  activeStatus.value = status;
};

const formatDisplayDate = (date: string) => {
  const today = dayjs().format("YYYY-MM-DD");
  const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD");

  if (date === today) {
    return "今天";
  } else if (date === tomorrow) {
    return "明天";
  } else {
    return dayjs(date).format("MM月DD日");
  }
};

const getStatusText = (status: string) => {
  const statusMap = {
    pending: "待确认",
    confirmed: "已确认",
    completed: "已完成",
  };
  return statusMap[status] || status;
};

const getEmptyTitle = () => {
  if (activeStatus.value === "all") {
    return "暂无零工安排";
  }
  const statusMap = {
    pending: "没有待确认的零工",
    confirmed: "没有已确认的零工",
    completed: "没有已完成的零工",
  };
  return statusMap[activeStatus.value] || "暂无数据";
};

const getEmptyDesc = () => {
  if (activeStatus.value === "all") {
    return "这一天还没有零工安排，去看看其他机会吧";
  }
  return "切换其他状态查看或选择其他日期";
};

const confirmGig = (gigId: number) => {
  const gig = allGigs.value.find((g) => g.id === gigId);
  if (gig) {
    gig.status = "confirmed";
    uni.showToast({
      title: "确认成功",
      icon: "success",
    });
  }
};

const contactEmployer = (gig: any) => {
  uni.showModal({
    title: "联系雇主",
    content: `是否要联系 ${gig.employerName}？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "正在跳转到聊天",
          icon: "none",
        });
      }
    },
  });
};

const viewResult = (gigId: number) => {
  uni.showToast({
    title: "查看工作结果",
    icon: "none",
  });
};

const goToGigDetail = (gigId: number) => {
  uni.navigateTo({
    url: `/pages/gig/detail?id=${gigId}`,
  });
};

const goToGigList = () => {
  uni.navigateTo({
    url: "/pages/gig/index",
  });
};

onMounted(() => {
  // 页面加载时的初始化逻辑
});
</script>

<style lang="scss" scoped>
.gig-calendar-page {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: var(--spacing-20);
}

.stats-section {
  padding: var(--spacing-16);
  margin-top: var(--spacing-10);
}

.stats-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
  flex: 1;

  .stat-number {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-base);
    margin-bottom: var(--spacing-4);
  }

  .stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }
}

.stat-divider {
  width: 1rpx;
  height: var(--spacing-20);
  background-color: var(--border-color);
  margin: 0 var(--spacing-12);
}

.week-selector-section {
  padding: 0 var(--spacing-16) var(--spacing-16);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-10);

  .title-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-base);
  }

  .current-month {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }

  .gig-count {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }
}

.week-calendar-wrapper {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.daily-gigs-section {
  padding: 0 var(--spacing-16);
}

.section-header {
  margin-bottom: var(--spacing-12);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-8);
  margin-top: var(--spacing-8);
}

.filter-tab {
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: var(--radius-base);
  background-color: var(--bg-tag);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: all 0.3s ease;

  &.active {
    background-color: var(--primary);
    color: var(--text-inverse);
  }
}

.gigs-list {
  display: flex;
  flex-direction: column;
}

.gig-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.gig-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.gig-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-8);
}

.gig-title-area {
  flex: 1;
  margin-right: var(--spacing-8);
}

.gig-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-4);
  line-height: 1.4;
}

.gig-status {
  display: inline-block;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);

  &.pending {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.confirmed {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.completed {
    background-color: var(--bg-tag);
    color: var(--text-secondary);
  }
}

.gig-salary {
  text-align: right;

  .salary-amount {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--primary);
  }

  .salary-unit {
    font-size: var(--font-size-xs);
    color: var(--text-info);
    margin-left: var(--spacing-4);
  }
}

.gig-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-12);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.info-icon {
  font-size: var(--font-size-base);
  color: var(--text-info);
  width: var(--font-size-base);
  flex-shrink: 0;
}

.info-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.gig-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-12);
  padding-top: var(--spacing-12);
  border-top: 1rpx solid var(--border-color);
}

.gig-tags {
  display: flex;
  gap: var(--spacing-6);
  flex: 1;
  flex-wrap: wrap;
}

.gig-tag {
  padding: var(--spacing-4) var(--spacing-8);
  background-color: var(--bg-tag);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.gig-actions {
  display: flex;
  gap: var(--spacing-8);
}

.action-btn {
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &.primary {
    background-color: var(--primary);
    color: var(--text-inverse);
  }

  &.secondary {
    background-color: var(--bg-tag);
    color: var(--text-secondary);
  }

  &.success {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &:active {
    transform: scale(0.95);
  }
}

.empty-state {
  text-align: center;
  padding: var(--spacing-40) var(--spacing-20);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  margin-top: var(--spacing-16);
}

.empty-icon {
  font-size: 120rpx;
  color: var(--text-disable);
  margin-bottom: var(--spacing-16);
}

.empty-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-8);
}

.empty-desc {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-info);
  margin-bottom: var(--spacing-20);
  line-height: 1.5;
}

.empty-action {
  display: inline-block;
  padding: var(--spacing-8) var(--spacing-16);
  background-color: var(--primary);
  color: var(--text-inverse);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: var(--primary-700);
  }
}
</style>
