<template>
  <view class="gig-recruiter-page bg-page min-h-screen">
    <!-- 顶部头部 -->
    <view class="header-section bg-white">
      <view class="flex items-center justify-between px-30rpx py-20rpx">
        <view class="header-info">
          <text class="welcome-text text-base font-bold">招聘管理</text>
          <text class="subtitle text-secondary text-sm"
            >发布零工，找到合适的人才</text
          >
        </view>
        <!-- 切换到找零工模式按钮 -->
        <view
          class="mode-switch bg-success-light rounded-full px-24rpx py-12rpx"
          @tap="switchToSeeker"
        >
          <view class="flex items-center">
            <text class="i-carbon-user text-20rpx text-green mr-8rpx"></text>
            <text class="text-green font-semibold text-26rpx">找零工</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 快捷功能入口 -->
    <view class="function-entrance bg-white px-20rpx py-30rpx">
      <view class="grid grid-cols-4 gap-20rpx">
        <view
          v-for="func in recruiterFunctions"
          :key="func.id"
          class="function-item flex flex-col items-center justify-center py-20rpx"
          @tap="navigateToFunction(func.path)"
        >
          <view
            class="icon-wrapper w-88rpx h-88rpx rounded-2xl flex items-center justify-center mb-12rpx"
            :style="{ background: func.gradient }"
          >
            <text :class="func.icon" class="text-44rpx text-white"></text>
          </view>
          <text class="text-26rpx text-secondary font-medium">{{
            func.name
          }}</text>
        </view>
      </view>
    </view>

    <!-- 我的发布列表 -->
    <view class="published-section">
      <view class="section-header">
        <text class="section-title">我的发布</text>
        <view class="section-action" @tap="viewAllPublished">
          <text class="action-text">查看全部</text>
          <text class="i-carbon-chevron-right"></text>
        </view>
      </view>

      <view v-if="recentPublished.length > 0" class="published-list">
        <view
          v-for="gig in recentPublished"
          :key="gig.id"
          class="gig-card-wrapper"
          @tap="navigateToDetail(gig.id)"
        >
          <GigCard :gig="gig" mode="recruiter" :show-status="true" />
        </view>
      </view>

      <view v-else class="empty-published">
        <view class="empty-icon">
          <text class="i-carbon-add-alt"></text>
        </view>
        <text class="empty-title">暂无发布的零工</text>
        <text class="empty-desc">发布零工信息，快速找到合适的工人</text>
        <view class="empty-action" @tap="goToPublish">
          <text>立即发布</text>
        </view>
      </view>
    </view>

    <!-- 最新报名 -->
    <view class="applications-section">
      <view class="section-header">
        <text class="section-title">最新报名</text>
        <view class="section-action" @tap="viewAllApplications">
          <text class="action-text">查看全部</text>
          <text class="i-carbon-chevron-right"></text>
        </view>
      </view>

      <view v-if="recentApplications.length > 0" class="applications-list">
        <view
          v-for="app in recentApplications"
          :key="app.id"
          class="application-item"
          @tap="navigateToApplicants(app.gigId)"
        >
          <view class="app-avatar">
            <image :src="app.avatar" class="avatar" />
          </view>
          <view class="app-info">
            <text class="app-name">{{ app.name }}</text>
            <text class="app-job">报名了《{{ app.jobTitle }}》</text>
          </view>
          <view class="app-time">
            <text>{{ formatTime(app.applyTime) }}</text>
          </view>
        </view>
      </view>

      <view v-else class="empty-applications">
        <text class="empty-text">暂无新的报名</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import GigCard from "./components/GigCard.vue";
import dayjs from "dayjs";

// 统计数据
const publishedCount = ref(3);
const totalApplicants = ref(12);
const pendingCount = ref(5);

// 招人角色的功能入口
const recruiterFunctions = [
  {
    id: "publish_gig",
    name: "发布零工",
    icon: "i-carbon-add-alt",
    path: "/pages/gig/publish",
    gradient: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
  },
  {
    id: "my_posts",
    name: "我的发布",
    icon: "i-carbon-document-tasks",
    path: "/pages/gig/manage",
    gradient: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
  },
  {
    id: "manage_applicants",
    name: "管理应聘",
    icon: "i-carbon-user-multiple",
    path: "/pages/gig/manage",
    gradient: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
  },
  {
    id: "data_analytics",
    name: "数据分析",
    icon: "i-carbon-analytics",
    path: "/pages/gig/analytics",
    gradient: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)",
  },
];

// 最近发布的零工
const recentPublished = ref([
  {
    id: 1,
    title: "周末招聘传单派发员，薪资日结",
    price: "150",
    price_unit: "天",
    tags: ["日结", "学生可", "男女不限"],
    distance: "1.2km",
    location: "朝阳区·三里屯",
    status: "recruiting",
    applicantsCount: 5,
    viewCount: 28,
    employer: {
      name: "李先生",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      verified: true,
    },
  },
  {
    id: 2,
    title: "急招临时搬家师傅，自带车辆优先",
    price: "300",
    price_unit: "次",
    tags: ["急招", "自带车", "体力活"],
    distance: "3.5km",
    location: "海淀区·中关村",
    status: "working",
    applicantsCount: 1,
    viewCount: 15,
    employer: {
      name: "张女士",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      verified: true,
    },
  },
]);

// 最新报名
const recentApplications = ref([
  {
    id: 1,
    name: "小红",
    avatar: "https://randomuser.me/api/portraits/women/45.jpg",
    jobTitle: "周末招聘传单派发员",
    gigId: 1,
    applyTime: dayjs().subtract(2, "hour").toISOString(),
  },
  {
    id: 2,
    name: "小强",
    avatar: "https://randomuser.me/api/portraits/men/46.jpg",
    jobTitle: "急招临时搬家师傅",
    gigId: 2,
    applyTime: dayjs().subtract(1, "day").toISOString(),
  },
  {
    id: 3,
    name: "小美",
    avatar: "https://randomuser.me/api/portraits/women/47.jpg",
    jobTitle: "周末招聘传单派发员",
    gigId: 1,
    applyTime: dayjs().subtract(3, "hour").toISOString(),
  },
]);

// 方法
const switchToSeeker = () => {
  uni.redirectTo({
    url: "/pages/gig/seeker",
  });
};

const navigateToFunction = (path: string) => {
  uni.navigateTo({ url: path });
};

const navigateToDetail = (id: string | number) => {
  uni.navigateTo({ url: `/pages/gig/detail?id=${id}` });
};

const navigateToApplicants = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/applicants?gigId=${gigId}` });
};

const viewAllPublished = () => {
  uni.navigateTo({ url: "/pages/gig/manage" });
};

const viewAllApplications = () => {
  uni.navigateTo({ url: "/pages/gig/manage?tab=applications" });
};

const goToPublish = () => {
  uni.navigateTo({ url: "/pages/gig/publish" });
};

const formatTime = (time: string) => {
  const now = dayjs();
  const targetTime = dayjs(time);
  const diffHours = now.diff(targetTime, "hour");
  const diffDays = now.diff(targetTime, "day");

  if (diffHours < 1) {
    return "刚刚";
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return targetTime.format("MM-DD");
  }
};

onMounted(() => {
  console.log("招人页面加载完成");
});
</script>

<style lang="scss" scoped>
.gig-recruiter-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.header-section {
  background-color: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.welcome-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
}

.subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.mode-switch {
  background-color: var(--bg-success-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6) var(--spacing-12);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.function-entrance {
  padding: var(--spacing-16) var(--spacing-10);
  border-bottom: 1rpx solid var(--border-color);
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-10);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  .icon-wrapper {
    width: 88rpx;
    height: 88rpx;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-sm);
  }

  .text-26rpx {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.published-section,
.applications-section {
  padding: var(--spacing-16);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-16);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
}

.section-action {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.action-text {
  font-size: var(--font-size-sm);
  color: var(--text-info);
}

.published-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.gig-card-wrapper {
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.empty-published,
.empty-applications {
  text-align: center;
  padding: var(--spacing-40) var(--spacing-20);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
}

.empty-icon {
  font-size: 120rpx;
  color: var(--text-grey);
  margin-bottom: var(--spacing-16);
}

.empty-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-8);
}

.empty-desc {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-24);
  line-height: var(--line-height-normal);
}

.empty-action {
  display: inline-block;
  padding: var(--spacing-12) var(--spacing-24);
  background-color: var(--primary);
  color: white;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: var(--primary-700);
  }
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.application-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-16);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.app-avatar {
  margin-right: var(--spacing-12);
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid var(--border-color);
}

.app-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.app-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
}

.app-job {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.app-time {
  font-size: var(--font-size-sm);
  color: var(--text-info);
}

.empty-text {
  font-size: var(--font-size-base);
  color: var(--text-info);
  text-align: center;
  padding: var(--spacing-20);
}
</style>
