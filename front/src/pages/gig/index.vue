<template>
  <view class="gig-square-page bg-page min-h-screen">
    <!-- 顶部搜索和角色切换 -->
    <view class="header-bar sticky top-0 z-10 bg-white shadow-sm">
      <view class="flex items-center justify-between px-30rpx py-16rpx">
        <view class="search-box flex-1 flex items-center bg-search rounded-full px-30rpx py-16rpx mr-20rpx">
          <text class="i-carbon-search mr-10rpx text-info"></text>
          <text class="text-info text-28rpx">搜索任务，如：派传单、搬家</text>
        </view>
        <!-- 角色切换按钮 -->
        <view class="role-switch bg-primary-light rounded-full px-24rpx py-12rpx" @tap="toggleRole">
          <view v-if="!isRoleSwitching" class="flex items-center">
            <text :class="currentRole === 'seeker' ? 'i-carbon-user' : 'i-carbon-user-certification'"
              class="text-20rpx text-primary mr-8rpx"></text>
            <text class="text-primary font-semibold text-26rpx">{{ currentRole === 'seeker' ? '找零工' : '我要招人' }}</text>
          </view>
          <view v-else class="flex items-center">
            <text class="i-carbon-rotate-clockwise animate-spin text-20rpx text-primary mr-8rpx"></text>
            <text class="text-primary font-semibold text-26rpx">切换中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能入口 -->
    <view class="function-entrance bg-white px-20rpx py-30rpx">
      <view class="grid grid-cols-4 gap-20rpx">
        <view v-for="func in currentFunctions" :key="func.id"
          class="function-item flex flex-col items-center justify-center py-20rpx" @tap="navigateToFunction(func.path)">
          <view class="icon-wrapper w-88rpx h-88rpx rounded-2xl flex items-center justify-center mb-12rpx">
            <text :class="func.icon" class="text-44rpx"></text>
          </view>
          <text class="text-26rpx text-secondary font-medium">{{ func.name }}</text>
        </view>
      </view>
    </view>

    <!-- 筛选排序 -->
    <view class="sorting-bar flex items-center bg-white px-30rpx py-20rpx mt-2">
      <view class="flex">
        <view class="sort-item mr-40rpx" @tap="changeSort('nearby')">
          <text :class="currentSort === 'nearby' ? 'text-primary font-bold' : 'text-secondary'">附近工作</text>
          <view v-if="currentSort === 'nearby'" class="sort-indicator bg-primary rounded-full"></view>
        </view>
        <view class="sort-item" @tap="changeSort('latest')">
          <text :class="currentSort === 'latest' ? 'text-primary font-bold' : 'text-secondary'">最新发布</text>
          <view v-if="currentSort === 'latest'" class="sort-indicator bg-primary rounded-full"></view>
        </view>
      </view>
    </view>

    <!-- 零工列表 -->
    <view class="gig-list px-30rpx py-30rpx">
      <GigCard v-for="gig in gigs" :key="gig.id" :gig="gig" class="mb-30rpx" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import GigCard from './components/GigCard.vue';

// --- 响应式数据 ---
const currentRole = ref('seeker'); // 'seeker' | 'recruiter'
const isRoleSwitching = ref(false);
const currentSort = ref('nearby');

// 找零工角色的功能入口
const seekerFunctions = [
  {
    id: 'my_applications',
    name: '我的报名',
    icon: 'i-carbon-document-signed',
    path: '/pages/gig/manage?tab=applied',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    id: 'work_calendar',
    name: '干活日历',
    icon: 'i-carbon-calendar',
    path: '/pages/gig/calendar',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    id: 'my_favorites',
    name: '我的收藏',
    icon: 'i-carbon-favorite',
    path: '/pages/mine/collections',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    id: 'skills_certificate',
    name: '技能认证',
    icon: 'i-carbon-certificate',
    path: '/pages/gig/skills-certificate',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  },
];

// 招人角色的功能入口
const recruiterFunctions = [
  {
    id: 'my_posts',
    name: '我的发布',
    icon: 'i-carbon-document-tasks',
    path: '/pages/gig/manage',
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  },
  {
    id: 'publish_gig',
    name: '发布零工',
    icon: 'i-carbon-add-alt',
    path: '/pages/gig/publish',
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
  },
  {
    id: 'verification',
    name: '认证',
    icon: 'i-carbon-badge',
    path: '/pages/mine/profile',
    gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
  },
  {
    id: 'manage_applicants',
    name: '管理应聘',
    icon: 'i-carbon-user-multiple',
    path: '/pages/gig/applicants',
    gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
  },
];

// 计算当前角色对应的功能入口
const currentFunctions = computed(() => {
  return currentRole.value === 'seeker' ? seekerFunctions : recruiterFunctions;
});

// 模拟数据 - 根据角色显示不同的列表数据
const gigs = ref([
  {
    id: 1,
    title: '周末招聘传单派发员，薪资日结',
    price: '150',
    price_unit: '天',
    tags: ['日结', '学生可', '男女不限'],
    distance: '1.2km',
    location: '朝阳区·三里屯',
    employer: {
      name: '李先生',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      verified: true,
    },
  },
  {
    id: 2,
    title: '急招临时搬家师傅，自带车辆优先',
    price: '300',
    price_unit: '次',
    tags: ['急招', '自带车', '体力活'],
    distance: '3.5km',
    location: '海淀区·中关村',
    employer: {
      name: '张女士',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      verified: true,
    },
  },
  {
    id: 3,
    title: '家庭日常保洁，两室一厅',
    price: '40',
    price_unit: '小时',
    tags: ['长期稳定', '女性优先'],
    distance: '800m',
    location: '西城区·金融街',
    employer: {
      name: '王阿姨',
      avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
      verified: false,
    },
  },
]);

// --- 方法 ---
const toggleRole = async () => {
  if (isRoleSwitching.value) return;

  isRoleSwitching.value = true;

  // 模拟切换加载
  setTimeout(() => {
    currentRole.value = currentRole.value === 'seeker' ? 'recruiter' : 'seeker';
    isRoleSwitching.value = false;

    // TODO: 根据角色重新加载数据
    console.log(`Role switched to: ${currentRole.value}`);
  }, 1200);
};

const changeSort = (type: string) => {
  currentSort.value = type;
  // TODO: 根据排序方式重新加载数据
  console.log(`Sort changed to: ${type}`);
};

const navigateToFunction = (path: string) => {
  uni.navigateTo({ url: path });
};
</script>

<style lang="scss" scoped>
.gig-square-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.header-bar {
  background-color: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-box {
  background-color: var(--bg-search);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8) var(--spacing-16);
}

.role-switch {
  background-color: var(--bg-tag);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6) var(--spacing-12);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.function-entrance {
  background-color: var(--bg-card);
  padding: var(--spacing-16) var(--spacing-10);
  margin-bottom: var(--spacing-12);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-10);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  .icon-wrapper {
    width: 88rpx;
    height: 88rpx;
    border-radius: var(--radius-lg);
    background-color: var(--bg-tag);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }

  .text-26rpx {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.sorting-bar {
  background-color: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
  padding: var(--spacing-10) var(--spacing-16);
  margin-top: var(--spacing-10);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sort-item {
  position: relative;
  padding-bottom: var(--spacing-4);
  margin-right: var(--spacing-20);

  .text-primary {
    color: var(--primary);
  }

  .text-secondary {
    color: var(--text-secondary);
  }

  .sort-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24rpx;
    height: 4rpx;
    background-color: var(--primary);
    border-radius: 2rpx;
  }
}

.gig-list {
  padding: var(--spacing-16);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-16);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
