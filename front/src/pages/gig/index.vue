<template>
  <view class="gig-index-page">
    <!-- 加载状态 -->
    <view class="loading-wrapper">
      <view class="loading-content">
        <view class="loading-icon">
          <text class="i-carbon-rotate-clockwise animate-spin"></text>
        </view>
        <text class="loading-text">正在进入零工广场...</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from "vue";

onMounted(() => {
  // 直接跳转到找零工页面
  setTimeout(() => {
    uni.redirectTo({
      url: "/pages/gig/seeker",
    });
  }, 500);
});
</script>

<style lang="scss" scoped>
.gig-index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-16);
}

.loading-icon {
  font-size: 80rpx;
  color: white;
}

.loading-text {
  font-size: var(--font-size-base);
  color: white;
  opacity: 0.9;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
