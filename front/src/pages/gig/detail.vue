<template>
  <view class="gig-detail-page">
    <scroll-view scroll-y class="detail-scroll-view">
      <!-- 核心信息 -->
      <view class="card main-info-card">
        <text class="gig-title">{{ gig.title }}</text>
        <view class="gig-price">
          <text class="price-value">{{ gig.price }}</text>
          <text class="price-unit">元/{{ gig.price_unit }}</text>
        </view>
        <view class="gig-tags">
          <view v-for="(tag, index) in gig.tags" :key="index" class="tag">
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="card detail-info-card">
        <view class="detail-item">
          <text class="i-carbon-calendar detail-icon"></text>
          <view>
            <text class="item-label">时间</text>
            <text class="item-value">{{ gig.time }}</text>
          </view>
        </view>
        <view class="detail-item">
          <text class="i-carbon-location detail-icon"></text>
          <view>
            <text class="item-label">地点</text>
            <text class="item-value">{{ gig.location_full }}</text>
          </view>
        </view>
        <view class="detail-item">
          <text class="i-carbon-group-account detail-icon"></text>
          <view>
            <text class="item-label">需要人数</text>
            <text class="item-value">{{ gig.headcount }}</text>
          </view>
        </view>
      </view>

      <!-- 任务描述 -->
      <view class="card description-card">
        <text class="card-title">任务描述</text>
        <text class="description-text">{{ gig.description }}</text>
      </view>

      <!-- 雇主信息 -->
      <view class="card employer-card">
        <view class="employer-content">
          <image :src="gig.employer.avatar" class="employer-avatar" />
          <view class="employer-details">
            <text class="employer-name">{{ gig.employer.name }}</text>
            <view class="employer-stats">
              <text class="stats-text"
                >已发布 {{ gig.employer.gig_count }} 个任务</text
              >
              <text
                v-if="gig.employer.verified"
                class="i-carbon-badge-filled verified-icon"
              ></text>
            </view>
          </view>
        </view>
        <text class="i-carbon-chevron-right arrow-icon"></text>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-item">
        <text class="i-carbon-star action-icon"></text>
        <text class="action-text">收藏</text>
      </view>
      <view class="action-item">
        <text class="i-carbon-chat action-icon"></text>
        <text class="action-text">沟通</text>
      </view>
      <button class="apply-button">立即报名</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// --- 模拟数据 ---
const gig = ref({
  id: 0,
  title: "",
  price: "",
  price_unit: "",
  tags: [],
  time: "",
  location_full: "",
  headcount: "",
  description: "",
  employer: {
    name: "",
    avatar: "",
    verified: false,
    gig_count: 0,
  },
});

onMounted(() => {
  // 在实际应用中，这里会根据页面参数(id)从API获取数据
  // const options = getCurrentPages().pop().options;
  // const gigId = options.id;
  gig.value = {
    id: 1,
    title: "周末招聘传单派发员，薪资日结",
    price: "150",
    price_unit: "天",
    tags: ["日结", "学生可", "男女不限"],
    time: "2025年7月5日-7月6日 10:00-18:00",
    location_full: "北京市朝阳区三里屯太古里南区 (距您1.2km)",
    headcount: "5人，还剩3人",
    description:
      "负责在商场门口派发宣传单，引导顾客进店咨询。要求积极主动，有责任心，不害羞。工作轻松，氛围好。" +
      "\n\n工作要求：\n1. 年龄18-30岁，男女不限。\n2. 有良好的沟通能力。\n3. 服从现场安排。",
    employer: {
      name: "李先生",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      verified: true,
      gig_count: 5,
    },
  };
});
</script>

<style lang="scss" scoped>
.gig-detail-page {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: var(--spacing-40); /* Space for bottom bar */
}

.detail-scroll-view {
  height: calc(100vh - 160rpx); /* Adjust height for scrollable area */
}

.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  margin: var(--spacing-12) var(--spacing-16);
  padding: var(--spacing-16);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
}

.gig-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-10);
  line-height: 1.3;
}

.gig-price {
  margin-bottom: var(--spacing-16);
}

.price-value {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--primary);
}

.price-unit {
  font-size: var(--font-size-base);
  color: var(--primary);
  margin-left: var(--spacing-4);
}

.gig-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-8);
}

.tag {
  background-color: var(--bg-info-light);
  color: var(--text-blue);
  font-size: var(--font-size-sm);
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: var(--radius-base);
}

.detail-info-card {
  .detail-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-16);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-icon {
    font-size: var(--font-size-lg);
    color: var(--text-blue);
    margin-right: var(--spacing-12);
    flex-shrink: 0;
  }

  .item-label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-base);
    margin-bottom: var(--spacing-4);
    display: block;
  }

  .item-value {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.5;
  }
}

.description-card {
  .card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-base);
    margin-bottom: var(--spacing-12);
  }

  .description-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.8;
  }
}

.employer-card {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .employer-content {
    display: flex;
    align-items: center;
  }

  .employer-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    margin-right: var(--spacing-12);
    border: 2rpx solid var(--border-color);
  }

  .employer-name {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-bold);
    color: var(--text-base);
    margin-bottom: var(--spacing-4);
  }

  .employer-stats {
    display: flex;
    align-items: center;
  }

  .stats-text {
    font-size: var(--font-size-sm);
    color: var(--text-info);
  }

  .verified-icon {
    color: var(--text-blue);
    margin-left: var(--spacing-6);
    font-size: var(--font-size-base);
  }

  .arrow-icon {
    font-size: var(--font-size-lg);
    color: var(--text-info);
  }
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  padding: var(--spacing-12) var(--spacing-16);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 var(--spacing-10);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-icon {
  font-size: var(--font-size-xxl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
}

.action-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.apply-button {
  flex: 1;
  background-color: var(--primary);
  color: var(--text-inverse);
  height: 96rpx;
  border-radius: 48rpx;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--spacing-20);
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.8;
  }
}
</style>
