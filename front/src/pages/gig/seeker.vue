<template>
  <view class="gig-seeker-page bg-page min-h-screen">
    <!-- 使用z-paging实现高性能列表 -->
    <z-paging
      ref="pagingRef"
      v-model="gigList"
      :safe-area-inset-bottom="true"
      :refresher-enabled="true"
      :auto="true"
      :default-page-size="10"
      @query="queryGigList"
      @scroll="onPageScroll"
    >
      <template #top>
        <uni-nav-bar
          title="零工广场"
          :fixed="true"
          status-bar
          :border="false"
        />
        <!-- 顶部搜索和切换按钮 -->
        <view class="header-bar bg-white">
          <view class="flex items-center justify-between px-30rpx py-16rpx">
            <view
              class="search-box flex-1 flex items-center bg-search rounded-full px-30rpx py-16rpx mr-20rpx"
            >
              <text class="i-carbon-search mr-10rpx text-info"></text>
              <input
                v-model="searchKeyword"
                class="flex-1 text-28rpx text-base bg-transparent"
                placeholder="搜索任务，如：派传单、搬家"
                placeholder-class="text-info"
                confirm-type="search"
                @confirm="handleSearch"
                @input="onSearchInput"
                @focus="isSearching = true"
              />
              <text
                v-if="searchKeyword"
                class="i-carbon-close-filled text-grey ml-10rpx"
                @tap="clearSearch"
              ></text>
            </view>
            <!-- 切换到招人模式按钮 -->
            <view
              class="mode-switch bg-primary-light rounded-full px-24rpx py-12rpx"
              @tap="switchToRecruiter"
            >
              <view class="flex items-center">
                <text
                  class="i-carbon-user-certification text-20rpx text-primary mr-8rpx"
                ></text>
                <text class="text-primary font-semibold text-26rpx"
                  >我要招人</text
                >
              </view>
            </view>
          </view>

          <!-- 搜索建议和历史 -->
          <view v-if="isSearching" class="search-panel">
            <!-- 搜索建议 -->
            <view
              v-if="searchSuggestions.length > 0"
              class="search-suggestions"
            >
              <view
                v-for="(suggestion, index) in searchSuggestions"
                :key="'suggestion-' + index"
                class="suggestion-item"
                @tap="selectSuggestion(suggestion)"
              >
                <text class="i-carbon-search text-grey mr-16rpx"></text>
                <text>{{ suggestion }}</text>
              </view>
            </view>

            <!-- 搜索历史 -->
            <view v-else-if="searchHistory.length > 0" class="search-history">
              <view class="history-header">
                <text class="history-title">搜索历史</text>
                <text class="clear-history" @tap="clearSearchHistory"
                  >清空</text
                >
              </view>
              <view class="history-list">
                <view
                  v-for="(history, index) in searchHistory"
                  :key="'history-' + index"
                  class="history-item"
                  @tap="selectHistory(history)"
                >
                  <text class="i-carbon-time text-grey mr-16rpx"></text>
                  <text>{{ history }}</text>
                </view>
              </view>
            </view>

            <!-- 无搜索历史提示 -->
            <view v-else class="no-history">
              <text class="no-history-text">暂无搜索历史</text>
            </view>

            <!-- 关闭搜索面板按钮 -->
            <view class="close-search" @tap="isSearching = false">
              <text>关闭</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 找零工功能入口 -->
      <view class="function-entrance bg-white px-20rpx py-30rpx">
        <view class="grid grid-cols-3 gap-20rpx">
          <view
            v-for="func in seekerFunctions"
            :key="func.id"
            class="function-item flex flex-col items-center justify-center py-20rpx"
            @tap="navigateToFunction(func.path)"
          >
            <view
              class="icon-wrapper w-88rpx h-88rpx rounded-2xl flex items-center justify-center mb-12rpx"
              :style="{ background: func.gradient }"
            >
              <text :class="func.icon" class="text-44rpx text-white"></text>
            </view>
            <text class="text-26rpx text-secondary font-medium">{{
              func.name
            }}</text>
          </view>
        </view>
      </view>

      <!-- 筛选排序 -->
      <view class="tabs-wrapper box-shadow sticky top-0">
        <tui-tabs
          :tabs="sortTabs"
          :currentTab="sortTabIndex"
          sliderBgColor="var(--primary)"
          selectedColor="var(--primary)"
          color="var(--text-secondary)"
          :bold="true"
          :height="80"
          :sliderWidth="48"
          :sliderHeight="6"
          backgroundColor="var(--bg-card)"
          @change="onTabChange"
          :isFixed="false"
        ></tui-tabs>
      </view>

      <!-- 零工列表 -->
      <view
        v-for="gig in gigList"
        :key="gig.id"
        class="gig-card-wrapper"
        @tap="navigateToDetail(gig.id)"
      >
        <GigCard :gig="gig" mode="seeker" />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";
import GigCard from "./components/GigCard.vue";
import tuiTabs from "@/components/thorui/tui-tabs/tui-tabs.vue";

// z-paging引用
const pagingRef = ref(null);

// --- 响应式数据 ---
const currentSort = ref("nearby");
// 搜索相关
const searchKeyword = ref("");
const isSearching = ref(false);
const searchHistory = ref<string[]>([]);
const searchSuggestions = ref<string[]>([]);

// 排序选项卡数据
const sortTabs = ref([
  { name: "附近工作", value: "nearby" },
  { name: "最新发布", value: "latest" },
]);

// 当前选中的选项卡索引
const sortTabIndex = computed(() => {
  return currentSort.value === "nearby" ? 0 : 1;
});

// 零工列表数据
const gigList = ref([]);

// 找零工角色的功能入口
const seekerFunctions = [
  {
    id: "my_applications",
    name: "我的报名",
    icon: "i-carbon-document-signed",
    path: "/pages/gig/my-applications",
    gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  },
  {
    id: "work_calendar",
    name: "干活日历",
    icon: "i-carbon-calendar",
    path: "/pages/gig/calendar",
    gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
  },
  {
    id: "my_favorites",
    name: "我的收藏",
    icon: "i-carbon-favorite",
    path: "/pages/mine/collections",
    gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
  },
];

// 模拟零工数据
const mockGigData = [
  {
    id: 1,
    title: "周末招聘传单派发员，薪资日结",
    price: "150",
    price_unit: "天",
    tags: ["日结", "学生可", "男女不限"],
    distance: "1.2km",
    location: "朝阳区·三里屯",
    employer: {
      name: "李先生",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      verified: true,
    },
  },
  {
    id: 2,
    title: "急招临时搬家师傅，自带车辆优先",
    price: "300",
    price_unit: "次",
    tags: ["急招", "自带车", "体力活"],
    distance: "3.5km",
    location: "海淀区·中关村",
    employer: {
      name: "张女士",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      verified: true,
    },
  },
  {
    id: 3,
    title: "家庭日常保洁，两室一厅",
    price: "40",
    price_unit: "小时",
    tags: ["长期稳定", "女性优先"],
    distance: "800m",
    location: "西城区·金融街",
    employer: {
      name: "王阿姨",
      avatar: "https://randomuser.me/api/portraits/women/3.jpg",
      verified: false,
    },
  },
];

// --- 方法 ---
// z-paging查询数据方法
const queryGigList = (pageNo, pageSize) => {
  console.log("查询零工数据:", pageNo, pageSize);

  // 模拟搜索和筛选逻辑
  let filteredData = [...mockGigData];

  // 如果有搜索关键词，进行筛选
  if (searchKeyword.value) {
    filteredData = filteredData.filter(
      (item) =>
        item.title.includes(searchKeyword.value) ||
        item.location.includes(searchKeyword.value)
    );
  }

  // 根据排序方式排序
  if (currentSort.value === "nearby") {
    filteredData.sort((a, b) => {
      const distanceA = parseFloat(a.distance);
      const distanceB = parseFloat(b.distance);
      return distanceA - distanceB;
    });
  }

  const pageData = pageNo === 1 ? filteredData : [];

  setTimeout(() => {
    try {
      pagingRef.value.complete(pageData);
    } catch (e) {
      console.error("z-paging错误:", e);
      uni.showToast({
        title: "加载数据失败",
        icon: "none",
      });
    }
  }, 500);
};

// 页面滚动时关闭搜索面板
const onPageScroll = () => {
  if (isSearching.value) {
    isSearching.value = false;
  }
};

// 处理搜索输入
const onSearchInput = (e: any) => {
  const value = e.detail.value;
  if (!value) {
    searchSuggestions.value = [];
    return;
  }

  setTimeout(() => {
    searchSuggestions.value = [
      `${value}相关工作`,
      `${value}兼职`,
      `${value}全职`,
      `${value}日结`,
    ].slice(0, 3);
  }, 300);
};

// 处理搜索确认
const handleSearch = () => {
  if (!searchKeyword.value) {
    uni.showToast({
      title: "请输入搜索关键词",
      icon: "none",
    });
    return;
  }

  isSearching.value = false;

  // 保存搜索历史
  if (!searchHistory.value.includes(searchKeyword.value)) {
    searchHistory.value.unshift(searchKeyword.value);
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10);
    }
    try {
      uni.setStorageSync(
        "gigSearchHistory",
        JSON.stringify(searchHistory.value)
      );
    } catch (e) {
      console.error("保存搜索历史失败", e);
    }
  }

  searchSuggestions.value = [];
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = "";
  isSearching.value = false;
  searchSuggestions.value = [];
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

// 选择搜索建议
const selectSuggestion = (suggestion: string) => {
  searchKeyword.value = suggestion;
  handleSearch();
};

// 选择搜索历史
const selectHistory = (history: string) => {
  searchKeyword.value = history;
  handleSearch();
};

// 清空搜索历史
const clearSearchHistory = () => {
  searchHistory.value = [];
  try {
    uni.removeStorageSync("gigSearchHistory");
  } catch (e) {
    console.error("清空搜索历史失败", e);
  }
};

// 切换到招人模式
const switchToRecruiter = () => {
  uni.redirectTo({
    url: "/pages/gig/recruiter",
  });
};

// 选项卡切换事件处理
const onTabChange = (e: { index: number; item: any }) => {
  const sortType = e.item.value;
  currentSort.value = sortType;

  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

const navigateToFunction = (path: string) => {
  uni.navigateTo({ url: path });
};

const navigateToDetail = (id: string | number) => {
  uni.navigateTo({ url: `/pages/gig/detail?id=${id}` });
};

onMounted(() => {
  // 加载搜索历史
  try {
    const historyStr = uni.getStorageSync("gigSearchHistory");
    if (historyStr) {
      searchHistory.value = JSON.parse(historyStr);
    }
  } catch (e) {
    console.error("加载搜索历史失败", e);
  }

  nextTick(() => {
    if (pagingRef.value) {
      gigList.value = [...mockGigData];
    }
  });
});
</script>

<style lang="scss" scoped>
.gig-seeker-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.header-bar {
  background-color: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.search-box {
  background-color: var(--bg-search);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8) var(--spacing-16);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.mode-switch {
  background-color: var(--bg-primary-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6) var(--spacing-12);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.function-entrance {
  padding: var(--spacing-16) var(--spacing-10);
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-10);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  .icon-wrapper {
    width: 88rpx;
    height: 88rpx;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-sm);
  }

  .text-26rpx {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.tabs-wrapper {
  background-color: #ffffff;
  padding: 0 var(--spacing-10);
  margin-bottom: var(--spacing-10);
}

.gig-card-wrapper {
  margin: 20rpx;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 搜索面板样式 */
.search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  z-index: 100;
  padding: var(--spacing-16);
  box-shadow: var(--shadow-md);
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.suggestion-item,
.history-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-12) var(--spacing-8);
  font-size: var(--font-size-base);
  color: var(--text-base);

  &:active {
    background-color: var(--bg-hover);
  }
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-8) var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.history-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.clear-history {
  font-size: var(--font-size-sm);
  color: var(--text-info);
  padding: var(--spacing-4) var(--spacing-8);
}

.no-history {
  padding: var(--spacing-24) 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-history-text {
  font-size: var(--font-size-sm);
  color: var(--text-grey);
}

.close-search {
  margin-top: var(--spacing-16);
  padding: var(--spacing-12) 0;
  text-align: center;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  border-top: 1rpx solid var(--border-color);

  &:active {
    background-color: var(--bg-hover);
  }
}
</style>
