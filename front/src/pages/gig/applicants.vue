<template>
  <view class="applicants-page bg-page min-h-screen">
    <CustomNavBar title="报名管理" />
    <view class="p-30rpx">
      <view v-if="applicants.length > 0" class="applicants-list">
        <view v-for="applicant in applicants" :key="applicant.id"
          class="applicant-item card bg-white rounded-lg shadow-sm mb-20rpx p-20rpx flex justify-between items-center">
          <view class="flex items-center" @tap="goToApplicantProfile(applicant.id)">
            <image :src="applicant.avatar" class="avatar w-100rpx h-100rpx rounded-full mr-20rpx" />
            <view>
              <text class="name font-semibold text-base">{{ applicant.name }}</text>
              <view class="rating flex items-center mt-4rpx">
                <text class="status-tag" :class="applicant.status">{{ getStatusText(applicant.status) }}</text>
              </view>
            </view>
          </view>
          <view class="actions flex items-center">
            <template v-if="applicant.status === 'pending'">
              <button class="action-btn text-sm bg-red-light text-red mr-20rpx"
                @click="updateApplicantStatus(applicant.id, 'rejected')">拒绝</button>
              <button class="action-btn text-sm bg-primary-light text-primary"
                @click="updateApplicantStatus(applicant.id, 'hired')">录用</button>
            </template>
            <template v-else-if="applicant.status === 'hired'">
              <button class="action-btn text-sm" @click="updateApplicantStatus(applicant.id, 'pending')">取消录用</button>
            </template>
            <template v-else-if="applicant.status === 'rejected'">
              <text class="text-info text-sm">已拒绝</text>
            </template>
          </view>
        </view>
      </view>
      <view v-else class="empty-state text-center py-80rpx">
        <text class="text-info">暂无报名</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

interface Applicant {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  status: 'pending' | 'hired' | 'rejected';
}

const gigId = ref<number | null>(null);
const applicants = ref<Applicant[]>([]);

const mockApplicants: Applicant[] = [
  { id: 201, name: '小红', avatar: 'https://randomuser.me/api/portraits/women/45.jpg', rating: 4.9, status: 'pending' },
  { id: 202, name: '小强', avatar: 'https://randomuser.me/api/portraits/men/46.jpg', rating: 4.8, status: 'hired' },
  { id: 203, name: '小美', avatar: 'https://randomuser.me/api/portraits/women/47.jpg', rating: 5.0, status: 'pending' },
  { id: 204, name: '大壮', avatar: 'https://randomuser.me/api/portraits/men/48.jpg', rating: 4.7, status: 'rejected' },
];

onLoad((options) => {
  if (options && options.gigId) {
    gigId.value = parseInt(options.gigId, 10);
    // TODO: Fetch applicants for the gig with gigId from API
    // For now, use mock data
    applicants.value = mockApplicants;
  }
});

const getStatusText = (status: Applicant['status']) => {
  const map = {
    pending: '待定',
    hired: '已录用',
    rejected: '已拒绝'
  };
  return map[status];
};

const updateApplicantStatus = (applicantId: number, newStatus: Applicant['status']) => {
  const applicant = applicants.value.find(a => a.id === applicantId);
  if (applicant) {
    // If hiring someone, check if someone else is already hired
    if (newStatus === 'hired') {
      const currentlyHired = applicants.value.find(a => a.status === 'hired');
      if (currentlyHired) {
        uni.showToast({ title: '已录用他人，请先取消录用', icon: 'none' });
        return;
      }
    }
    applicant.status = newStatus;
    console.log(`Updated applicant ${applicantId} to status ${newStatus} for gig ${gigId.value}`);
    // TODO: Call API to update status
  }
};

const goToApplicantProfile = (applicantId: number) => {
  uni.navigateTo({ url: `/pages/gig/profile?id=${applicantId}` });
};
</script>

<style lang="scss" scoped>
.applicants-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.applicants-list {
  display: grid;
  gap: var(--spacing-12);
}

.applicant-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: var(--spacing-12);
  border: 2rpx solid var(--border-color);
  flex-shrink: 0;
}

.name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
}

.status-tag {
  font-size: var(--font-size-sm);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  margin-top: var(--spacing-4);
  display: inline-block;

  &.pending {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.hired {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.rejected {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }
}

.actions {
  display: flex;
  gap: var(--spacing-8);
}

.action-btn {
  padding: var(--spacing-8) var(--spacing-12);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &.bg-red-light {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &.bg-primary-light {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &:active {
    transform: scale(0.95);
  }

  &::after {
    border: none;
  }
}

.empty-state {
  text-align: center;
  padding: var(--spacing-40) var(--spacing-20);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  margin-top: var(--spacing-16);

  .text-info {
    font-size: var(--font-size-base);
    color: var(--text-info);
  }
}
</style>
