<template>
  <view class="applicants-page bg-page min-h-screen">
    <CustomNavBar title="报名管理" />
    <view class="p-30rpx">
      <view v-if="applicants.length > 0" class="applicants-list">
        <view
          v-for="applicant in applicants"
          :key="applicant.id"
          class="applicant-item card bg-white rounded-lg shadow-sm mb-20rpx p-20rpx"
        >
          <view
            class="flex items-center justify-between mb-12rpx"
            @tap="goToApplicantProfile(applicant.id)"
          >
            <view>
              <view class="flex items-center">
                <image
                  :src="applicant.avatar"
                  class="avatar w-80rpx h-80rpx rounded-full mr-16rpx"
                />
                <view>
                  <text class="name font-semibold text-base">{{
                    applicant.name
                  }}</text>
                  <view class="rating flex items-center mt-4rpx">
                    <text
                      class="i-carbon-star-filled text-yellow mr-4rpx text-sm"
                    ></text>
                    <text class="rating-text text-sm">{{
                      applicant.rating
                    }}</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="status-badge" :class="applicant.status">
              <text>{{ getStatusText(applicant.status) }}</text>
            </view>
          </view>

          <view class="details mb-16rpx">
            <view class="detail-row">
              <text class="i-carbon-phone detail-icon"></text>
              <text class="detail-text">{{ applicant.phone }}</text>
              <text class="i-carbon-time detail-icon ml-24rpx"></text>
              <text class="detail-text">{{
                formatApplyTime(applicant.applyTime)
              }}</text>
            </view>
            <view v-if="applicant.message" class="message-row">
              <text class="i-carbon-chat detail-icon"></text>
              <text class="message-text">{{ applicant.message }}</text>
            </view>
          </view>

          <view class="actions flex items-center justify-end">
            <template v-if="applicant.status === 'pending'">
              <button
                class="action-btn text-sm bg-red-light text-red mr-20rpx"
                @click="updateApplicantStatus(applicant.id, 'rejected')"
              >
                拒绝
              </button>
              <button
                class="action-btn text-sm bg-primary-light text-primary"
                @click="updateApplicantStatus(applicant.id, 'hired')"
              >
                录用
              </button>
            </template>
            <template v-else-if="applicant.status === 'hired'">
              <button
                class="action-btn text-sm bg-info-light text-blue mr-20rpx"
                @click="contactApplicant(applicant.id)"
              >
                联系Ta
              </button>
              <button
                class="action-btn text-sm bg-grey-light text-grey"
                @click="updateApplicantStatus(applicant.id, 'pending')"
              >
                取消录用
              </button>
            </template>
            <template v-else-if="applicant.status === 'rejected'">
              <text class="text-info text-sm">已拒绝</text>
            </template>
          </view>
        </view>
      </view>
      <view v-else class="empty-state text-center py-80rpx">
        <text class="text-info">暂无报名</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";

interface Applicant {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  status: "pending" | "hired" | "rejected";
  phone: string;
  applyTime: string;
  message?: string;
}

const gigId = ref<number | null>(null);
const applicants = ref<Applicant[]>([]);

const mockApplicants: Applicant[] = [
  {
    id: 1,
    name: "小红",
    avatar: "https://randomuser.me/api/portraits/women/1.jpg",
    rating: 4.9,
    status: "pending",
    phone: "138****1234",
    applyTime: "2024-03-15 09:30",
    message: "我有相关经验，能胜任这份工作",
  },
  {
    id: 2,
    name: "小强",
    avatar: "https://randomuser.me/api/portraits/men/2.jpg",
    rating: 4.8,
    status: "hired",
    phone: "139****5678",
    applyTime: "2024-03-14 16:20",
    message: "有搬家经验，自带面包车",
  },
  {
    id: 3,
    name: "小美",
    avatar: "https://randomuser.me/api/portraits/women/3.jpg",
    rating: 5.0,
    status: "pending",
    phone: "136****9876",
    applyTime: "2024-03-14 14:15",
    message: "时间灵活，可以长期合作",
  },
  {
    id: 4,
    name: "小明",
    avatar: "https://randomuser.me/api/portraits/men/4.jpg",
    rating: 4.7,
    status: "rejected",
    phone: "137****4321",
    applyTime: "2024-03-13 11:45",
    message: "希望能有机会尝试这份工作",
  },
];

onLoad((options) => {
  if (options && options.gigId) {
    gigId.value = parseInt(options.gigId, 10);
    // TODO: Fetch applicants for the gig with gigId from API
    // For now, use mock data
    applicants.value = mockApplicants;
  }
});

const getStatusText = (status: Applicant["status"]) => {
  const map = {
    pending: "待定",
    hired: "已录用",
    rejected: "已拒绝",
  };
  return map[status];
};

const updateApplicantStatus = (
  applicantId: number,
  newStatus: Applicant["status"]
) => {
  const applicant = applicants.value.find((a) => a.id === applicantId);
  if (applicant) {
    // If hiring someone, check if someone else is already hired
    if (newStatus === "hired") {
      const currentlyHired = applicants.value.find((a) => a.status === "hired");
      if (currentlyHired) {
        uni.showToast({ title: "已录用他人，请先取消录用", icon: "none" });
        return;
      }
    }
    applicant.status = newStatus;
    console.log(
      `Updated applicant ${applicantId} to status ${newStatus} for gig ${gigId.value}`
    );
    // TODO: Call API to update status
  }
};

const goToApplicantProfile = (applicantId: number) => {
  uni.navigateTo({ url: `/pages/gig/profile?id=${applicantId}` });
};

const formatApplyTime = (applyTime: string) => {
  const date = new Date(applyTime);
  return date.toLocaleString();
};

const contactApplicant = (applicantId: number) => {
  // Implement the logic to contact the applicant
  console.log(`Contacting applicant ${applicantId}`);
};
</script>

<style lang="scss" scoped>
.applicants-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.applicants-list {
  display: grid;
  gap: var(--spacing-12);
}

.applicant-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: var(--spacing-12);
  border: 2rpx solid var(--border-color);
  flex-shrink: 0;
}

.name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
}

.rating-text {
  font-size: var(--font-size-sm);
  color: var(--text-base);
  font-weight: var(--font-weight-medium);
}

.status-badge {
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-left: var(--spacing-8);

  &.pending {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.hired {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.rejected {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }
}

.actions {
  display: flex;
  gap: var(--spacing-8);
}

.action-btn {
  padding: var(--spacing-8) var(--spacing-12);
  border-radius: var(--radius-base);
  border: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &.bg-red-light {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &.bg-primary-light {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &.bg-info-light {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &.bg-grey-light {
    background-color: var(--bg-grey-light);
    color: var(--text-grey);
  }

  &:active {
    transform: scale(0.95);
  }
}

.empty-state {
  text-align: center;
  padding: var(--spacing-40) var(--spacing-20);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 var(--spacing-4) var(--spacing-16) rgba(0, 0, 0, 0.08);
  margin-top: var(--spacing-16);

  .text-info {
    font-size: var(--font-size-base);
    color: var(--text-info);
  }
}

.details {
  margin-bottom: var(--spacing-16);
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-8);
}

.detail-icon {
  margin-right: var(--spacing-8);
}

.detail-text {
  font-size: var(--font-size-base);
  color: var(--text-base);
}

.message-row {
  margin-top: var(--spacing-8);
}

.message-text {
  font-size: var(--font-size-base);
  color: var(--text-base);
}
</style>
