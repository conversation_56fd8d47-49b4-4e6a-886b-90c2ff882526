<template>
  <view class="about-page">
    <CustomNavBar title="关于我们" />
    
    <view class="content">
      <!-- App信息 -->
      <view class="app-info">
        <view class="app-logo">
          <image src="/static/logo.png" mode="aspectFit" />
        </view>
        <text class="app-name">本地宝</text>
        <text class="app-version">v1.0.0</text>
        <text class="app-desc">本地生活服务平台，连接你我他</text>
      </view>

      <!-- 功能介绍 -->
      <view class="features-section">
        <view class="section-title">核心功能</view>
        <view class="features-list">
          <view class="feature-item">
            <tui-icon name="briefcase" :size="48" color="#2196F3"></tui-icon>
            <view class="feature-content">
              <text class="feature-title">求职招聘</text>
              <text class="feature-desc">为求职者和招聘方提供高效匹配服务</text>
            </view>
          </view>
          
          <view class="feature-item">
            <tui-icon name="home" :size="48" color="#4CAF50"></tui-icon>
            <view class="feature-content">
              <text class="feature-title">房产服务</text>
              <text class="feature-desc">租房、二手房、新房全方位房产服务</text>
            </view>
          </view>
          
          <view class="feature-item">
            <tui-icon name="heart" :size="48" color="#E91E63"></tui-icon>
            <view class="feature-content">
              <text class="feature-title">相亲交友</text>
              <text class="feature-desc">真实身份认证的安全交友平台</text>
            </view>
          </view>
          
          <view class="feature-item">
            <tui-icon name="service" :size="48" color="#FF9800"></tui-icon>
            <view class="feature-content">
              <text class="feature-title">本地服务</text>
              <text class="feature-desc">家政、维修、装修等本地生活服务</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系我们 -->
      <view class="contact-section">
        <view class="section-title">联系我们</view>
        <view class="contact-list">
          <view class="contact-item" @click="copyEmail">
            <tui-icon name="email" :size="40" color="#666"></tui-icon>
            <view class="contact-info">
              <text class="contact-label">客服邮箱</text>
              <text class="contact-value"><EMAIL></text>
            </view>
            <tui-icon name="copy" :size="32" color="#ccc"></tui-icon>
          </view>
          
          <view class="contact-item" @click="copyPhone">
            <tui-icon name="phone" :size="40" color="#666"></tui-icon>
            <view class="contact-info">
              <text class="contact-label">客服热线</text>
              <text class="contact-value">************</text>
            </view>
            <tui-icon name="copy" :size="32" color="#ccc"></tui-icon>
          </view>
          
          <view class="contact-item">
            <tui-icon name="location" :size="40" color="#666"></tui-icon>
            <view class="contact-info">
              <text class="contact-label">公司地址</text>
              <text class="contact-value">北京市朝阳区科技园区</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 法律信息 -->
      <view class="legal-section">
        <view class="section-title">法律信息</view>
        <view class="legal-list">
          <view class="legal-item" @click="goToPrivacyPolicy">
            <text class="legal-text">隐私政策</text>
            <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
          </view>
          
          <view class="legal-item" @click="goToUserAgreement">
            <text class="legal-text">用户协议</text>
            <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
          </view>
          
          <view class="legal-item" @click="goToLicense">
            <text class="legal-text">开源许可</text>
            <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
          </view>
        </view>
      </view>

      <!-- 版权信息 -->
      <view class="copyright">
        <text class="copyright-text">© 2024 本地宝科技有限公司</text>
        <text class="copyright-text">All rights reserved</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar.vue'

// 复制邮箱
const copyEmail = () => {
  uni.setClipboardData({
    data: '<EMAIL>',
    success: () => {
      uni.showToast({
        title: '邮箱已复制',
        icon: 'success'
      })
    }
  })
}

// 复制电话
const copyPhone = () => {
  uni.setClipboardData({
    data: '************',
    success: () => {
      uni.showToast({
        title: '电话已复制',
        icon: 'success'
      })
    }
  })
}

// 隐私政策
const goToPrivacyPolicy = () => {
  uni.showToast({
    title: '隐私政策页面开发中',
    icon: 'none'
  })
}

// 用户协议
const goToUserAgreement = () => {
  uni.showToast({
    title: '用户协议页面开发中',
    icon: 'none'
  })
}

// 开源许可
const goToLicense = () => {
  uni.showToast({
    title: '开源许可页面开发中',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.about-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.content {
  padding: 0 32rpx 40rpx;
}

.app-info {
  text-align: center;
  padding: 80rpx 0;
  
  .app-logo {
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto 32rpx;
    border-radius: 32rpx;
    overflow: hidden;
    background: var(--bg-card);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .app-name {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16rpx;
  }
  
  .app-version {
    display: block;
    font-size: 28rpx;
    color: var(--text-secondary);
    margin-bottom: 24rpx;
  }
  
  .app-desc {
    display: block;
    font-size: 26rpx;
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

.features-section, .contact-section, .legal-section {
  margin-bottom: 48rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.features-list {
  .feature-item {
    display: flex;
    align-items: flex-start;
    background: var(--bg-card);
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 16rpx;
    gap: 24rpx;
    
    .feature-content {
      flex: 1;
      
      .feature-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8rpx;
      }
      
      .feature-desc {
        font-size: 24rpx;
        color: var(--text-secondary);
        line-height: 1.5;
      }
    }
  }
}

.contact-list, .legal-list {
  background: var(--bg-card);
  border-radius: 16rpx;
  overflow: hidden;
}

.contact-item, .legal-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: var(--bg-hover);
  }
}

.contact-item {
  gap: 24rpx;
  
  .contact-info {
    flex: 1;
    
    .contact-label {
      display: block;
      font-size: 28rpx;
      color: var(--text-primary);
      margin-bottom: 8rpx;
    }
    
    .contact-value {
      font-size: 24rpx;
      color: var(--text-secondary);
    }
  }
}

.legal-item {
  justify-content: space-between;
  
  .legal-text {
    font-size: 28rpx;
    color: var(--text-primary);
  }
}

.copyright {
  text-align: center;
  padding: 40rpx 0;
  
  .copyright-text {
    display: block;
    font-size: 24rpx;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 8rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style> 