<template>
  <view class="filter-panel">
    <!-- 遮罩层 -->
    <view
      v-if="showFilterModal"
      class="filter-mask"
      @tap="closeFilterModal"
    ></view>

    <!-- 筛选栏 -->
    <view class="filter-section">
      <view
        class="filter-item"
        v-for="(filter, index) in filterOptions"
        :key="index"
        :class="{ active: filter.active }"
        @tap="toggleFilter(index)"
      >
        <text>{{ filter.label }}</text>
        <text
          v-if="filter.hasDropdown"
          :class="['arrow-icon', { 'arrow-up': filter.active }]"
          class="i-carbon-chevron-down text-gray text-32rpx ml-8rpx"
        ></text>
      </view>
    </view>

    <!-- 筛选内容下拉面板 -->
    <view v-if="showFilterModal" class="filter-dropdown">
      <view class="filter-content" @tap.stop>
        <scroll-view scroll-y class="filter-body">
          <!-- 区域筛选 -->
          <view class="filter-group" v-if="currentFilterType === 'area'">
            <text class="group-title">区域</text>
            <view class="option-grid">
              <text
                v-for="area in areaOptions"
                :key="area.value"
                :class="{ active: selectedFilters.area === area.value }"
                class="option-item"
                @tap="selectArea(area.value)"
              >
                {{ area.label }}
              </text>
            </view>
          </view>

          <!-- 价格筛选 -->
          <view class="filter-group" v-if="currentFilterType === 'price'">
            <text class="group-title">总价 (万/套)</text>
            <view class="option-grid">
              <text
                v-for="price in priceOptions"
                :key="price.value"
                :class="{ active: selectedFilters.price === price.value }"
                class="option-item"
                @tap="selectPrice(price.value)"
              >
                {{ price.label }}
              </text>
            </view>
          </view>

          <!-- 房屋类型筛选 -->
          <view class="filter-group" v-if="currentFilterType === 'houseType'">
            <text class="group-title">户型</text>
            <view class="option-grid">
              <text
                v-for="type in houseTypeOptions"
                :key="type.value"
                :class="{ active: selectedFilters.houseType === type.value }"
                class="option-item"
                @tap="selectHouseType(type.value)"
              >
                {{ type.label }}
              </text>
            </view>
          </view>

          <!-- 特色筛选 -->
          <view class="filter-group" v-if="currentFilterType === 'features'">
            <text class="group-title">楼盘特色</text>
            <view class="option-grid">
              <text
                v-for="feature in featureOptions"
                :key="feature.value"
                :class="{
                  active: selectedFilters.features.includes(feature.value),
                }"
                class="option-item"
                @tap="toggleFeature(feature.value)"
              >
                {{ feature.label }}
              </text>
            </view>

            <text class="group-title mt-32rpx">售卖状态</text>
            <view class="option-grid">
              <text
                v-for="status in saleStatusOptions"
                :key="status.value"
                :class="{ active: selectedFilters.saleStatus === status.value }"
                class="option-item"
                @tap="selectSaleStatus(status.value)"
              >
                {{ status.label }}
              </text>
            </view>

            <text class="group-title mt-32rpx">装修状况</text>
            <view class="option-grid">
              <text
                v-for="decoration in decorationOptions"
                :key="decoration.value"
                :class="{
                  active: selectedFilters.decoration === decoration.value,
                }"
                class="option-item"
                @tap="selectDecoration(decoration.value)"
              >
                {{ decoration.label }}
              </text>
            </view>

            <text class="group-title mt-32rpx">开盘时间</text>
            <view class="option-grid">
              <text
                v-for="time in openTimeOptions"
                :key="time.value"
                :class="{ active: selectedFilters.openTime === time.value }"
                class="option-item"
                @tap="selectOpenTime(time.value)"
              >
                {{ time.label }}
              </text>
            </view>
          </view>

          <!-- 更多筛选 -->
          <view class="filter-group" v-if="currentFilterType === 'more'">
            <text class="group-title">开发商</text>
            <view class="option-grid">
              <text
                v-for="developer in developerOptions"
                :key="developer.value"
                :class="{
                  active: selectedFilters.developer === developer.value,
                }"
                class="option-item"
                @tap="selectDeveloper(developer.value)"
              >
                {{ developer.label }}
              </text>
            </view>
          </view>
        </scroll-view>

        <view class="filter-footer">
          <button class="cancel-btn" @tap="closeFilterModal">不限条件</button>
          <button class="confirm-btn" @tap="confirmFilter">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits } from "vue";

const emit = defineEmits(["filter-change", "open-filter", "close-filter"]);

// 当前筛选类型
const currentFilterType = ref("");
const showFilterModal = ref(false);

// 筛选选项
const filterOptions = ref([
  { label: "区域", active: false, hasDropdown: true, type: "area" },
  { label: "价格", active: false, hasDropdown: true, type: "price" },
  { label: "户型", active: false, hasDropdown: true, type: "houseType" },
  { label: "筛选", active: false, hasDropdown: true, type: "features" },
  { label: "排序", active: false, hasDropdown: true, type: "more" },
]);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
];

// 价格选项
const priceOptions = [
  { label: "200万以下", value: "0-200" },
  { label: "200-250万", value: "200-250" },
  { label: "250-300万", value: "250-300" },
  { label: "300-400万", value: "300-400" },
  { label: "400-500万", value: "400-500" },
  { label: "500-800万", value: "500-800" },
  { label: "800-1000万", value: "800-1000" },
  { label: "1000万以上", value: "1000-" },
];

// 房屋类型选项
const houseTypeOptions = [
  { label: "不限", value: "" },
  { label: "1居", value: "1" },
  { label: "2居", value: "2" },
  { label: "3居", value: "3" },
  { label: "4居", value: "4" },
  { label: "5居", value: "5" },
  { label: "5居+", value: "5+" },
];

// 特色选项
const featureOptions = [
  { label: "视频看房", value: "video" },
  { label: "特价好房", value: "special" },
  { label: "限竞房", value: "limited" },
  { label: "品牌房企", value: "brand" },
  { label: "低单价", value: "lowUnitPrice" },
  { label: "低总价", value: "lowTotalPrice" },
  { label: "小户型", value: "smallUnit" },
  { label: "现房", value: "ready" },
];

// 售卖状态选项
const saleStatusOptions = [
  { label: "在售", value: "selling" },
  { label: "待售", value: "coming" },
  { label: "售罄", value: "sold" },
];

// 装修状况选项
const decorationOptions = [
  { label: "带装修", value: "decorated" },
  { label: "毛坯", value: "rough" },
];

// 开盘时间选项
const openTimeOptions = [
  { label: "最新取证", value: "newest" },
  { label: "近期开盘", value: "recent" },
  { label: "本月开盘", value: "thisMonth" },
  { label: "未来一个月", value: "nextMonth" },
];

// 开发商选项
const developerOptions = [
  { label: "不限", value: "" },
  { label: "万科", value: "vanke" },
  { label: "恒大", value: "evergrande" },
  { label: "碧桂园", value: "countrygarden" },
  { label: "融创", value: "sunac" },
  { label: "保利", value: "poly" },
  { label: "龙湖", value: "longfor" },
];

// 选中的筛选条件
const selectedFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  features: [] as string[],
  saleStatus: "",
  decoration: "",
  openTime: "",
  developer: "",
});

// 方法
const toggleFilter = (index: number) => {
  const filter = filterOptions.value[index];

  // 如果是点击已经激活的筛选项，则关闭筛选框
  if (filter.active && showFilterModal.value) {
    console.log("关闭筛选框");
    closeFilterModal();
    emit("close-filter");
    return;
  }
  emit("open-filter");
  // 重置所有筛选项的激活状态
  filterOptions.value.forEach((item) => {
    item.active = false;
  });

  // 设置当前筛选项为激活状态
  filter.active = true;
  currentFilterType.value = filter.type;
  showFilterModal.value = true;
};

const closeFilterModal = () => {
  showFilterModal.value = false;
  // 重置筛选项的激活状态
  filterOptions.value.forEach((filter) => {
    filter.active = false;
  });
  emit("close-filter");
};

const resetFilter = () => {
  selectedFilters.area = "";
  selectedFilters.price = "";
  selectedFilters.houseType = "";
  selectedFilters.features = [];
  selectedFilters.saleStatus = "";
  selectedFilters.decoration = "";
  selectedFilters.openTime = "";
  selectedFilters.developer = "";
};

const selectArea = (value: string) => {
  selectedFilters.area = selectedFilters.area === value ? "" : value;
  updateFilterStatus("area", selectedFilters.area);
};

const selectPrice = (value: string) => {
  selectedFilters.price = selectedFilters.price === value ? "" : value;
  updateFilterStatus("price", selectedFilters.price);
};

const selectHouseType = (value: string) => {
  selectedFilters.houseType = selectedFilters.houseType === value ? "" : value;
  updateFilterStatus("houseType", selectedFilters.houseType);
};

const toggleFeature = (value: string) => {
  const index = selectedFilters.features.indexOf(value);
  if (index > -1) {
    selectedFilters.features.splice(index, 1);
  } else {
    selectedFilters.features.push(value);
  }
  updateFilterStatus("features", selectedFilters.features.length > 0);
};

const selectSaleStatus = (value: string) => {
  selectedFilters.saleStatus =
    selectedFilters.saleStatus === value ? "" : value;
  updateFilterStatus("features", true);
};

const selectDecoration = (value: string) => {
  selectedFilters.decoration =
    selectedFilters.decoration === value ? "" : value;
  updateFilterStatus("features", true);
};

const selectOpenTime = (value: string) => {
  selectedFilters.openTime = selectedFilters.openTime === value ? "" : value;
  updateFilterStatus("features", true);
};

const selectDeveloper = (value: string) => {
  selectedFilters.developer = selectedFilters.developer === value ? "" : value;
  updateFilterStatus("more", selectedFilters.developer);
};

const updateFilterStatus = (type: string, hasValue: any) => {
  const filterMap: { [key: string]: string } = {
    area: "area",
    price: "price",
    houseType: "houseType",
    features: "features",
    saleStatus: "features",
    decoration: "features",
    openTime: "features",
    developer: "more",
  };

  const filterType = filterMap[type];
  const filterItem = filterOptions.value.find(
    (item) => item.type === filterType
  );
  if (filterItem) {
    filterItem.active = !!hasValue;
  }
};

const confirmFilter = () => {
  // 发送筛选条件给父组件
  emit("filter-change", { ...selectedFilters });
  closeFilterModal();
};
</script>

<style lang="scss" scoped>
/* 筛选栏样式 */
.filter-section {
  position: relative;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 0 32rpx;
  z-index: 991;
}

.filter-scroll {
  white-space: nowrap;
  padding: 0 16rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  position: relative;
  color: #666;
}

.filter-item.active {
  color: #3388ff;
  font-weight: 500;
}

.arrow-icon {
  transition: transform 0.3s;
}

.arrow-up {
  transform: rotate(180deg);
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 800;
}

/* 筛选下拉面板样式 */
.filter-dropdown {
  position: absolute;
  width: 100%;
  z-index: 990;
}

.filter-content {
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  animation: slideDown 0.25s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-body {
  flex: 1;
  max-height: 60vh;
  padding: 24rpx 32rpx;
}

.filter-group {
  margin-bottom: 32rpx;
}

.group-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.option-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.option-item {
  padding: 14rpx 28rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #f5f5f5;
  transition: all 0.2s;
}

.option-item.active {
  background-color: #e6f0ff;
  color: #3388ff;
}

.filter-footer {
  display: flex;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f5f5f5;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f5f5f5;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #3388ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.mt-32rpx {
  margin-top: 32rpx;
}
</style>
