<template>
  <view class="notification-page">
    <CustomNavBar title="消息设置" />
    
    <view class="content">
      <!-- 推送总开关 -->
      <view class="main-switch-section">
        <view class="switch-card">
          <view class="switch-info">
            <text class="switch-title">推送通知总开关</text>
            <text class="switch-desc">关闭后将不会收到任何推送消息</text>
          </view>
          <tui-switch v-model="notificationEnabled" @change="handleMainSwitchChange"></tui-switch>
        </view>
      </view>

      <!-- 消息类型设置 -->
      <view class="notification-section">
        <view class="section-title">消息类型</view>
        <view class="notification-list">
          <view v-for="item in messageTypes" :key="item.key" class="notification-item">
            <view class="item-left">
              <view class="item-icon">
                <tui-icon :name="item.icon" :size="40" :color="item.color"></tui-icon>
              </view>
              <view class="item-info">
                <text class="item-title">{{ item.title }}</text>
                <text class="item-desc">{{ item.desc }}</text>
              </view>
            </view>
            <tui-switch 
              v-model="item.enabled" 
              :disabled="!notificationEnabled"
              @change="handleTypeChange(item)"
            ></tui-switch>
          </view>
        </view>
      </view>

      <!-- 推送时间设置 -->
      <view class="time-section">
        <view class="section-title">推送时间</view>
        <view class="time-list">
          <view class="time-item" @click="setQuietTime">
            <view class="item-left">
              <tui-icon name="time" :size="40" color="#666"></tui-icon>
              <view class="item-info">
                <text class="item-label">免打扰时间</text>
                <text class="item-desc">设置不接收推送的时间段</text>
              </view>
            </view>
            <view class="item-right">
              <text class="time-value">{{ quietTimeText }}</text>
              <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
            </view>
          </view>
          
          <view class="time-item">
            <view class="item-left">
              <tui-icon name="vibrate" :size="40" color="#666"></tui-icon>
              <view class="item-info">
                <text class="item-label">震动提醒</text>
                <text class="item-desc">收到消息时震动提醒</text>
              </view>
            </view>
            <tui-switch v-model="vibrateEnabled" :disabled="!notificationEnabled"></tui-switch>
          </view>
          
          <view class="time-item">
            <view class="item-left">
              <tui-icon name="sound" :size="40" color="#666"></tui-icon>
              <view class="item-info">
                <text class="item-label">声音提醒</text>
                <text class="item-desc">收到消息时播放提示音</text>
              </view>
            </view>
            <tui-switch v-model="soundEnabled" :disabled="!notificationEnabled"></tui-switch>
          </view>
        </view>
      </view>

      <!-- 高级设置 -->
      <view class="advanced-section">
        <view class="section-title">高级设置</view>
        <view class="advanced-list">
          <view class="advanced-item" @click="clearNotifications">
            <view class="item-left">
              <tui-icon name="delete" :size="40" color="#F44336"></tui-icon>
              <text class="item-label">清空所有通知</text>
            </view>
            <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
          </view>
          
          <view class="advanced-item" @click="resetSettings">
            <view class="item-left">
              <tui-icon name="refresh" :size="40" color="#666"></tui-icon>
              <text class="item-label">恢复默认设置</text>
            </view>
            <tui-icon name="right" :size="32" color="#ccc"></tui-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import CustomNavBar from '@/components/CustomNavBar.vue'

// 主开关
const notificationEnabled = ref(true)
const vibrateEnabled = ref(true)
const soundEnabled = ref(true)

// 免打扰时间
const quietStartTime = ref('23:00')
const quietEndTime = ref('08:00')

// 消息类型设置
const messageTypes = ref([
  {
    key: 'system',
    title: '系统消息',
    desc: '系统通知、版本更新等',
    icon: 'notification',
    color: '#2196F3',
    enabled: true
  },
  {
    key: 'job',
    title: '招聘消息',
    desc: '新职位推荐、简历状态更新',
    icon: 'briefcase',
    color: '#4CAF50',
    enabled: true
  },
  {
    key: 'house',
    title: '房产消息',
    desc: '新房源推荐、房源状态更新',
    icon: 'home',
    color: '#FF9800',
    enabled: true
  },
  {
    key: 'dating',
    title: '交友消息',
    desc: '新配对、聊天消息',
    icon: 'heart',
    color: '#E91E63',
    enabled: false
  },
  {
    key: 'service',
    title: '服务消息',
    desc: '服务预约、订单状态',
    icon: 'service',
    color: '#9C27B0',
    enabled: true
  },
  {
    key: 'activity',
    title: '活动消息',
    desc: '优惠活动、福利通知',
    icon: 'gift',
    color: '#FF5722',
    enabled: true
  }
])

// 免打扰时间文本
const quietTimeText = computed(() => {
  return `${quietStartTime.value} - ${quietEndTime.value}`
})

// 主开关变化
const handleMainSwitchChange = (e: any) => {
  console.log('推送总开关:', e.detail.value)
  if (!e.detail.value) {
    // 关闭总开关时，禁用所有子选项
    messageTypes.value.forEach(item => {
      item.enabled = false
    })
  }
}

// 消息类型开关变化
const handleTypeChange = (item: any) => {
  console.log(`${item.title}设置:`, item.enabled)
}

// 设置免打扰时间
const setQuietTime = () => {
  uni.showActionSheet({
    itemList: ['设置开始时间', '设置结束时间'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 设置开始时间
        uni.showModal({
          title: '设置开始时间',
          editable: true,
          placeholderText: '请输入时间(如: 23:00)',
          success: (res) => {
            if (res.confirm && res.content) {
              quietStartTime.value = res.content
            }
          }
        })
      } else if (res.tapIndex === 1) {
        // 设置结束时间
        uni.showModal({
          title: '设置结束时间',
          editable: true,
          placeholderText: '请输入时间(如: 08:00)',
          success: (res) => {
            if (res.confirm && res.content) {
              quietEndTime.value = res.content
            }
          }
        })
      }
    }
  })
}

// 清空所有通知
const clearNotifications = () => {
  uni.showModal({
    title: '清空通知',
    content: '确定要清空所有通知吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已清空所有通知',
          icon: 'success'
        })
      }
    }
  })
}

// 恢复默认设置
const resetSettings = () => {
  uni.showModal({
    title: '恢复默认',
    content: '确定要恢复默认设置吗？',
    success: (res) => {
      if (res.confirm) {
        // 恢复默认设置
        notificationEnabled.value = true
        vibrateEnabled.value = true
        soundEnabled.value = true
        quietStartTime.value = '23:00'
        quietEndTime.value = '08:00'
        
        messageTypes.value.forEach(item => {
          item.enabled = item.key !== 'dating' // 交友消息默认关闭
        })
        
        uni.showToast({
          title: '已恢复默认设置',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.notification-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.content {
  padding: 0 32rpx 40rpx;
}

.main-switch-section {
  margin-bottom: 48rpx;
  
  .switch-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    padding: 40rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: -30%;
      right: -15%;
      width: 150rpx;
      height: 150rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }
    
    .switch-info {
      flex: 1;
      position: relative;
      z-index: 1;
      
      .switch-title {
        color: #fff;
        font-size: 32rpx;
        font-weight: 600;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .switch-desc {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
        line-height: 1.4;
      }
    }
  }
}

.notification-section, .time-section, .advanced-section {
  margin-bottom: 48rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.notification-list, .time-list {
  background: var(--bg-card);
  border-radius: 16rpx;
  overflow: hidden;
}

.notification-item, .time-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  
  &:last-child {
    border-bottom: none;
  }
  
  .item-left {
    display: flex;
    align-items: center;
    gap: 24rpx;
    flex: 1;
    
    .item-icon {
      flex-shrink: 0;
    }
    
    .item-info {
      flex: 1;
      
      .item-title, .item-label {
        display: block;
        font-size: 28rpx;
        color: var(--text-primary);
        margin-bottom: 8rpx;
      }
      
      .item-desc {
        font-size: 24rpx;
        color: var(--text-secondary);
        line-height: 1.4;
      }
    }
  }
  
  .item-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    .time-value {
      font-size: 26rpx;
      color: var(--text-secondary);
    }
  }
}

.advanced-list {
  background: var(--bg-card);
  border-radius: 16rpx;
  overflow: hidden;
}

.advanced-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: var(--bg-hover);
  }
  
  .item-left {
    display: flex;
    align-items: center;
    gap: 24rpx;
    
    .item-label {
      font-size: 28rpx;
      color: var(--text-primary);
    }
  }
}
</style> 