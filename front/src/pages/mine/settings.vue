<template>
  <view class="settings-page">
    <view class="settings-content">
      <!-- 账号信息 -->
      <view class="settings-section">
        <view class="section-title">账号信息</view>
        <view class="settings-list">
          <view class="setting-item" @click="editProfile">
            <view class="item-left">
              <text class="i-solar-user-circle-linear text-20rpx text-blue-500"></text>
              <text class="item-label">个人资料</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{ userStore.user?.name || '未设置' }}</text>
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
          
          <view class="setting-item" @click="editPhone">
            <view class="item-left">
              <text class="i-solar-phone-linear text-20rpx text-green-500"></text>
              <text class="item-label">手机号码</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{ userStore.user?.phone || '未绑定' }}</text>
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
          
          <view class="setting-item" @click="changePassword">
            <view class="item-left">
              <text class="i-solar-lock-password-linear text-20rpx text-orange-500"></text>
              <text class="item-label">修改密码</text>
            </view>
            <view class="item-right">
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <view class="section-title">应用设置</view>
        <view class="settings-list">
          <view class="setting-item">
            <view class="item-left">
              <text class="i-solar-bell-linear text-20rpx text-purple-500"></text>
              <text class="item-label">推送通知</text>
            </view>
            <view class="item-right">
              <tui-switch v-model="notificationEnabled" @change="handleNotificationChange"></tui-switch>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-left">
              <text class="i-solar-volume-loud-linear text-20rpx text-indigo-500"></text>
              <text class="item-label">声音提醒</text>
            </view>
            <view class="item-right">
              <tui-switch v-model="soundEnabled" @change="handleSoundChange"></tui-switch>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="settings-section">
        <view class="section-title">其他设置</view>
        <view class="settings-list">
          <view class="setting-item" @click="clearCache">
            <view class="item-left">
              <text class="i-solar-trash-bin-minimalistic-linear text-20rpx text-red-500"></text>
              <text class="item-label">清理缓存</text>
            </view>
            <view class="item-right">
              <text class="item-value">{{ cacheSize }}</text>
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
          
          <view class="setting-item" @click="checkUpdate">
            <view class="item-left">
              <text class="i-solar-refresh-linear text-20rpx text-blue-500"></text>
              <text class="item-label">检查更新</text>
            </view>
            <view class="item-right">
              <text class="item-value">v1.0.0</text>
              <text class="i-solar-arrow-right-linear text-16rpx text-gray-400"></text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 设置状态
const notificationEnabled = ref(true)
const soundEnabled = ref(true)
const cacheSize = ref('12.5MB')

// 编辑个人资料
const editProfile = () => {
  uni.navigateTo({
    url: '/pages/mine/profile'
  })
}

// 编辑手机号
const editPhone = () => {
  uni.showToast({
    title: '手机号修改功能开发中',
    icon: 'none'
  })
}

// 修改密码
const changePassword = () => {
  uni.showToast({
    title: '密码修改功能开发中',
    icon: 'none'
  })
}

// 处理通知设置变化
const handleNotificationChange = (e: any) => {
  console.log('通知设置:', e.detail.value)
}

// 处理声音设置变化
const handleSoundChange = (e: any) => {
  console.log('声音设置:', e.detail.value)
}

// 清理缓存
const clearCache = () => {
  uni.showModal({
    title: '清理缓存',
    content: '确定要清理应用缓存吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({ title: '清理中...' })
        setTimeout(() => {
          uni.hideLoading()
          cacheSize.value = '0MB'
          uni.showToast({
            title: '缓存清理完成',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}

// 检查更新
const checkUpdate = () => {
  uni.showLoading({ title: '检查中...' })
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '已是最新版本',
      icon: 'success'
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
.settings-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.settings-content {
  padding: 32rpx;
}

.settings-section {
  margin-bottom: 48rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-base);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.settings-list {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f8f9fa;
  }
  
  .item-left {
    display: flex;
    align-items: center;
    gap: 20rpx;
    
    .item-label {
      font-size: 28rpx;
      color: var(--text-base);
    }
  }
  
  .item-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    .item-value {
      font-size: 26rpx;
      color: var(--text-secondary);
      max-width: 300rpx;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style> 