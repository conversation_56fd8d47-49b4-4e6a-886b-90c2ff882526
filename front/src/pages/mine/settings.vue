<template>
  <view class="settings-page">
    <view class="settings-content">
      <!-- 账号信息 -->
      <view class="settings-section">
        <view class="section-title">账号信息</view>
        <view class="settings-list">
          <tui-list-cell
            title="个人资料"
            :note="userStore.user?.name || '未设置'"
            arrow
            padding="24rpx 32rpx"
            @click="editProfile"
          >
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-user-circle-linear text-20rpx text-blue-500"
                ></text>
              </view>
            </template>
          </tui-list-cell>
          <tui-list-cell
            title="手机号码"
            :note="userStore.user?.phone || '未绑定'"
            arrow
            padding="24rpx 32rpx"
            @click="editPhone"
          >
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-phone-linear text-20rpx text-green-500"
                ></text>
              </view>
            </template>
          </tui-list-cell>
          <tui-list-cell
            title="修改密码"
            arrow
            padding="24rpx 32rpx"
            @click="changePassword"
          >
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-lock-password-linear text-20rpx text-orange-500"
                ></text>
              </view>
            </template>
          </tui-list-cell>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <view class="section-title">应用设置</view>
        <view class="settings-list">
          <tui-list-cell title="推送通知" padding="24rpx 32rpx" :arrow="false">
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-bell-linear text-20rpx text-purple-500"
                ></text>
              </view>
            </template>
            <template #right>
              <tui-switch
                v-model="notificationEnabled"
                @change="handleNotificationChange"
              ></tui-switch>
            </template>
          </tui-list-cell>
          <tui-list-cell title="声音提醒" padding="24rpx 32rpx" :arrow="false">
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-volume-loud-linear text-20rpx text-indigo-500"
                ></text>
              </view>
            </template>
            <template #right>
              <tui-switch
                v-model="soundEnabled"
                @change="handleSoundChange"
              ></tui-switch>
            </template>
          </tui-list-cell>
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="settings-section">
        <view class="section-title">其他设置</view>
        <view class="settings-list">
          <tui-list-cell
            title="清理缓存"
            :note="cacheSize"
            arrow
            padding="24rpx 32rpx"
            @click="clearCache"
          >
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-trash-bin-minimalistic-linear text-20rpx text-red-500"
                ></text>
              </view>
            </template>
          </tui-list-cell>
          <tui-list-cell
            title="检查更新"
            note="v1.0.0"
            arrow
            padding="24rpx 32rpx"
            @click="checkUpdate"
          >
            <template #icon>
              <view class="icon-wrapper">
                <text
                  class="i-solar-refresh-linear text-20rpx text-blue-500"
                ></text>
              </view>
            </template>
          </tui-list-cell>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 设置状态
const notificationEnabled = ref(true)
const soundEnabled = ref(true)
const cacheSize = ref('12.5MB')

// 编辑个人资料
const editProfile = () => {
  uni.navigateTo({
    url: '/pages/mine/profile'
  })
}

// 编辑手机号
const editPhone = () => {
  uni.showToast({
    title: '手机号修改功能开发中',
    icon: 'none'
  })
}

// 修改密码
const changePassword = () => {
  uni.showToast({
    title: '密码修改功能开发中',
    icon: 'none'
  })
}

// 处理通知设置变化
const handleNotificationChange = (e: any) => {
  console.log('通知设置:', e.detail.value)
}

// 处理声音设置变化
const handleSoundChange = (e: any) => {
  console.log('声音设置:', e.detail.value)
}

// 清理缓存
const clearCache = () => {
  uni.showModal({
    title: '清理缓存',
    content: '确定要清理应用缓存吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({ title: '清理中...' })
        setTimeout(() => {
          uni.hideLoading()
          cacheSize.value = '0MB'
          uni.showToast({
            title: '缓存清理完成',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}

// 检查更新
const checkUpdate = () => {
  uni.showLoading({ title: '检查中...' })
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '已是最新版本',
      icon: 'success'
    })
  }, 1500)
}
</script>

<style lang="scss" scoped>
.settings-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.settings-content {
  padding: 32rpx;
}

.settings-section {
  margin-bottom: 48rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-base);
    margin-bottom: 24rpx;
    padding-left: 8rpx;
  }
}

.settings-list {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.icon-wrapper {
  margin-right: 20rpx;
}
</style> 