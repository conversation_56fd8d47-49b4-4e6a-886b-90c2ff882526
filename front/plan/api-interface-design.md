# 本地宝API接口设计

## 一、API设计规范

### 1.1 基础规范
- **Base URL**: `https://api.bdb.com`
- **版本**: `/api/v1`
- **认证**: <PERSON><PERSON> (JWT)
- **响应格式**: JSON

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 二、认证模块

### 2.1 用户登录
```http
POST /api/v1/auth/login
{
  "phone": "13800138000",
  "password": "password123"
}

Response:
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": "uuid",
      "username": "用户名",
      "avatar": "头像URL"
    }
  }
}
```

### 2.2 短信验证码
```http
POST /api/v1/auth/sms/send
{
  "phone": "13800138000",
  "type": "login"
}
```

### 2.3 微信登录
```http
POST /api/v1/auth/wechat
{
  "code": "wx_auth_code"
}
```

## 三、用户模块

### 3.1 获取用户信息
```http
GET /api/v1/users/profile
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": {
    "id": "uuid",
    "username": "用户名",
    "phone": "138****8000",
    "avatar": "头像URL",
    "verification_status": 2,
    "vip_level": 1
  }
}
```

### 3.2 更新用户信息
```http
PUT /api/v1/users/profile
{
  "nickname": "新昵称",
  "avatar": "新头像URL",
  "introduction": "个人简介"
}
```

## 四、招聘模块

### 4.1 职位列表
```http
GET /api/v1/jobs?category=1&city=北京&page=1&limit=20

Response:
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "uuid",
        "title": "前端工程师",
        "company": "科技公司",
        "salary_min": 15000,
        "salary_max": 25000,
        "location": "北京市",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1
  }
}
```

### 4.2 发布职位
```http
POST /api/v1/jobs
{
  "title": "前端工程师",
  "category": 1,
  "description": "职位描述",
  "salary_min": 15000,
  "salary_max": 25000,
  "location_city": "北京"
}
```

### 4.3 简历管理
```http
GET /api/v1/resumes/my
PUT /api/v1/resumes
POST /api/v1/jobs/{job_id}/apply
```

## 五、房产模块

### 5.1 房源列表
```http
GET /api/v1/houses?type=1&city=北京&min_price=100&max_price=500

Response:
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "uuid",
        "title": "精装三居室",
        "house_type": 1,
        "area": 120.5,
        "total_price": 450.0,
        "location": "朝阳区",
        "images": ["url1", "url2"]
      }
    ]
  }
}
```

### 5.2 发布房源
```http
POST /api/v1/houses
{
  "title": "精装三居室",
  "house_type": 1,
  "city": "北京",
  "area": 120.5,
  "total_price": 450.0,
  "images": ["url1", "url2"]
}
```

## 六、零工模块

### 6.1 零工列表
```http
GET /api/v1/gigs?category=1&city=北京&date=2024-01-01

Response:
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "uuid",
        "title": "会议服务员",
        "work_date": "2024-01-15",
        "payment_amount": 200.0,
        "required_people": 5,
        "applicant_count": 3
      }
    ]
  }
}
```

### 6.2 报名零工
```http
POST /api/v1/gigs/{gig_id}/apply
{
  "message": "我有相关经验"
}
```

## 七、交友模块

### 7.1 推荐用户
```http
GET /api/v1/dating/recommendations

Response:
{
  "code": 200,
  "data": [
    {
      "id": "uuid",
      "name": "小美",
      "age": 25,
      "photos": ["url1"],
      "tags": ["旅行", "美食"],
      "distance": 2.5
    }
  ]
}
```

### 7.2 用户操作
```http
POST /api/v1/dating/actions
{
  "target_user_id": "uuid",
  "action": 1 // 1:喜欢 2:不喜欢
}
```

### 7.3 动态广场
```http
GET /api/v1/dating/posts
POST /api/v1/dating/posts
POST /api/v1/dating/posts/{post_id}/like
```

## 八、消息模块

### 8.1 会话列表
```http
GET /api/v1/conversations

Response:
{
  "code": 200,
  "data": [
    {
      "id": "uuid",
      "name": "小美",
      "avatar": "avatar_url",
      "last_message": {
        "content": "你好",
        "time": "2024-01-01T10:00:00Z"
      },
      "unread_count": 3
    }
  ]
}
```

### 8.2 聊天记录
```http
GET /api/v1/conversations/{conversation_id}/messages
```

### 8.3 发送消息
```http
POST /api/v1/conversations/{conversation_id}/messages
{
  "message_type": 1,
  "content": "你好"
}
```

## 九、支付模块

### 9.1 钱包信息
```http
GET /api/v1/wallet

Response:
{
  "code": 200,
  "data": {
    "balance": 10000,
    "points": 1500
  }
}
```

### 9.2 创建订单
```http
POST /api/v1/orders
{
  "order_type": 1,
  "title": "VIP会员",
  "amount": 9900
}
```

### 9.3 支付处理
```http
POST /api/v1/payments
{
  "order_id": "uuid",
  "payment_method": 2
}
```

## 十、通用接口

### 10.1 文件上传
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

FormData:
- file: (file)
- business_type: "avatar"
```

### 10.2 地区数据
```http
GET /api/v1/areas?parent_id=0
```

### 10.3 搜索
```http
GET /api/v1/search?q=关键词&type=job
```

## 十一、WebSocket

### 11.1 连接
```javascript
const ws = new WebSocket('wss://api.bdb.com/ws?token={jwt_token}');
```

### 11.2 消息格式
```json
{
  "type": "message",
  "data": {
    "conversation_id": "uuid",
    "message": {
      "content": "消息内容",
      "message_type": 1
    }
  }
}
```

## 十二、状态码

### 12.1 HTTP状态码
- 200: 成功
- 400: 参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

### 12.2 业务状态码
- 10001: 用户不存在
- 10002: 密码错误
- 20001: 职位不存在
- 30001: 房源不存在
- 40001: 零工不存在
- 50001: 余额不足

这个API设计覆盖了本地宝平台的所有核心功能，遵循RESTful规范，便于前端调用和后端实现。 