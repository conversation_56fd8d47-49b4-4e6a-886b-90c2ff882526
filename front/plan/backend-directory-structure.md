# 本地宝后端目录结构设计（单体架构）

## 一、整体项目结构

```
bdb-backend/
├── cmd/                          # 应用程序入口
│   └── server/
│       └── main.go              # 主程序入口
├── internal/                     # 私有应用代码
│   ├── types/                   # api请求类型与返回类型
│   ├── api/                     # 应用程序层
    │   ├── controller/              # HTTP处理层
    │   │   ├── auth_ctl.go              # 认证相关
    │   │   ├── user_ctl.go              # 用户管理
    │   │   ├── job_ctl.go               # 招聘相关
    │   │   ├── house_ctl.go             # 房产相关
    │   │   ├── gig_ctl.go               # 零工相关
    │   │   ├── dating_ctl.go            # 交友相关
    │   │   ├── message_ctl.go           # 消息相关
    │   │   ├── payment_ctl.go           # 支付相关
    │   │   └── file_ctl.go              # 文件上传
│   │   ├── router/              # 路由配置
│   │   ├── middleware/          # 中间件
│   │   └── server.go            # 服务器配置
│   ├── service/                 # 业务逻辑层
│   │   ├── auth_svc.go              # 认证服务
│   │   ├── user_svc.go              # 用户服务
│   │   ├── job_svc.go               # 招聘服务
│   │   ├── house_svc.go             # 房产服务
│   │   ├── gig_svc.go               # 零工服务
│   │   ├── dating_svc.go            # 交友服务
│   │   ├── message_svc.go           # 消息服务
│   │   ├── payment_svc.go           # 支付服务
│   │   └── file.go              # 文件服务
│   ├── repository/              # 数据访问层
│   │   ├── user_repo.go              # 用户数据访问
│   │   ├── job_repo.go               # 招聘数据访问
│   │   ├── house_repo.go             # 房产数据访问
│   │   ├── gig_repo.go               # 零工数据访问
│   │   ├── dating_repo.go            # 交友数据访问
│   │   ├── message_repo.go           # 消息数据访问
│   │   └── payment_repo.go           # 支付数据访问
│   ├── model/                   # 数据模型
│   │   ├── user.go              # 用户模型
│   │   ├── job.go               # 招聘模型
│   │   ├── house.go             # 房产模型
│   │   ├── gig.go               # 零工模型
│   │   ├── dating.go            # 交友模型
│   │   ├── message.go           # 消息模型
│   │   └── payment.go           # 支付模型
│   ├── utils/                   # 通用工具
│   │   ├── helper.go            # 辅助函数
│   │   ├── time.go              # 时间处理
│   │   └── http.go              # HTTP工具
│   └── websocket/               # WebSocket处理
│       ├── manager.go           # 连接管理
│       ├── client.go            # 客户端
│       └── message.go           # 消息处理
├── pkg/                         # 公共库代码
│   ├── auth/                    # 认证工具
│   │   ├── jwt.go               # JWT处理
│   │   └── password.go          # 密码加密
│   ├── cache/                   # 缓存工具
│   │   ├── redis.go             # Redis客户端
│   │   └── memory.go            # 内存缓存
│   ├── database/                # 数据库工具
│   │   ├── postgres.go          # PostgreSQL连接
│   │   └── migration.go         # 数据库迁移
│   ├── logger/                  # 日志工具
│   │   └── logger.go            # 日志配置
│   ├── validator/               # 验证工具
│   │   └── validator.go         # 参数验证
│   ├── queue/                   # 队列工具（可选）
│   │   └── memory.go            # 内存队列
│   ├── storage/                 # 存储工具
│   │   └── qiniu.go             # 七牛云存储
│   ├── sms/                     # 短信工具
│   │   └── aliyun.go            # 阿里云短信
│   ├── payment/                 # 支付工具
│   │   ├── wechat.go            # 微信支付
│   │   └── alipay.go            # 支付宝
│   └── response/                # 响应格式
│       └── response.go          # 统一响应格式
├── configs/                     # 配置文件
│   ├── config.yaml              # 主配置文件
│   ├── config.dev.yaml          # 开发环境配置
│   ├── config.prod.yaml         # 生产环境配置
│   └── database.sql             # 数据库初始化
├── migrations/                  # 数据库迁移
│   ├── 000001_init_users.up.sql
│   ├── 000001_init_users.down.sql
│   ├── 000002_init_jobs.up.sql
│   ├── 000002_init_jobs.down.sql
│   └── README.md
├── deployments/                 # 部署配置
│   ├── Dockerfile               # Docker镜像配置
│   ├── docker-compose.yml       # 本地开发环境
│   └── nginx.conf               # Nginx配置
├── scripts/                     # 脚本文件
│   ├── build.sh                 # 构建脚本
│   ├── migrate.sh               # 数据库迁移脚本
│   └── deploy.sh                # 部署脚本
├── tests/                       # 测试文件
│   ├── integration/             # 集成测试
│   └── unit/                    # 单元测试
├── docs/                        # 项目文档
│   ├── API.md                   # API文档
│   └── DEPLOY.md                # 部署指南
├── .env                         # 环境变量
├── .env.example                 # 环境变量示例
├── .gitignore                   # Git忽略文件
├── .dockerignore                # Docker忽略文件
├── go.mod                       # Go模块文件
├── go.sum                       # Go模块校验
├── Makefile                     # 构建配置
└── README.md                    # 项目说明
```

## 二、分层架构设计

### 2.1 整体架构

```
┌─────────────────┐
│   HTTP Layer    │ ← Gin路由 + 中间件
├─────────────────┤
│   Handler       │ ← 请求处理层
├─────────────────┤
│   Service       │ ← 业务逻辑层
├─────────────────┤
│   Repository    │ ← 数据访问层
├─────────────────┤
│   Model         │ ← 数据模型层
└─────────────────┘
```

### 2.2 依赖关系

- Handler 依赖 Service
- Service 依赖 Repository
- Repository 依赖 Model
- 所有层都可以使用 pkg/ 中的工具

## 三、核心目录详解

### 3.1 cmd/ - 应用程序入口

```go
// cmd/server/main.go
package main

import (
    "bdb-backend/internal/app"
    "bdb-backend/pkg/logger"
    "log"
)

func main() {
    // 初始化配置
    cfg := config.Load()

    // 初始化日志
    logger.Init(cfg.Logger)

    // 启动服务器
    server := app.NewServer(cfg)
    if err := server.Run(); err != nil {
        log.Fatal("启动服务器失败:", err)
    }
}
```

### 3.2 internal/ - 私有应用代码

#### 3.2.1 app/ - 应用程序层

```go
// internal/app/server.go
package app

type Server struct {
    router *gin.Engine
    config *config.Config
    db     *gorm.DB
    redis  *redis.Client
}

func NewServer(cfg *config.Config) *Server {
    return &Server{
        config: cfg,
        router: setupRouter(),
        db:     setupDatabase(cfg),
        redis:  setupRedis(cfg),
    }
}

func (s *Server) Run() error {
    return s.router.Run(":" + s.config.Server.Port)
}
```

#### 3.2.2 handler/ - HTTP 处理层

```go
// internal/handler/user.go
package handler

type UserHandler struct {
    userService service.UserService
}

func NewUserHandler(userService service.UserService) *UserHandler {
    return &UserHandler{userService: userService}
}

// 获取用户资料
func (h *UserHandler) GetProfile(c *gin.Context) {
    userID := c.GetString("user_id")

    user, err := h.userService.GetProfile(userID)
    if err != nil {
        c.JSON(400, response.Error(400, err.Error()))
        return
    }

    c.JSON(200, response.Success(user))
}

// 更新用户资料
func (h *UserHandler) UpdateProfile(c *gin.Context) {
    // 实现更新逻辑
}
```

#### 3.2.3 service/ - 业务逻辑层

```go
// internal/service/user.go
package service

type UserService struct {
    userRepo repository.UserRepository
    cache    cache.Cache
    logger   logger.Logger
}

func NewUserService(userRepo repository.UserRepository) *UserService {
    return &UserService{
        userRepo: userRepo,
        cache:    cache.NewRedisCache(),
        logger:   logger.NewLogger(),
    }
}

func (s *UserService) GetProfile(userID string) (*model.User, error) {
    // 先从缓存获取
    if user := s.getUserFromCache(userID); user != nil {
        return user, nil
    }

    // 从数据库获取
    user, err := s.userRepo.GetByID(userID)
    if err != nil {
        return nil, err
    }

    // 缓存结果
    s.cacheUser(user)

    return user, nil
}
```

### 3.3 pkg/ - 公共库代码

#### 3.3.1 response/ - 统一响应格式

```go
// pkg/response/response.go
package response

type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func Success(data interface{}) Response {
    return Response{
        Code:    200,
        Message: "success",
        Data:    data,
    }
}

func Error(code int, message string) Response {
    return Response{
        Code:    code,
        Message: message,
    }
}
```

#### 3.3.2 auth/ - 认证工具

```go
// pkg/auth/jwt.go
package auth

type JWTManager struct {
    secretKey []byte
    tokenTTL  time.Duration
}

func NewJWTManager(secretKey string, tokenTTL time.Duration) *JWTManager {
    return &JWTManager{
        secretKey: []byte(secretKey),
        tokenTTL:  tokenTTL,
    }
}

func (j *JWTManager) GenerateToken(userID string) (string, error) {
    claims := &Claims{
        UserID: userID,
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(j.tokenTTL).Unix(),
        },
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString(j.secretKey)
}
```

### 3.4 configs/ - 配置文件

#### 3.4.1 主配置文件

```yaml
# configs/config.yaml
server:
  port: "8080"
  mode: "release"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "bdb"
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100

redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10

jwt:
  secret: "your-jwt-secret-key"
  ttl: "24h"

storage:
  qiniu:
    access_key: "your-access-key"
    secret_key: "your-secret-key"
    bucket: "bdb-files"
    domain: "your-domain.com"

sms:
  aliyun:
    access_key: "your-access-key"
    secret_key: "your-secret-key"
    sign_name: "本地宝"
    template_code: "SMS_123456789"
```

### 3.5 Makefile - 构建配置

```makefile
# Makefile
.PHONY: build run dev test clean docker-build docker-run

# 项目名称
PROJECT_NAME=bdb-backend
BINARY_NAME=server

# 构建
build:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME) cmd/server/main.go

# 运行
run:
	go run cmd/server/main.go

# 开发模式（热重载）
dev:
	air -c .air.toml

# 测试
test:
	go test -v ./...

# 清理
clean:
	rm -rf bin/

# Docker构建
docker-build:
	docker build -t $(PROJECT_NAME):latest .

# Docker运行
docker-run:
	docker-compose up -d

# 停止Docker
docker-stop:
	docker-compose down

# 数据库迁移
migrate-up:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" up

migrate-down:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" down

# 代码格式化
fmt:
	go fmt ./...
	gofumpt -w .

# 代码检查
lint:
	golangci-lint run

# 生成API文档
docs:
	swag init -g cmd/server/main.go -o ./docs

# 安装依赖
deps:
	go mod tidy
	go mod download
```

## 四、部署配置

### 4.1 Dockerfile

```dockerfile
# deployments/Dockerfile
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装git和ca-certificates
RUN apk --no-cache add git ca-certificates

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/server/main.go

# 第二阶段：运行时镜像
FROM alpine:latest

# 安装ca-certificates和tzdata
RUN apk --no-cache add ca-certificates tzdata

WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"]
```

### 4.2 Docker Compose

```yaml
# deployments/docker-compose.yml
version: "3.8"

services:
  app:
    build:
      context: ..
      dockerfile: deployments/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ENV=production
      - DATABASE_URL=******************************************/bdb?sslmode=disable
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - bdb-network

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: bdb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - bdb-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - bdb-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - bdb-network

volumes:
  postgres_data:

networks:
  bdb-network:
    driver: bridge
```

## 五、开发规范

### 5.1 目录命名规范

- **小写字母**：所有目录名使用小写字母
- **单数形式**：package 名使用单数（user 不是 users）
- **语义化**：目录名清楚表达功能

### 5.2 文件命名规范

- **功能模块**：按业务模块命名（user.go, job.go）
- **下划线分隔**：多单词使用下划线（user_profile.go）
- **测试文件**：以\_test.go 结尾

### 5.3 代码组织规范

- **单一职责**：每个文件只负责一个功能模块
- **依赖注入**：使用依赖注入管理组件
- **接口设计**：定义清晰的接口边界
- **错误处理**：统一的错误处理机制

### 5.4 包导入规范

```go
package handler

import (
    // 标准库
    "context"
    "fmt"
    "net/http"

    // 第三方库
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"

    // 项目内部包
    "bdb-backend/internal/service"
    "bdb-backend/pkg/response"
)
```

## 六、优势总结

### 6.1 简洁性

- **单体架构**：避免微服务的复杂性
- **清晰分层**：易于理解和维护
- **统一部署**：简化部署和运维

### 6.2 可扩展性

- **模块化设计**：便于功能扩展
- **预留接口**：支持后期服务拆分
- **标准架构**：遵循 Go 项目最佳实践

### 6.3 开发效率

- **快速启动**：降低项目搭建成本
- **统一规范**：减少决策成本
- **工具完善**：提供完整的开发工具链

这个架构设计专门为中小型项目和个人全栈开发者优化，既保持了代码的专业性，又避免了过度设计的复杂性。
