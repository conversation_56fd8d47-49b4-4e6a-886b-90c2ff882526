# 本地宝数据库设计方案

## 一、数据库架构

### 1.1 技术选型
- **主数据库**：PostgreSQL 15+
- **缓存数据库**：Redis 7.0+
- **分片策略**：按业务模块垂直分片

### 1.2 数据库分配
```yaml
bdb_user:      # 用户相关
bdb_business:  # 业务数据（招聘、房产、零工、交友）
bdb_message:   # 消息数据
bdb_payment:   # 支付交易
```

## 二、核心数据表设计

### 2.1 用户模块

#### 用户基础表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255),
    avatar_url VARCHAR(500),
    status INTEGER DEFAULT 1,
    login_type INTEGER DEFAULT 1,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 用户资料表 (user_profiles)
```sql
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    real_name VARCHAR(50),
    nickname VARCHAR(50),
    gender INTEGER,
    birth_date DATE,
    location_city VARCHAR(50),
    occupation VARCHAR(100),
    company VARCHAR(100),
    introduction TEXT,
    tags JSONB,
    verification_status INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### VIP会员表 (vip_memberships)
```sql
CREATE TABLE vip_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    vip_level INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 招聘模块

#### 企业表 (companies)
```sql
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    company_name VARCHAR(100) NOT NULL,
    industry VARCHAR(50),
    company_size INTEGER,
    description TEXT,
    logo_url VARCHAR(500),
    verification_status INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 职位表 (jobs)
```sql
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    title VARCHAR(100) NOT NULL,
    category INTEGER NOT NULL,
    description TEXT,
    employment_type INTEGER,
    salary_min INTEGER,
    salary_max INTEGER,
    location_city VARCHAR(50),
    status INTEGER DEFAULT 1,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 简历表 (resumes)
```sql
CREATE TABLE resumes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    title VARCHAR(100),
    education_background JSONB,
    work_experience JSONB,
    skills JSONB,
    expected_salary_min INTEGER,
    expected_salary_max INTEGER,
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.3 房产模块

#### 房源表 (houses)
```sql
CREATE TABLE houses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    house_type INTEGER NOT NULL,
    transaction_type INTEGER NOT NULL,
    city VARCHAR(50) NOT NULL,
    district VARCHAR(50) NOT NULL,
    area DECIMAL(8,2),
    rooms INTEGER,
    total_price DECIMAL(12,2),
    unit_price DECIMAL(8,2),
    description TEXT,
    images JSONB,
    status INTEGER DEFAULT 1,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.4 零工模块

#### 零工任务表 (gigs)
```sql
CREATE TABLE gigs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    category INTEGER NOT NULL,
    description TEXT,
    work_date DATE,
    city VARCHAR(50),
    payment_amount DECIMAL(8,2),
    required_people INTEGER DEFAULT 1,
    status INTEGER DEFAULT 1,
    applicant_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 零工申请表 (gig_applications)
```sql
CREATE TABLE gig_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    gig_id UUID NOT NULL REFERENCES gigs(id),
    user_id UUID NOT NULL REFERENCES users(id),
    message TEXT,
    status INTEGER DEFAULT 1,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(gig_id, user_id)
);
```

### 2.5 交友模块

#### 交友资料表 (dating_profiles)
```sql
CREATE TABLE dating_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    photos JSONB,
    bio TEXT,
    interests JSONB,
    height INTEGER,
    preferred_age_min INTEGER,
    preferred_age_max INTEGER,
    like_count INTEGER DEFAULT 0,
    match_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 动态帖子表 (posts)
```sql
CREATE TABLE posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    content TEXT NOT NULL,
    images JSONB,
    topics JSONB,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    visibility INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 用户匹配表 (user_matches)
```sql
CREATE TABLE user_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    target_user_id UUID NOT NULL REFERENCES users(id),
    action_type INTEGER NOT NULL,
    is_mutual BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_user_id)
);
```

### 2.6 消息模块

#### 会话表 (conversations)
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type INTEGER NOT NULL,
    name VARCHAR(100),
    creator_id UUID REFERENCES users(id),
    last_message_time TIMESTAMP,
    member_count INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 消息表 (messages)
```sql
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id),
    sender_id UUID NOT NULL REFERENCES users(id),
    message_type INTEGER NOT NULL,
    content TEXT,
    attachment_url VARCHAR(500),
    reply_to_id UUID REFERENCES messages(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.7 支付模块

#### 钱包表 (wallets)
```sql
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    balance BIGINT DEFAULT 0,
    frozen_amount BIGINT DEFAULT 0,
    points INTEGER DEFAULT 0,
    pay_password VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    UNIQUE(user_id)
);
```

#### 订单表 (orders)
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    order_type INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    amount BIGINT NOT NULL,
    actual_amount BIGINT NOT NULL,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 支付记录表 (payments)
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_no VARCHAR(32) UNIQUE NOT NULL,
    order_id UUID NOT NULL REFERENCES orders(id),
    user_id UUID NOT NULL REFERENCES users(id),
    payment_method INTEGER NOT NULL,
    amount BIGINT NOT NULL,
    status INTEGER DEFAULT 1,
    third_party_order_no VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 三、索引优化

### 3.1 核心索引
```sql
-- 用户查询
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_status ON users(status, created_at);

-- 职位搜索
CREATE INDEX idx_jobs_category_city ON jobs(category, location_city, status);
CREATE INDEX idx_jobs_salary ON jobs(salary_min, salary_max);

-- 房源搜索
CREATE INDEX idx_houses_type_city ON houses(house_type, city, status);
CREATE INDEX idx_houses_price ON houses(total_price, unit_price);

-- 消息查询
CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at);

-- 支付查询
CREATE INDEX idx_payments_user ON payments(user_id, created_at);
```

### 3.2 性能优化
- **分区表**：按时间分区大表（消息、交易记录）
- **读写分离**：主库写入，从库查询
- **缓存策略**：热点数据Redis缓存
- **连接池**：数据库连接池优化

## 四、数据字典

### 4.1 状态码定义
```yaml
用户状态:
  1: 正常
  2: 禁用
  3: 删除

认证状态:
  0: 未认证
  1: 手机认证
  2: 实名认证
  3: 企业认证

订单状态:
  1: 待支付
  2: 已支付
  3: 已完成
  4: 已取消
  5: 已退款

支付方式:
  1: 余额支付
  2: 微信支付
  3: 支付宝
  4: 银行卡
```

### 4.2 业务分类
```yaml
职位分类:
  1: 互联网/IT
  2: 金融
  3: 教育培训
  4: 医疗健康
  5: 房地产/建筑
  6: 制造业
  7: 餐饮酒店
  8: 零售/电商

房产类型:
  1: 二手房
  2: 新房
  3: 租房
  4: 商业地产

零工分类:
  1: 体力劳动
  2: 技能服务
  3: 创意设计
  4: 生活服务
```

这个数据库设计方案采用模块化设计，支持水平扩展，能够满足本地宝平台的业务需求。 