# 本地宝后端架构设计方案（单体架构）

## 一、架构设计理念

### 1.1 项目定位
- **项目规模**：中小型本地生活服务平台
- **开发模式**：个人全栈开发者
- **架构选择**：单体架构 + 模块化设计
- **发展策略**：快速迭代，后期可平滑演进

### 1.2 技术栈选型
- **开发语言**：Go 1.21+
- **Web框架**：Gin v1.9+
- **ORM框架**：GORM v1.25+
- **数据库**：PostgreSQL 15+
- **缓存**：Redis 7.0+
- **消息队列**：RabbitMQ 3.12+（可选，初期可用内存队列）
- **日志**：Zerolog
- **实时通讯**：WebSocket
- **认证**：JWT
- **文件存储**：七牛云 Kodo
- **短信服务**：阿里云短信
- **支付集成**：微信支付 + 支付宝

### 1.3 架构原则
- **简单优先**：避免过度设计，关注业务价值
- **模块化**：按业务模块组织代码，便于维护
- **可扩展**：预留扩展点，支持未来增长
- **快速开发**：减少配置和模板代码

## 二、单体架构设计

### 2.1 整体架构图
```mermaid
graph TB
    Client[前端应用] --> Gateway[Nginx/负载均衡]
    Gateway --> App[Gin Web应用]
    
    App --> Auth[认证模块]
    App --> User[用户模块]
    App --> Job[招聘模块]
    App --> House[房产模块]
    App --> Gig[零工模块]
    App --> Dating[交友模块]
    App --> Message[消息模块]
    App --> Payment[支付模块]
    App --> File[文件模块]
    
    App --> DB[(PostgreSQL)]
    App --> Cache[(Redis)]
    App --> Queue[消息队列]
    App --> Storage[七牛云存储]
    
    App --> SMS[短信服务]
    App --> WechatPay[微信支付]
    App --> Alipay[支付宝]
```

### 2.2 分层架构
```
┌─────────────────┐
│   Handlers      │ ← HTTP路由处理层
├─────────────────┤
│   Services      │ ← 业务逻辑层
├─────────────────┤
│   Repositories  │ ← 数据访问层
├─────────────────┤
│   Models        │ ← 数据模型层
└─────────────────┘
```

### 2.3 核心模块设计

#### 2.3.1 认证与授权模块
```go
type AuthService struct {
    userRepo    repository.UserRepository
    jwtManager  *jwt.Manager
    redisClient *redis.Client
}

// 功能：
// - 用户注册/登录
// - JWT Token管理
// - 权限验证
// - 会话管理
```

#### 2.3.2 用户管理模块
```go
type UserService struct {
    userRepo    repository.UserRepository
    fileService service.FileService
    cache       cache.Cache
}

// 功能：
// - 用户资料管理
// - VIP会员管理
// - 实名认证
// - 用户权限
```

#### 2.3.3 业务模块（招聘/房产/零工/交友）
```go
type JobService struct {
    jobRepo     repository.JobRepository
    companyRepo repository.CompanyRepository
    cache       cache.Cache
    search      search.Engine
}

// 统一的业务模块结构：
// - CRUD操作
// - 搜索筛选
// - 数据统计
// - 缓存管理
```

#### 2.3.4 消息通讯模块
```go
type MessageService struct {
    messageRepo repository.MessageRepository
    wsManager   websocket.Manager
    queue       queue.Queue
}

// 功能：
// - WebSocket连接管理
// - 实时消息推送
// - 离线消息存储
// - 消息队列处理
```

## 三、数据库设计

### 3.1 单库设计
- **优势**：简化部署、事务一致性、开发便利
- **劣势**：扩展限制（但对中小型项目足够）
- **策略**：合理索引 + 数据归档 + 读写分离（后期）

### 3.2 表结构规划
```sql
-- 核心业务表
users               -- 用户基础信息
user_profiles       -- 用户详细资料
vip_memberships     -- VIP会员

companies           -- 企业信息
jobs                -- 职位信息
resumes             -- 简历信息
job_applications    -- 求职申请

houses              -- 房源信息
house_favorites     -- 房源收藏

gigs                -- 零工任务
gig_applications    -- 零工申请

dating_profiles     -- 交友资料
user_matches        -- 用户匹配
posts               -- 动态帖子
post_comments       -- 帖子评论

conversations       -- 会话
messages            -- 消息

wallets             -- 钱包
orders              -- 订单
payments            -- 支付记录

-- 系统表
areas               -- 地区数据
file_uploads        -- 文件管理
system_configs      -- 系统配置
```

### 3.3 缓存策略
```yaml
Redis使用场景:
  - 用户会话: "session:{token}"
  - 热门数据: "hot:jobs:{city}"
  - 计数器: "count:views:{id}"
  - 搜索缓存: "search:{keyword}:{params}"
  - 验证码: "sms:{phone}"
  - 限流: "rate:limit:{user_id}"
```

## 四、API设计

### 4.1 RESTful API
```
/api/v1/auth/*           # 认证相关
/api/v1/users/*          # 用户管理
/api/v1/jobs/*           # 招聘相关
/api/v1/houses/*         # 房产相关
/api/v1/gigs/*           # 零工相关
/api/v1/dating/*         # 交友相关
/api/v1/messages/*       # 消息相关
/api/v1/payments/*       # 支付相关
/api/v1/files/*          # 文件上传
/ws/*                    # WebSocket
```

### 4.2 中间件设计
```go
// 核心中间件
- CORS          // 跨域处理
- Logger        // 请求日志
- Recovery      // 错误恢复
- RateLimit     // 频率限制
- Auth          // 身份认证
- Permission    // 权限验证
- Cache         // 响应缓存
```

## 五、核心功能实现

### 5.1 实时消息系统
```go
type WebSocketManager struct {
    clients    map[string]*websocket.Conn
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
}

// 消息处理流程：
// 1. WebSocket连接管理
// 2. 消息路由分发
// 3. 离线消息存储
// 4. 消息状态同步
```

### 5.2 搜索系统
```go
type SearchService struct {
    db    *gorm.DB
    cache *redis.Client
}

// 搜索策略：
// 1. 数据库全文搜索（PostgreSQL）
// 2. Redis缓存热门搜索
// 3. 搜索结果排序和分页
// 4. 搜索历史记录
```

### 5.3 支付系统
```go
type PaymentService struct {
    wechatPay *wechat.Client
    alipay    *alipay.Client
    orderRepo repository.OrderRepository
}

// 支付流程：
// 1. 订单创建
// 2. 调用支付接口
// 3. 处理回调通知
// 4. 更新订单状态
// 5. 业务处理（VIP开通等）
```

## 六、部署方案

### 6.1 开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENV=development
    volumes:
      - .:/app
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: bdb_dev
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "6379:6379"
```

### 6.2 生产环境
```bash
# 构建镜像
docker build -t bdb-backend:latest .

# 运行容器
docker run -d \
  --name bdb-backend \
  -p 8080:8080 \
  -e ENV=production \
  -e DATABASE_URL=postgres://... \
  -e REDIS_URL=redis://... \
  bdb-backend:latest
```

## 七、性能优化

### 7.1 数据库优化
- **索引优化**：为常用查询添加复合索引
- **查询优化**：使用EXPLAIN分析慢查询
- **连接池**：合理配置数据库连接池
- **读写分离**：后期可配置主从复制

### 7.2 缓存优化
- **热点数据**：用户信息、热门内容缓存
- **查询缓存**：搜索结果、列表数据缓存
- **会话缓存**：JWT token、用户会话缓存
- **计数缓存**：点赞数、浏览数等计数器

### 7.3 应用优化
- **并发控制**：Goroutine池化
- **内存管理**：及时释放大对象
- **日志优化**：异步日志写入
- **监控告警**：关键指标监控

## 八、安全设计

### 8.1 API安全
- **HTTPS**：全站SSL加密
- **JWT认证**：无状态身份验证
- **参数验证**：严格的输入验证
- **限流防护**：API调用频率限制

### 8.2 数据安全
- **密码加密**：bcrypt哈希加密
- **敏感信息**：数据脱敏处理
- **SQL注入**：使用参数化查询
- **XSS防护**：输出内容转义

## 九、监控运维

### 9.1 日志管理
```go
// 结构化日志
log.Info().
    Str("user_id", userID).
    Str("action", "login").
    Dur("duration", time.Since(start)).
    Msg("User login successful")
```

### 9.2 健康检查
```go
// /health 端点
func HealthCheck(c *gin.Context) {
    status := map[string]string{
        "database": checkDatabase(),
        "redis":    checkRedis(),
        "storage":  checkStorage(),
    }
    c.JSON(200, status)
}
```

## 十、扩展规划

### 10.1 短期优化
- **性能调优**：优化热点查询
- **功能完善**：补充业务功能
- **用户体验**：优化API响应时间

### 10.2 长期演进
- **服务拆分**：按业务模块拆分微服务
- **数据分片**：水平分库分表
- **云原生**：容器化部署、服务网格

这个单体架构设计简洁实用，适合中小型项目快速开发，同时保留了未来扩展的可能性。 