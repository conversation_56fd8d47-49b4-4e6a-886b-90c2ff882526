/**
 * 渐变背景样式库
 * 基于优秀金融应用界面设计分析
 * 适用于现代化移动应用界面
 */

// ==================== 颜色变量定义 ====================
$gradient-purple-50: #faf5ff;
$gradient-purple-100: #f3e8ff;
$gradient-purple-200: #e9d5ff;
$gradient-pink-50: #fdf2f8;
$gradient-pink-100: #fce7f3;
$gradient-orange-50: #fef7f0;
$gradient-orange-100: #fed7aa;
$gradient-slate-50: #f8fafc;
$gradient-slate-100: #f1f5f9;

// ==================== 基础渐变类 ====================

/* 方案1：基础对角线渐变 - 适用于简洁背景 */
.gradient-basic {
  background: linear-gradient(135deg, #{$gradient-purple-100} 0%, #{$gradient-pink-50} 50%, #{$gradient-orange-50} 100%);
}

/* 方案2：多层次渐变（推荐） - 适用于主要界面背景 */
.gradient-recommended {
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 25%, #{$gradient-pink-50} 70%, #{$gradient-orange-50} 100%);
}

/* 方案3：柔和紫色调 - 适用于VIP页面、会员中心 */
.gradient-purple {
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 20%, #{$gradient-pink-50} 60%, #{$gradient-orange-50} 100%);
}

/* 方案4：RGBA透明度控制 - 适用于需要透明度的场景 */
.gradient-rgba {
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 1) 0%,
    rgba(241, 245, 249, 1) 25%,
    rgba(253, 242, 248, 1) 65%,
    rgba(254, 247, 240, 1) 100%
  );
}

/* 方案5：CSS变量版本 - 适用于主题切换 */
.gradient-variables {
  --gradient-start: #{$gradient-purple-50};
  --gradient-mid1: #{$gradient-purple-100};
  --gradient-mid2: #{$gradient-pink-50};
  --gradient-end: #{$gradient-orange-50};
  
  background: linear-gradient(135deg, 
    var(--gradient-start) 0%, 
    var(--gradient-mid1) 25%, 
    var(--gradient-mid2) 70%, 
    var(--gradient-end) 100%
  );
}

// ==================== 应用场景专用类 ====================

/* 首页背景 */
.home-background {
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 25%, #{$gradient-pink-50} 70%, #{$gradient-orange-50} 100%);
  min-height: 100vh;
  position: relative;
}

/* 钱包/余额页面背景 */
.wallet-background {
  background: linear-gradient(135deg, #{$gradient-purple-100} 0%, #{$gradient-pink-50} 50%, #{$gradient-orange-50} 100%);
  padding: 40rpx 0;
  border-radius: 0 0 32rpx 32rpx;
}

/* VIP/会员页面背景 */
.vip-background {
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 20%, #{$gradient-pink-50} 60%, #{$gradient-orange-50} 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
  }
}

/* 登录/注册页面背景 */
.auth-background {
  background: linear-gradient(135deg, 
    rgba(250, 245, 255, 0.95) 0%, 
    rgba(243, 232, 255, 0.95) 25%, 
    rgba(253, 242, 248, 0.95) 70%, 
    rgba(254, 247, 240, 0.95) 100%
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 卡片背景 */
.card-gradient {
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 25%, #{$gradient-pink-50} 70%, #{$gradient-orange-50} 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

/* 按钮渐变背景 */
.button-gradient {
  background: linear-gradient(135deg, #{$gradient-purple-100} 0%, #{$gradient-pink-100} 100%);
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  color: #6b46c1;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover, &:active {
    background: linear-gradient(135deg, #{$gradient-purple-200} 0%, #{$gradient-pink-100} 100%);
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 25rpx rgba(107, 70, 193, 0.25);
  }
}

// ==================== 容器类 ====================

/* 全屏渐变容器 */
.gradient-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 25%, #{$gradient-pink-50} 70%, #{$gradient-orange-50} 100%);
  background-attachment: fixed;
  background-size: cover;
  position: relative;
}

/* 页面内容容器 */
.gradient-content {
  background: linear-gradient(135deg, #{$gradient-purple-50} 0%, #{$gradient-purple-100} 25%, #{$gradient-pink-50} 70%, #{$gradient-orange-50} 100%);
  min-height: calc(100vh - 88rpx); // 减去导航栏高度
  padding: 32rpx;
}

/* 模态框背景 */
.modal-gradient {
  background: linear-gradient(135deg, 
    rgba(250, 245, 255, 0.98) 0%, 
    rgba(243, 232, 255, 0.98) 25%, 
    rgba(253, 242, 248, 0.98) 70%, 
    rgba(254, 247, 240, 0.98) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// ==================== 动画效果 ====================

@keyframes shimmer {
  0%, 100% {
    opacity: 0.3;
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    opacity: 0.8;
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 动态渐变效果 */
.gradient-animated {
  background: linear-gradient(135deg, #{$gradient-purple-50}, #{$gradient-purple-100}, #{$gradient-pink-50}, #{$gradient-orange-50});
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

// ==================== 响应式适配 ====================

/* 小屏幕适配 */
@media (max-width: 750rpx) {
  .gradient-container {
    background-attachment: scroll; // 移动端性能优化
  }
  
  .card-gradient {
    margin: 16rpx;
    padding: 32rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .gradient-dark {
    background: linear-gradient(135deg, 
      rgba(30, 27, 75, 0.9) 0%, 
      rgba(45, 35, 85, 0.9) 25%, 
      rgba(60, 45, 95, 0.9) 70%, 
      rgba(75, 55, 105, 0.9) 100%
    );
  }
}

// ==================== 工具类 ====================

/* 渐变文字 */
.text-gradient {
  background: linear-gradient(135deg, #6b46c1 0%, #ec4899 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

/* 渐变边框 */
.border-gradient {
  position: relative;
  background: white;
  border-radius: 16rpx;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2rpx;
    background: linear-gradient(135deg, #{$gradient-purple-100}, #{$gradient-pink-100}, #{$gradient-orange-100});
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
  }
}
