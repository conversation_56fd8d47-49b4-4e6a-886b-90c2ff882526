<template>
  <view class="gradient-examples">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title text-gradient">渐变背景示例</text>
      <text class="page-subtitle">基于优秀金融应用界面设计</text>
    </view>

    <!-- 基础渐变示例 -->
    <view class="section">
      <text class="section-title">基础渐变方案</text>
      
      <view class="gradient-item gradient-basic">
        <text class="gradient-label">基础对角线渐变</text>
        <text class="gradient-desc">适用于简洁背景效果</text>
      </view>
      
      <view class="gradient-item gradient-recommended">
        <text class="gradient-label">多层次渐变（推荐）</text>
        <text class="gradient-desc">适用于主要界面背景</text>
      </view>
      
      <view class="gradient-item gradient-purple">
        <text class="gradient-label">柔和紫色调</text>
        <text class="gradient-desc">适用于VIP页面、会员中心</text>
      </view>
    </view>

    <!-- 应用场景示例 -->
    <view class="section">
      <text class="section-title">应用场景示例</text>
      
      <!-- 钱包卡片示例 -->
      <view class="wallet-card card-gradient">
        <view class="wallet-header">
          <text class="wallet-title">我的钱包</text>
          <text class="wallet-balance">¥1,234.56</text>
        </view>
        <view class="wallet-actions">
          <view class="action-btn button-gradient" @tap="handleAction('recharge')">
            <text class="i-carbon-add"></text>
            <text>充值</text>
          </view>
          <view class="action-btn button-gradient" @tap="handleAction('withdraw')">
            <text class="i-carbon-subtract"></text>
            <text>提现</text>
          </view>
        </view>
      </view>

      <!-- VIP会员卡示例 -->
      <view class="vip-card vip-background">
        <view class="vip-header">
          <text class="vip-title">VIP会员</text>
          <text class="vip-level">黄金会员</text>
        </view>
        <view class="vip-benefits">
          <text class="benefit-item">专属客服</text>
          <text class="benefit-item">优先处理</text>
          <text class="benefit-item">专属优惠</text>
        </view>
      </view>

      <!-- 登录表单示例 -->
      <view class="auth-form auth-background">
        <text class="form-title">欢迎登录</text>
        <view class="form-group">
          <input class="form-input" placeholder="请输入手机号" />
        </view>
        <view class="form-group">
          <input class="form-input" placeholder="请输入密码" type="password" />
        </view>
        <view class="login-btn button-gradient" @tap="handleLogin">
          <text>登录</text>
        </view>
      </view>
    </view>

    <!-- 动画效果示例 */
    <view class="section">
      <text class="section-title">动画效果</text>
      
      <view class="gradient-item gradient-animated">
        <text class="gradient-label">动态渐变效果</text>
        <text class="gradient-desc">背景色彩流动动画</text>
      </view>
      
      <view class="shimmer-card vip-background">
        <text class="shimmer-text">光泽动画效果</text>
      </view>
    </view>

    <!-- 边框渐变示例 -->
    <view class="section">
      <text class="section-title">特殊效果</text>
      
      <view class="border-gradient-card border-gradient">
        <text class="card-title">渐变边框卡片</text>
        <text class="card-content">这是一个带有渐变边框的卡片示例</text>
      </view>
      
      <view class="modal-example modal-gradient">
        <text class="modal-title">模态框效果</text>
        <text class="modal-content">毛玻璃背景 + 渐变效果</text>
      </view>
    </view>

    <!-- 切换主题按钮 -->
    <view class="theme-switcher">
      <view 
        class="theme-btn" 
        :class="{ active: currentTheme === 'light' }"
        @tap="switchTheme('light')"
      >
        <text>浅色主题</text>
      </view>
      <view 
        class="theme-btn" 
        :class="{ active: currentTheme === 'dark' }"
        @tap="switchTheme('dark')"
      >
        <text>深色主题</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 当前主题
const currentTheme = ref<'light' | 'dark'>('light')

// 切换主题
const switchTheme = (theme: 'light' | 'dark') => {
  currentTheme.value = theme
  // 这里可以添加主题切换逻辑
  console.log('切换主题:', theme)
}

// 处理钱包操作
const handleAction = (action: string) => {
  console.log('钱包操作:', action)
  uni.showToast({
    title: `${action === 'recharge' ? '充值' : '提现'}功能`,
    icon: 'none'
  })
}

// 处理登录
const handleLogin = () => {
  console.log('登录操作')
  uni.showToast({
    title: '登录功能演示',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
// 引入渐变背景样式
@import './gradient-backgrounds.scss';

.gradient-examples {
  min-height: 100vh;
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 25%, #fdf2f8 70%, #fef7f0 100%);
  padding: 32rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .page-title {
    font-size: 48rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 16rpx;
  }
  
  .page-subtitle {
    font-size: 28rpx;
    color: #6b7280;
  }
}

.section {
  margin-bottom: 60rpx;
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #374151;
    margin-bottom: 32rpx;
    display: block;
  }
}

.gradient-item {
  padding: 40rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  
  .gradient-label {
    font-size: 32rpx;
    font-weight: 600;
    color: #374151;
    display: block;
    margin-bottom: 8rpx;
  }
  
  .gradient-desc {
    font-size: 26rpx;
    color: #6b7280;
  }
}

// 钱包卡片样式
.wallet-card {
  margin-bottom: 32rpx;
  
  .wallet-header {
    margin-bottom: 32rpx;
    
    .wallet-title {
      font-size: 28rpx;
      color: #6b7280;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .wallet-balance {
      font-size: 48rpx;
      font-weight: bold;
      color: #374151;
    }
  }
  
  .wallet-actions {
    display: flex;
    gap: 24rpx;
    
    .action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      font-size: 28rpx;
    }
  }
}

// VIP卡片样式
.vip-card {
  padding: 40rpx;
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  
  .vip-header {
    margin-bottom: 24rpx;
    
    .vip-title {
      font-size: 28rpx;
      color: #6b7280;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .vip-level {
      font-size: 36rpx;
      font-weight: bold;
      color: #d97706;
    }
  }
  
  .vip-benefits {
    display: flex;
    gap: 16rpx;
    
    .benefit-item {
      background: rgba(255, 255, 255, 0.3);
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #374151;
    }
  }
}

// 登录表单样式
.auth-form {
  padding: 40rpx;
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  
  .form-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #374151;
    text-align: center;
    display: block;
    margin-bottom: 40rpx;
  }
  
  .form-group {
    margin-bottom: 24rpx;
    
    .form-input {
      width: 100%;
      padding: 24rpx;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12rpx;
      font-size: 28rpx;
    }
  }
  
  .login-btn {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    margin-top: 32rpx;
  }
}

// 特殊效果样式
.shimmer-card {
  padding: 40rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  
  .shimmer-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #374151;
  }
}

.border-gradient-card {
  padding: 40rpx;
  margin-bottom: 24rpx;
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #374151;
    display: block;
    margin-bottom: 16rpx;
  }
  
  .card-content {
    font-size: 28rpx;
    color: #6b7280;
  }
}

.modal-example {
  padding: 40rpx;
  margin-bottom: 24rpx;
  text-align: center;
  
  .modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #374151;
    display: block;
    margin-bottom: 16rpx;
  }
  
  .modal-content {
    font-size: 28rpx;
    color: #6b7280;
  }
}

// 主题切换器
.theme-switcher {
  display: flex;
  gap: 16rpx;
  margin-top: 60rpx;
  
  .theme-btn {
    flex: 1;
    padding: 24rpx;
    background: rgba(255, 255, 255, 0.6);
    border: 2px solid transparent;
    border-radius: 12rpx;
    text-align: center;
    font-size: 28rpx;
    color: #374151;
    transition: all 0.3s ease;
    
    &.active {
      background: linear-gradient(135deg, #f3e8ff 0%, #fdf2f8 100%);
      border-color: #a855f7;
      color: #7c3aed;
    }
  }
}
</style>
