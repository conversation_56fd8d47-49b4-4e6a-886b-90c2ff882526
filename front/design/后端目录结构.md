# 后端服务目录结构设计

## 项目根目录
```
bdb-server/
├── cmd/                  # 服务入口文件
├── config/               # 配置文件
├── internal/             # 私有应用代码
├── pkg/                  # 公共库代码
├── scripts/              # 部署和运维脚本
├── api/                  # API定义文件
├── deployments/          # 部署配置
├── docs/                 # 项目文档
├── test/                 # 测试代码
├── go.mod                # Go模块定义
└── go.sum                # 依赖校验文件
```

## 详细目录说明

### 1. cmd/ - 服务入口
```
cmd/
├── api-server/           # 主API服务入口
│   └── main.go
├── job-worker/           # 异步任务处理
│   └── main.go
└── websocket-server/     # WebSocket服务
    └── main.go
```

### 2. config/ - 配置管理
```
config/
├── config.go             # 配置结构定义
├── dev.yaml              # 开发环境配置
├── test.yaml             # 测试环境配置
└── prod.yaml             # 生产环境配置
```

### 3. internal/ - 核心业务逻辑
```
internal/
├── app/                  # 应用层
│   ├── handler/          # HTTP请求处理
│   ├── middleware/       # 中间件
│   └── router/           # 路由定义
├── domain/               # 领域模型
│   ├── user/             # 用户领域
│   ├── content/          # 内容领域
│   ├── social/           # 社交领域
│   └── payment/          # 支付领域
├── service/              # 业务服务
│   ├── user_service.go
│   ├── post_service.go
│   ├── gig_service.go
│   └── payment_service.go
├── repository/           # 数据仓库
│   ├── user_repo.go
│   ├── post_repo.go
│   ├── cache/            # Redis缓存
│   └── db/               # 数据库操作
└── infrastructure/       # 基础设施
    ├── storage/          # 文件存储
    ├── sms/              # 短信服务
    ├── wechat/           # 微信服务
    └── payment/          # 支付网关
```

### 4. pkg/ - 公共库
```
pkg/
├── logger/               # 日志封装
├── util/                 # 工具函数
│   ├── timeutil.go
│   ├── stringutil.go
│   └── crypto.go
├── errors/               # 错误处理
├── validator/            # 参数验证
└── queue/                # 消息队列封装
```

### 5. scripts/ - 运维脚本
```
scripts/
├── deploy.sh             # 部署脚本
├── migrate.sh            # 数据库迁移
└── backup.sh             # 数据备份
```

### 6. api/ - API定义
```
api/
├── openapi/              # OpenAPI文档
│   └── v1.yaml
└── proto/                # gRPC协议定义
    └── v1/
        ├── user.proto
        ├── post.proto
        └── payment.proto
```

### 7. deployments/ - 部署配置
```
deployments/
├── docker/               # Docker配置
│   ├── api.Dockerfile
│   └── worker.Dockerfile
└── kubernetes/           # K8s配置
    ├── deployment.yaml
    └── service.yaml
```

## 目录设计原则
1. **分层架构**：清晰划分应用层、领域层、基础设施层
2. **依赖倒置**：高层模块不依赖低层实现细节
3. **单一职责**：每个目录/文件只负责一个功能
4. **可测试性**：业务逻辑与基础设施解耦
5. **可扩展性**：模块化设计支持功能扩展

## 代码组织规范
1. 使用Go标准项目布局
2. 遵循Clean Architecture原则
3. 接口定义在调用方
4. 领域模型独立于技术实现
5. 配置管理支持多环境
