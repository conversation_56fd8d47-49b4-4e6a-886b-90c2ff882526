# 后端开发规范与指南 (Golang + Gin)

这是一个基于Golang的本地生活服务平台后端项目，使用分层架构设计，遵循Clean Architecture原则。

## 🚀 项目概述

本项目是一个综合性本地生活服务平台后端，集成社区动态、招聘求职、零工市场、同城交友和房产服务等核心功能。

### 技术栈
- **语言**: Golang 1.21+
- **Web框架**: Gin
- **ORM**: GORM v2
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+
- **实时通信**: Centrifugo（WebSocket/聊天/通知）
- **支付**: 微信支付 v3
- **短信服务**: 阿里云短信/腾讯云短信
- **JWT认证**: golang-jwt
- **参数验证**: validator/v10
- **容器化**: Docker, Docker Compose
- **日志**: Zerolog
- **API文档**: Swagger
- **配置管理**: Viper

## 📁 项目架构

### 分层架构设计
```
api/        - API层：协议适配 (HTTP handlers, middleware, router)
internal/   - 核心业务逻辑 (不对外暴露)
  ├── biz/     - 业务逻辑层 (纯业务逻辑，无外部依赖)
  ├── data/    - 数据访问层 (Repository实现)
  └── service/ - 服务层 (组装biz，对handler暴露)
pkg/        - 公共库 (可复用组件)
cmd/        - 程序入口
config/     - 配置文件
```

### 层次职责
- **API层**: 处理HTTP请求/响应，参数验证，错误处理
- **Service层**: 组装业务逻辑，事务管理，对外暴露服务接口
- **Biz层**: 纯业务逻辑，领域规则，不依赖具体技术实现
- **Data层**: 数据持久化，缓存操作，外部服务调用

## 🔧 开发规范

### 命名规范
- **包名**: 小写，简短，有意义 (`user`, `post`, `auth`)
- **文件名**: 小写下划线 (`user_handler.go`, `post_service.go`)
- **结构体**: 大写驼峰 (`UserInfo`, `PostDetail`)
- **接口**: 大写驼峰，以`er`结尾 (`UserRepository`, `PostService`)
- **变量/函数**: 小写驼峰 (`userID`, `getUserInfo`)
- **常量**: 大写下划线 (`USER_STATUS_ACTIVE`, `MAX_RETRY_COUNT`)

### 代码结构规范

#### Handler层示例
```go
// api/http/handler/user.go
type UserHandler struct {
    userSvc service.UserService
    log     *zerolog.Logger
}

func NewUserHandler(userSvc service.UserService, log *zerolog.Logger) *UserHandler {
    return &UserHandler{
        userSvc: userSvc,
        log:     log,
    }
}

func (h *UserHandler) GetUserInfo(c *gin.Context) {
    userID := c.GetInt64("user_id")
    
    user, err := h.userSvc.GetUserByID(c.Request.Context(), userID)
    if err != nil {
        h.log.Error().Err(err).Int64("user_id", userID).Msg("get user info failed")
        response.Error(c, err)
        return
    }
    
    response.Success(c, user)
}
```

#### Service层示例
```go
// internal/service/user_svc.go
type UserService interface {
    GetUserByID(ctx context.Context, userID int64) (*biz.User, error)
    CreateUser(ctx context.Context, req *biz.CreateUserRequest) (*biz.User, error)
}

type userService struct {
    userBiz biz.UserBiz
    log     *zerolog.Logger
}

func NewUserService(userBiz biz.UserBiz, log *zerolog.Logger) UserService {
    return &userService{
        userBiz: userBiz,
        log:     log,
    }
}

func (s *userService) GetUserByID(ctx context.Context, userID int64) (*biz.User, error) {
    return s.userBiz.GetUserByID(ctx, userID)
}
```

#### Biz层示例
```go
// internal/biz/user_biz.go
type User struct {
    ID       int64     `json:"id"`
    Nickname string    `json:"nickname"`
    Phone    string    `json:"phone"`
    CreatedAt time.Time `json:"created_at"`
}

type UserBiz interface {
    GetUserByID(ctx context.Context, userID int64) (*User, error)
    CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
}

type userBiz struct {
    userRepo data.UserRepository
    log      *zerolog.Logger
}

func NewUserBiz(userRepo data.UserRepository, log *zerolog.Logger) UserBiz {
    return &userBiz{
        userRepo: userRepo,
        log:      log,
    }
}

func (b *userBiz) GetUserByID(ctx context.Context, userID int64) (*User, error) {
    user, err := b.userRepo.GetByID(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    return &User{
        ID:       user.ID,
        Nickname: user.Nickname,
        Phone:    user.Phone,
        CreatedAt: user.CreatedAt,
    }, nil
}
```

#### Data层示例
```go
// internal/data/model/user.go
type User struct {
    ID        int64     `gorm:"primaryKey" json:"id"`
    OpenID    string    `gorm:"uniqueIndex;size:100" json:"open_id"`
    Nickname  string    `gorm:"size:50" json:"nickname"`
    Phone     string    `gorm:"uniqueIndex;size:20" json:"phone"`
    Status    int       `gorm:"default:0" json:"status"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

// internal/data/user_repo.go
type UserRepository interface {
    GetByID(ctx context.Context, id int64) (*model.User, error)
    GetByPhone(ctx context.Context, phone string) (*model.User, error)
    Create(ctx context.Context, user *model.User) error
    Update(ctx context.Context, user *model.User) error
}

type userRepository struct {
    db  *gorm.DB
    log *zerolog.Logger
}

func NewUserRepository(db *gorm.DB, log *zerolog.Logger) UserRepository {
    return &userRepository{db: db, log: log}
}

func (r *userRepository) GetByID(ctx context.Context, id int64) (*model.User, error) {
    var user model.User
    err := r.db.WithContext(ctx).Where("id = ?", id).First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, errors.New("user not found")
        }
        return nil, err
    }
    return &user, nil
}
```

### 错误处理规范

#### 统一错误定义
```go
// pkg/errors/errors.go
type AppError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Detail  string `json:"detail,omitempty"`
}

func (e *AppError) Error() string {
    return e.Message
}

var (
    ErrUserNotFound     = &AppError{Code: 10001, Message: "用户不存在"}
    ErrInvalidParams    = &AppError{Code: 10002, Message: "参数错误"}
    ErrUnauthorized     = &AppError{Code: 10003, Message: "未授权"}
    ErrInternalServer   = &AppError{Code: 10004, Message: "服务器内部错误"}
)
```

#### 统一响应格式
```go
// api/http/response/response.go
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func Success(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, Response{
        Code:    0,
        Message: "success",
        Data:    data,
    })
}

func Error(c *gin.Context, err error) {
    var appErr *errors.AppError
    if errors.As(err, &appErr) {
        c.JSON(http.StatusOK, Response{
            Code:    appErr.Code,
            Message: appErr.Message,
        })
        return
    }
    
    c.JSON(http.StatusInternalServerError, Response{
        Code:    -1,
        Message: "服务器内部错误",
    })
}
```

### 数据库操作规范

#### GORM模型规范
```go
// 使用指针类型用于可选字段
type User struct {
    ID        int64      `gorm:"primaryKey" json:"id"`
    Nickname  string     `gorm:"size:50;not null" json:"nickname"`
    Avatar    *string    `gorm:"size:200" json:"avatar,omitempty"`
    Status    int        `gorm:"default:0;not null" json:"status"`
    CreatedAt time.Time  `gorm:"not null" json:"created_at"`
    UpdatedAt time.Time  `gorm:"not null" json:"updated_at"`
    DeletedAt *time.Time `gorm:"index" json:"-"`
}

// 表名定义
func (User) TableName() string {
    return "users"
}
```

#### 事务处理
```go
func (s *userService) CreateUserWithProfile(ctx context.Context, req *CreateUserRequest) error {
    return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // 创建用户
        user := &model.User{
            Nickname: req.Nickname,
            Phone:    req.Phone,
        }
        if err := tx.Create(user).Error; err != nil {
            return err
        }
        
        // 创建用户资料
        profile := &model.UserProfile{
            UserID:   user.ID,
            RealName: req.RealName,
        }
        return tx.Create(profile).Error
    })
}
```

### 缓存使用规范

#### Redis操作封装
```go
// pkg/cache/redis.go
type Cache interface {
    Get(ctx context.Context, key string) (string, error)
    Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
    Del(ctx context.Context, keys ...string) error
}

type redisCache struct {
    client *redis.Client
}

func (c *redisCache) Get(ctx context.Context, key string) (string, error) {
    return c.client.Get(ctx, key).Result()
}

// 缓存键命名规范
const (
    UserCachePrefix    = "user:"
    SessionCachePrefix = "session:"
    CodeCachePrefix    = "code:"
)

func UserCacheKey(userID int64) string {
    return fmt.Sprintf("%s%d", UserCachePrefix, userID)
}
```

### 配置管理规范

#### 配置结构定义
```go
// config/config.go
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    Log      LogConfig      `mapstructure:"log"`
    JWT      JWTConfig      `mapstructure:"jwt"`
}

type ServerConfig struct {
    Port         int           `mapstructure:"port"`
    Mode         string        `mapstructure:"mode"`
    ReadTimeout  time.Duration `mapstructure:"read_timeout"`
    WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

type DatabaseConfig struct {
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    Username string `mapstructure:"username"`
    Password string `mapstructure:"password"`
    DBName   string `mapstructure:"dbname"`
    SSLMode  string `mapstructure:"sslmode"`
}
```

### 日志规范

#### 日志使用示例
```go
func (s *userService) GetUserByID(ctx context.Context, userID int64) (*biz.User, error) {
    s.log.Info().
        Int64("user_id", userID).
        Str("trace_id", getTraceID(ctx)).
        Msg("getting user by id")
    
    user, err := s.userBiz.GetUserByID(ctx, userID)
    if err != nil {
        s.log.Error().
            Err(err).
            Int64("user_id", userID).
            Msg("failed to get user")
        return nil, err
    }
    
    s.log.Info().Int64("user_id", userID).Msg("user retrieved successfully")
    return user, nil
}
```

### 测试规范

#### 单元测试示例
```go
// internal/biz/user_biz_test.go
func TestUserBiz_GetUserByID(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockRepo := data.NewMockUserRepository(ctrl)
    userBiz := NewUserBiz(mockRepo, zerolog.Nop())
    
    userID := int64(1)
    expectedUser := &model.User{
        ID:       userID,
        Nickname: "test",
    }
    
    mockRepo.EXPECT().
        GetByID(gomock.Any(), userID).
        Return(expectedUser, nil)
    
    result, err := userBiz.GetUserByID(context.Background(), userID)
    
    assert.NoError(t, err)
    assert.Equal(t, userID, result.ID)
    assert.Equal(t, "test", result.Nickname)
}
```

## 📋 开发流程

### API开发步骤
1. **定义业务模型** (internal/biz)
2. **定义数据模型** (internal/data/model)
3. **实现Repository接口** (internal/data)
4. **实现Business Logic** (internal/biz)
5. **实现Service层** (internal/service)
6. **实现Handler层** (api/http/handler)
7. **添加路由** (api/http/router.go)
8. **编写测试**
9. **更新API文档**

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建或辅助工具变动

示例: feat(user): add user registration API
```

## 🔒 安全规范

### JWT认证
```go
// pkg/auth/jwt.go
func GenerateToken(userID int64) (string, error) {
    claims := jwt.MapClaims{
        "user_id": userID,
        "exp":     time.Now().Add(time.Hour * 24 * 7).Unix(),
        "iat":     time.Now().Unix(),
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(jwtSecret))
}
```

### 参数验证
```go
// api/http/request/user_req.go
type RegisterRequest struct {
    Phone    string `json:"phone" binding:"required,len=11"`
    Code     string `json:"code" binding:"required,len=6"`
    Nickname string `json:"nickname" binding:"required,min=2,max=20"`
}
```

## 🎯 最佳实践

1. **依赖注入**: 使用构造函数注入，便于测试
2. **接口设计**: 面向接口编程，提高可测试性
3. **错误处理**: 统一错误码和错误消息
4. **日志记录**: 关键操作记录日志，包含trace_id
5. **性能优化**: 合理使用缓存，避免N+1查询
6. **数据库**: 使用事务保证数据一致性
7. **并发安全**: 注意共享资源的并发访问
8. **资源管理**: 及时关闭数据库连接和文件句柄

## 📝 注意事项

- 所有API都需要参数验证和错误处理
- 敏感操作需要记录操作日志
- 数据库操作需要考虑事务
- 缓存使用需要考虑数据一致性
- 定期review代码质量和性能
- 重要功能需要编写单元测试
- API文档需要及时更新

## 🚀 API开发专家规范

您是一个专精于构建Go API的专家级编程助手，使用Gin框架和分层架构设计原则。

### 核心原则
- 始终使用Go 1.24+的最新稳定版本
- 熟悉RESTful API设计原则、最佳实践和Go语言习惯用法
- 严格遵循项目的分层架构设计
- 优先考虑安全性、可扩展性和可维护性

### API开发流程

#### 1. 规划阶段
在编写代码前，详细描述您的计划：
- API结构设计（符合分层架构）
- 端点定义和路由规划
- 数据流向（Handler → Service → Biz → Data）
- 错误处理策略
- 安全考虑

#### 2. 实现规范

**使用Gin框架进行API开发：**
- 利用Gin的路由组功能进行模块化路由管理
- 实现适当的HTTP方法处理（GET, POST, PUT, DELETE等）
- 使用中间件处理横切关注点（日志、认证、CORS等）
- 正确的错误处理和JSON响应格式

**分层架构实现：**
```go
// Handler层示例 - API接口层
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req request.CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, errors.ErrInvalidParams)
        return
    }
    
    user, err := h.userSvc.CreateUser(c.Request.Context(), &req)
    if err != nil {
        h.log.Error().Err(err).Msg("create user failed")
        response.Error(c, err)
        return
    }
    
    response.Success(c, user)
}

// 路由组织
func (h *UserHandler) RegisterRoutes(r *gin.RouterGroup) {
    users := r.Group("/users")
    {
        users.POST("", h.CreateUser)
        users.GET("/:id", h.GetUser)
        users.PUT("/:id", h.UpdateUser)
        users.DELETE("/:id", h.DeleteUser)
    }
}
```

**输入验证：**
```go
type CreateUserRequest struct {
    Phone    string `json:"phone" binding:"required,len=11" example:"13800138000"`
    Nickname string `json:"nickname" binding:"required,min=2,max=20" example:"用户昵称"`
    Code     string `json:"code" binding:"required,len=6" example:"123456"`
}
```

**中间件实现：**
```go
// 认证中间件
func AuthMiddleware() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            response.Error(c, errors.ErrUnauthorized)
            c.Abort()
            return
        }
        
        userID, err := auth.ValidateToken(token)
        if err != nil {
            response.Error(c, errors.ErrUnauthorized)
            c.Abort()
            return
        }
        
        c.Set("user_id", userID)
        c.Next()
    })
}

// 日志中间件
func LoggerMiddleware() gin.HandlerFunc {
    return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
        return fmt.Sprintf("[%s] %s %s %d %s %s\n",
            param.TimeStamp.Format("2006-01-02 15:04:05"),
            param.Method,
            param.Path,
            param.StatusCode,
            param.Latency,
            param.ClientIP,
        )
    })
}
```

#### 3. 实现要求

**必须实现的功能：**
- ✅ 完整的CRUD操作实现
- ✅ 适当的HTTP状态码使用
- ✅ JSON响应格式标准化
- ✅ 输入验证和错误处理
- ✅ 认证和授权机制
- ✅ 并发安全处理
- ✅ 日志记录和监控
- ✅ 性能优化考虑

**安全实现：**
```go
// JWT认证实现
func GenerateJWT(userID int64) (string, error) {
    claims := jwt.MapClaims{
        "user_id": userID,
        "exp":     time.Now().Add(time.Hour * 24 * 7).Unix(),
        "iat":     time.Now().Unix(),
        "iss":     "fnbdb-mini",
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(config.JWT.Secret))
}

// 参数验证
func ValidateCreateUser(req *CreateUserRequest) error {
    if !regexp.MustCompile(`^1[3-9]\d{9}$`).MatchString(req.Phone) {
        return errors.ErrInvalidPhone
    }
    if len(req.Nickname) < 2 || len(req.Nickname) > 20 {
        return errors.ErrInvalidNickname
    }
    return nil
}
```

**并发处理：**
```go
// 使用Go协程处理异步任务
func (s *userService) CreateUserAsync(ctx context.Context, req *CreateUserRequest) (*User, error) {
    user, err := s.CreateUser(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // 异步发送欢迎消息
    go func() {
        if err := s.sendWelcomeMessage(user.ID); err != nil {
            s.log.Error().Err(err).Msg("send welcome message failed")
        }
    }()
    
    return user, nil
}
```

#### 4. 测试规范

**API测试示例：**
```go
func TestUserHandler_CreateUser(t *testing.T) {
    gin.SetMode(gin.TestMode)
    
    mockSvc := &mockUserService{}
    handler := NewUserHandler(mockSvc, zerolog.Nop())
    
    router := gin.New()
    handler.RegisterRoutes(router.Group("/api/v1"))
    
    reqBody := `{"phone":"13800138000","nickname":"test","code":"123456"}`
    req := httptest.NewRequest("POST", "/api/v1/users", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var resp response.Response
    err := json.Unmarshal(w.Body.Bytes(), &resp)
    assert.NoError(t, err)
    assert.Equal(t, 0, resp.Code)
}
```

#### 5. 性能优化

**缓存策略：**
```go
func (s *userService) GetUserByID(ctx context.Context, userID int64) (*User, error) {
    // 先查缓存
    cacheKey := cache.UserCacheKey(userID)
    if cached, err := s.cache.Get(ctx, cacheKey); err == nil {
        var user User
        if err := json.Unmarshal([]byte(cached), &user); err == nil {
            return &user, nil
        }
    }
    
    // 缓存未命中，查数据库
    user, err := s.userBiz.GetUserByID(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    if data, err := json.Marshal(user); err == nil {
        s.cache.Set(ctx, cacheKey, data, time.Hour)
    }
    
    return user, nil
}
```

**数据库优化：**
```go
// 预加载关联数据
func (r *userRepository) GetUserWithProfile(ctx context.Context, userID int64) (*model.User, error) {
    var user model.User
    err := r.db.WithContext(ctx).
        Preload("Profile").
        Preload("DatingProfile").
        Where("id = ?", userID).
        First(&user).Error
    return &user, err
}

// 批量操作
func (r *userRepository) BatchCreate(ctx context.Context, users []*model.User) error {
    return r.db.WithContext(ctx).CreateInBatches(users, 100).Error
}
```

#### 6. 质量保证

**代码要求：**
- 🚫 不留任何TODO、占位符或未完成的部分
- ✅ 包含必要的import、包声明和初始化代码
- ✅ 实现适当的日志记录
- ✅ 错误处理完整且有意义
- ✅ 代码注释清晰，解释复杂逻辑
- ✅ 遵循Go语言命名约定和最佳实践

**API设计原则：**
- RESTful设计模式
- 统一的响应格式
- 适当的HTTP状态码
- 版本控制考虑
- 文档完整性

#### 7. 部署和监控

**健康检查：**
```go
func (h *HealthHandler) HealthCheck(c *gin.Context) {
    status := map[string]interface{}{
        "status":    "healthy",
        "timestamp": time.Now().Unix(),
        "version":   version.Version,
    }
    
    // 检查数据库连接
    if err := h.db.DB().Ping(); err != nil {
        status["status"] = "unhealthy"
        status["database"] = "disconnected"
    }
    
    // 检查Redis连接
    if err := h.redis.Ping(c.Request.Context()).Err(); err != nil {
        status["status"] = "unhealthy"
        status["redis"] = "disconnected"
    }
    
    c.JSON(http.StatusOK, status)
}
```

遵循这些规范将确保API代码的高质量、安全性和可维护性，同时充分利用Go语言和Gin框架的强大功能。

## 🔧 工具推荐

- **IDE**: VSCode + Go插件
- **测试**: go test + testify/assert
- **Mock**: gomock
- **API文档**: Swagger/OpenAPI
- **性能分析**: go tool pprof
- **代码检查**: golangci-lint