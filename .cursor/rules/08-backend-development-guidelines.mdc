
# Backend Development Guidelines

## 1. Overview

This document outlines the development rules and best practices for the backend of this project. The backend is built with Golang, using the Gin framework, GORM for database interaction, and PostgreSQL as the database. All subsequent development by the AI should strictly adhere to these guidelines.

## 2. Directory Structure

The backend follows a standard Go project layout:

- **/cmd/server/main.go**: Main application entry point. Responsible for initializing dependencies (config, logger, database, etc.) and starting the Gin server.
- **/config**: Handles application configuration. It should load settings from a file (e.g., `config.yaml`) and environment variables.
- **/internal**: Contains all the core application logic. This code is not meant to be imported by other projects.
    - **/api**: The API layer (Controllers/Handlers).
        - **/v1**: API version 1. Each file should correspond to a resource (e.g., `user.go`, `post.go`). Its sole responsibility is to parse requests, call the appropriate service, and return a JSON response. No business logic here.
    - **/model**: Defines GORM models, which represent database tables. Struct tags for JSON and database columns should be clearly defined.
    - **/repository**: The data access layer. It abstracts the database operations. Each model should have a corresponding repository (e.g., `user_repository.go`). It should be called only by the service layer.
    - **/service**: The business logic layer. It orchestrates the application's functionality, calling repositories to interact with the database and performing business-specific tasks.
    - **/worker**: Background workers for asynchronous tasks (e.g., processing messages from RabbitMQ).
- **/pkg**: Publicly available packages that can be used by other projects.
    - **/database**: Database connection and GORM initialization.
    - **/redis**: Redis client initialization and helper functions.
    - **/mq**: RabbitMQ connection and channel setup.
    - **/payment**: Wrappers for the WeChat payment SDK.
    - **/scheduler**: Cron job scheduling and management.
    - **/utils**: Generic utility functions (e.g., for password hashing, token generation).
- **/go.mod, go.sum**: Go module dependency management.

## 3. API Design

- **Framework**: Use [Gin](https://gin-gonic.com/).
- **RESTful Principles**: Adhere to RESTful API design principles. Use appropriate HTTP verbs (`GET`, `POST`, `PUT`, `DELETE`) for actions.
- **Versioning**: All API routes must be versioned (e.g., `/api/v1/...`).
- **Request/Response**:
    - All responses should be in JSON format.
    - Create standardized request and response structs within the `api` package for data validation and clarity.
    - Successful responses should have a consistent structure:
      ```json
      {
        "code": 0,
        "message": "success",
        "data": { ... }
      }
      ```
    - Error responses should also have a consistent structure:
      ```json
      {
        "code": 40001,
        "message": "Invalid input parameters",
        "data": null
      }
      ```
- **Routing**: Routes should be defined in a dedicated router file within the `api` package, which is then called from `main.go`.

## 4. Database

- **ORM**: Use [GORM](https://gorm.io/).
- **Repository Pattern**: All database interactions must go through the repository layer. Services should not call GORM directly. This isolates data access logic and makes it easier to mock for testing.
- **Migrations**: Use a migration tool like [gorm-migrate](https://github.com/go-gorm/gorm-migrate) or handle schema changes with care.
- **Transactions**: Use database transactions for operations that involve multiple write operations to ensure data integrity.

## 5. Configuration

- Use a library like [Viper](https://github.com/spf13/viper) to manage configuration.
- Support loading from both a configuration file (e.g., `config.yaml`) and environment variables. Environment variables should override file settings.
- Never hardcode configuration values in the code.

## 6. Error Handling

- Implement a centralized error handling middleware in Gin. This middleware will catch panics and errors returned from handlers and format them into the standard JSON error response.
- Services should return custom error types or error variables (e.g., `ErrUserNotFound`) to the API layer, which then maps them to appropriate HTTP status codes.

## 7. Logging

- Use a structured logging library like [Logrus](https://github.com/sirupsen/logrus) or [Zap](https://github.com/uber-go/zap).
- Logs should be in JSON format for easier parsing by log management systems.
- Implement a logging middleware to log request details (method, path, status, latency).
- Add contextual information to logs (e.g., `request_id`, `user_id`).

## 8. Code Style and Conventions

- **Formatting**: All code must be formatted with `gofmt`.
- **Naming**:
    - Follow standard Go naming conventions (e.g., `camelCase` for local variables, `PascalCase` for exported identifiers).
    - Package names should be short, concise, and in `lowercase`.
    - Receiver variables for methods should be short and consistent (e.g., `u` for `User`, `ur` for `UserRepository`).
- **Comments**: Write comments to explain *why* something is done, not *what* is being done. The code should be self-explanatory for the "what".

## 9. Security

- **Authentication**: Use JSON Web Tokens (JWT) for stateless authentication. Implement a Gin middleware to protect routes.
- **Authorization**: Implement role-based access control (RBAC) or other authorization logic within the service layer.
- **Input Validation**: Use a library like [go-playground/validator](https://github.com/go-playground/validator) to validate all incoming request data. Bind requests to structs with validation tags.
- **Secrets**: Never commit secrets or credentials to the repository. Use environment variables or a secret management system.

## 10. Testing

- **Unit Tests**: All services and utility functions should have comprehensive unit tests. Use the standard `testing` package and mocks for external dependencies (like repositories).
- **Integration Tests**: Write integration tests for API endpoints to ensure the different components work together correctly. These tests can use a separate test database.
- **Test Location**: Test files should be in the same package as the code they are testing, with the `_test.go` suffix.
