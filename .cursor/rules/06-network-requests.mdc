---
description: 
globs: 
alwaysApply: true
---
# 网络请求

本项目使用[@uni-helper/uni-network](mdc:https:/github.com/uni-helper/uni-network)库进行网络请求，该库是基于uni-app的网络请求工具。

## 请求封装

网络请求封装在[utils/http.ts](mdc:front/src/utils/http.ts)文件中。

### 实例配置

创建了自定义网络请求实例，配置了基础URL和超时时间：

```ts
const instance = un.create({
  baseUrl,
  timeout: 10000,
})
```

### 拦截器

#### 请求拦截器

- 添加平台标识
- 自动添加授权token（从userStore获取）

#### 响应拦截器

- 处理业务状态码，只返回data部分
- 处理401未授权情况，自动清除用户信息并跳转登录页
- 处理其他错误，显示错误提示

### 请求方法

封装了常用的HTTP方法：

- `http.get()`: GET请求
- `http.post()`: POST请求
- `http.put()`: PUT请求
- `http.delete()`: DELETE请求
- `http.upload()`: 文件上传
- `http.download()`: 文件下载

## 使用示例

```vue
<script setup lang="ts">
import { http } from '@/utils/http'

// 定义接口响应类型
interface UserInfo {
  id: number
  username: string
  avatar: string
}

// GET请求
async function fetchUserInfo() {
  try {
    const res = await http.get<ResponseData<UserInfo>>('/user/info')
    console.log(res.data)
  } catch (error) {
    console.error(error)
  }
}

// POST请求
async function login(username: string, password: string) {
  try {
    const res = await http.post<ResponseData<{token: string}>>('/login', {
      username,
      password
    })
    return res.data
  } catch (error) {
    console.error(error)
    return null
  }
}
</script>
```
